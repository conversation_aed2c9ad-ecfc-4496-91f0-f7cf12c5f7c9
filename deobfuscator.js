#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

const ControlFlowDeobfuscator = require('./control-flow-deobfuscator');
const StringDeobfuscator = require('./string-deobfuscator');

class JSDeobfuscator {
    constructor(sourceCode) {
        this.sourceCode = sourceCode;
        this.ast = null;
        this.stringMap = new Map();
        this.variableMap = new Map();
        this.deadCodeNodes = new Set();
        this.statistics = {
            originalSize: sourceCode.length,
            functionsFound: 0,
            variablesRenamed: 0,
            stringsDecrypted: 0,
            deadCodeRemoved: 0
        };
    }

    // 解析代码为AST
    parseCode() {
        try {
            this.ast = parse(this.sourceCode, {
                sourceType: 'script',
                allowImportExportEverywhere: true,
                allowReturnOutsideFunction: true,
                plugins: ['jsx', 'typescript']
            });
            console.log('✅ AST解析成功');
            return true;
        } catch (error) {
            console.error('❌ AST解析失败:', error.message);
            return false;
        }
    }

    // 分析代码结构
    analyzeStructure() {
        if (!this.ast) return;

        console.log('\n🔍 开始代码结构分析...');
        
        traverse(this.ast, {
            FunctionDeclaration: (path) => {
                this.statistics.functionsFound++;
                const name = path.node.id?.name || 'anonymous';
                console.log(`  📋 发现函数: ${name}`);
            },
            
            VariableDeclarator: (path) => {
                if (t.isIdentifier(path.node.id)) {
                    const name = path.node.id.name;
                    // 检测混淆的变量名模式
                    if (this.isObfuscatedName(name)) {
                        console.log(`  🔤 混淆变量: ${name}`);
                    }
                }
            },

            StringLiteral: (path) => {
                const value = path.node.value;
                // 检测可能的加密字符串
                if (this.isPossiblyEncrypted(value)) {
                    console.log(`  🔐 可能的加密字符串: ${value.substring(0, 50)}...`);
                }
            },

            CallExpression: (path) => {
                // 检测控制流扁平化模式
                if (this.isControlFlowFlattening(path)) {
                    console.log(`  🌀 检测到控制流扁平化`);
                }
            }
        });

        console.log(`\n📊 结构分析完成:`);
        console.log(`  - 函数数量: ${this.statistics.functionsFound}`);
    }

    // 检测是否为混淆的变量名
    isObfuscatedName(name) {
        // 检测短变量名、随机字符等模式
        return (
            name.length <= 2 || 
            /^[a-zA-Z]$/.test(name) ||
            /^_0x[a-f0-9]+$/i.test(name) ||
            /^[a-zA-Z]{1,3}\d+$/.test(name)
        );
    }

    // 检测可能的加密字符串
    isPossiblyEncrypted(str) {
        // 检测Base64、十六进制等模式
        return (
            str.length > 20 &&
            (/^[A-Za-z0-9+/]+=*$/.test(str) || // Base64
             /^[0-9a-fA-F]+$/.test(str) ||     // Hex
             /^\\x[0-9a-fA-F]{2}/.test(str))   // Hex escape
        );
    }

    // 检测控制流扁平化
    isControlFlowFlattening(path) {
        // 检测switch-case结构和while循环的组合
        if (t.isCallExpression(path.node)) {
            const callee = path.node.callee;
            if (t.isMemberExpression(callee)) {
                const property = callee.property;
                if (t.isIdentifier(property) && 
                    (property.name === 'split' || property.name === 'charAt')) {
                    return true;
                }
            }
        }
        return false;
    }

    // 变量名去混淆
    deobfuscateVariables() {
        console.log('\n🔤 开始变量名去混淆...');
        
        const variableCounter = new Map();
        
        traverse(this.ast, {
            Identifier: (path) => {
                const name = path.node.name;
                if (this.isObfuscatedName(name) && !this.variableMap.has(name)) {
                    // 根据上下文生成有意义的变量名
                    const newName = this.generateMeaningfulName(path, variableCounter);
                    this.variableMap.set(name, newName);
                    this.statistics.variablesRenamed++;
                }
            }
        });

        // 应用变量名替换
        traverse(this.ast, {
            Identifier: (path) => {
                const name = path.node.name;
                if (this.variableMap.has(name)) {
                    path.node.name = this.variableMap.get(name);
                }
            }
        });

        console.log(`✅ 变量名去混淆完成，重命名了 ${this.statistics.variablesRenamed} 个变量`);
    }

    // 生成有意义的变量名
    generateMeaningfulName(path, counter) {
        const parent = path.parent;
        let prefix = 'var';

        // 根据上下文确定前缀
        if (t.isFunctionDeclaration(parent) || t.isFunctionExpression(parent)) {
            prefix = 'func';
        } else if (t.isVariableDeclarator(parent)) {
            const init = parent.init;
            if (t.isStringLiteral(init)) {
                prefix = 'str';
            } else if (t.isNumericLiteral(init)) {
                prefix = 'num';
            } else if (t.isArrayExpression(init)) {
                prefix = 'arr';
            } else if (t.isObjectExpression(init)) {
                prefix = 'obj';
            }
        } else if (t.isCallExpression(parent)) {
            prefix = 'result';
        }

        // 生成唯一名称
        const count = counter.get(prefix) || 0;
        counter.set(prefix, count + 1);
        return `${prefix}${count || ''}`;
    }

    // 字符串解密
    decryptStrings() {
        console.log('\n🔐 开始字符串解密...');
        
        traverse(this.ast, {
            StringLiteral: (path) => {
                const value = path.node.value;
                if (this.isPossiblyEncrypted(value)) {
                    const decrypted = this.attemptDecryption(value);
                    if (decrypted && decrypted !== value) {
                        path.node.value = decrypted;
                        this.statistics.stringsDecrypted++;
                        console.log(`  🔓 解密: ${value.substring(0, 20)}... -> ${decrypted.substring(0, 20)}...`);
                    }
                }
            }
        });

        console.log(`✅ 字符串解密完成，解密了 ${this.statistics.stringsDecrypted} 个字符串`);
    }

    // 尝试解密字符串
    attemptDecryption(str) {
        try {
            // 尝试Base64解码
            if (/^[A-Za-z0-9+/]+=*$/.test(str) && str.length % 4 === 0) {
                const decoded = Buffer.from(str, 'base64').toString('utf8');
                if (this.isPrintableString(decoded)) {
                    return decoded;
                }
            }

            // 尝试十六进制解码
            if (/^[0-9a-fA-F]+$/.test(str) && str.length % 2 === 0) {
                const decoded = Buffer.from(str, 'hex').toString('utf8');
                if (this.isPrintableString(decoded)) {
                    return decoded;
                }
            }

            // 尝试URL解码
            if (str.includes('%')) {
                try {
                    const decoded = decodeURIComponent(str);
                    if (decoded !== str) {
                        return decoded;
                    }
                } catch (e) {
                    // 忽略解码错误
                }
            }
        } catch (error) {
            // 忽略解密错误
        }
        
        return str;
    }

    // 检查是否为可打印字符串
    isPrintableString(str) {
        return /^[\x20-\x7E\s]*$/.test(str) && str.length > 0;
    }

    // 死代码消除
    removeDeadCode() {
        console.log('\n🗑️  开始死代码消除...');
        
        traverse(this.ast, {
            IfStatement: (path) => {
                const test = path.node.test;
                if (t.isBooleanLiteral(test)) {
                    if (test.value === false) {
                        // 删除永远不会执行的if分支
                        path.remove();
                        this.statistics.deadCodeRemoved++;
                        console.log('  🗑️  移除永假if语句');
                    } else if (test.value === true && !path.node.alternate) {
                        // 替换永真if语句为其body
                        path.replaceWithMultiple(path.node.consequent.body || [path.node.consequent]);
                        this.statistics.deadCodeRemoved++;
                        console.log('  🗑️  简化永真if语句');
                    }
                }
            },

            ConditionalExpression: (path) => {
                const test = path.node.test;
                if (t.isBooleanLiteral(test)) {
                    const replacement = test.value ? path.node.consequent : path.node.alternate;
                    path.replaceWith(replacement);
                    this.statistics.deadCodeRemoved++;
                    console.log('  🗑️  简化三元表达式');
                }
            },

            UnaryExpression: (path) => {
                // 简化!!expression为Boolean(expression)
                if (path.node.operator === '!' && 
                    t.isUnaryExpression(path.node.argument) && 
                    path.node.argument.operator === '!') {
                    const innerArg = path.node.argument.argument;
                    path.replaceWith(t.callExpression(t.identifier('Boolean'), [innerArg]));
                    this.statistics.deadCodeRemoved++;
                    console.log('  🗑️  简化双重否定');
                }
            }
        });

        console.log(`✅ 死代码消除完成，移除了 ${this.statistics.deadCodeRemoved} 处死代码`);
    }

    // 执行完整的去混淆流程
    deobfuscate() {
        console.log('🚀 开始JavaScript去混淆流程...\n');

        if (!this.parseCode()) {
            return null;
        }

        this.analyzeStructure();

        // 使用专门的控制流去混淆器
        const controlFlowDeobfuscator = new ControlFlowDeobfuscator(this.ast);
        const controlFlowResult = controlFlowDeobfuscator.process();

        // 使用专门的字符串去混淆器
        const stringDeobfuscator = new StringDeobfuscator(this.ast);
        const stringResult = stringDeobfuscator.process();

        // 更新统计信息
        this.statistics.stringsDecrypted = stringResult.decryptedCount;
        this.statistics.controlFlowFixed = controlFlowResult.deobfuscatedFunctions;

        this.deobfuscateVariables();
        this.removeDeadCode();

        return this.generateCleanCode();
    }

    // 生成清理后的代码
    generateCleanCode() {
        console.log('\n📝 生成清理后的代码...');
        
        const result = generate(this.ast, {
            retainLines: false,
            compact: false,
            minified: false,
            comments: true
        });

        this.statistics.finalSize = result.code.length;
        this.statistics.compressionRatio = ((this.statistics.originalSize - this.statistics.finalSize) / this.statistics.originalSize * 100).toFixed(2);

        console.log('\n📊 去混淆统计:');
        console.log(`  原始大小: ${this.statistics.originalSize} 字符`);
        console.log(`  最终大小: ${this.statistics.finalSize} 字符`);
        console.log(`  压缩比例: ${this.statistics.compressionRatio}%`);
        console.log(`  函数数量: ${this.statistics.functionsFound}`);
        console.log(`  变量重命名: ${this.statistics.variablesRenamed}`);
        console.log(`  字符串解密: ${this.statistics.stringsDecrypted}`);
        console.log(`  死代码移除: ${this.statistics.deadCodeRemoved}`);

        return result.code;
    }
}

module.exports = JSDeobfuscator;

// 如果直接运行此文件
if (require.main === module) {
    const inputFile = process.argv[2] || 'proxy.js';
    const outputFile = process.argv[3] || 'deobfuscated.js';

    if (!fs.existsSync(inputFile)) {
        console.error(`❌ 输入文件不存在: ${inputFile}`);
        process.exit(1);
    }

    const sourceCode = fs.readFileSync(inputFile, 'utf8');
    const deobfuscator = new JSDeobfuscator(sourceCode);
    const cleanCode = deobfuscator.deobfuscate();

    if (cleanCode) {
        fs.writeFileSync(outputFile, cleanCode);
        console.log(`\n✅ 去混淆完成！结果已保存到: ${outputFile}`);
    } else {
        console.error('❌ 去混淆失败');
        process.exit(1);
    }
}
