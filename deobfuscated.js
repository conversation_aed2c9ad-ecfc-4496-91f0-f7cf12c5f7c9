(function () {
  'use strict';

  function _defineProperty(func, func1, func2) {
    if ((func1 = _toPropertyKey(func1)) in func) {
      Object.defineProperty(func, func1, {
        value: func2,
        enumerable: true,
        configurable: true,
        writable: true
      });
    } else {
      func[func1] = func2;
    }
    return func;
  }
  function ownKeys(func, func1) {
    var func2 = Object.keys(func);
    if (Object.getOwnPropertySymbols) {
      var var = Object.getOwnPropertySymbols(func);
      if (func1) {
        var = var.filter(function (func1) {
          return Object.getOwnPropertyDescriptor(func, func1).enumerable;
        });
      }
      func2.push.apply(func2, var);
    }
    return func2;
  }
  function _objectSpread2(func) {
    for (var func1 = 1; func1 < arguments.length; func1++) {
      var func2 = arguments[func1] ?? {};
      if (func1 % 2) {
        ownKeys(Object(func2), true).forEach(function (func1) {
          _defineProperty(func, func1, func2[func1]);
        });
      } else if (Object.getOwnPropertyDescriptors) {
        Object.defineProperties(func, Object.getOwnPropertyDescriptors(func2));
      } else {
        ownKeys(Object(func2)).forEach(function (func1) {
          Object.defineProperty(func, func1, Object.getOwnPropertyDescriptor(func2, func1));
        });
      }
    }
    return func;
  }
  function _objectWithoutProperties(func, func2) {
    if (func == null) {
      return {};
    }
    var var;
    var func1;
    var var1 = _objectWithoutPropertiesLoose(func, func2);
    if (Object.getOwnPropertySymbols) {
      var var2 = Object.getOwnPropertySymbols(func);
      for (func1 = 0; func1 < var2.length; func1++) {
        var = var2[func1];
        if (func2.indexOf(var) === -1 && {}.propertyIsEnumerable.call(func, var)) {
          var1[var] = func[var];
        }
      }
    }
    return var1;
  }
  function _objectWithoutPropertiesLoose(func1, func) {
    if (func1 == null) {
      return {};
    }
    var func2 = {};
    for (var var2 in func1) {
      if ({}.hasOwnProperty.call(func1, var2)) {
        if (func.indexOf(var2) !== -1) {
          continue;
        }
        func2[var2] = func1[var2];
      }
    }
    return func2;
  }
  function _toPrimitive(func2, func1) {
    if (typeof func2 != "object" || !func2) {
      return func2;
    }
    var func = func2[Symbol.toPrimitive];
    if (func !== undefined) {
      var var1 = func.call(func2, func1);
      if (typeof var1 != "object") {
        return var1;
      }
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return (func1 === "string" ? String : Number)(func2);
  }
  function _toPropertyKey(func2) {
    var var1 = _toPrimitive(func2, "string");
    if (typeof var1 == "symbol") {
      return var1;
    } else {
      return var1 + "";
    }
  }
  var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
  function getDefaultExportFromCjs(func3) {
    if (func3 && func3.__esModule && Object.prototype.hasOwnProperty.call(func3, "default")) {
      return func3.default;
    } else {
      return func3;
    }
  }
  function getAugmentedNamespace(var2) {
    if (var2.__esModule) {
      return var2;
    }
    var var3 = var2.default;
    if (typeof var3 == "shapgvba") {
      var var4 = function var4() {
        if (this instanceof var4) {
          return Reflect.construct(var3, arguments, this.constructor);
        }
        return var3.apply(this, arguments);
      };
      var4.prototype = var3.prototype;
    } else {
      var4 = {};
    }
    Object.defineProperty(var4, "__esModule", {
      value: true
    });
    Object.keys(var2).forEach(function (func4) {
      var var5 = Object.getOwnPropertyDescriptor(var2, func4);
      Object.defineProperty(var4, func4, var5.get ? var5 : {
        enumerable: true,
        get: function () {
          return var2[func4];
        }
      });
    });
    return var4;
  }
  var jsencrypt = {
    exports: {}
  };
  (function (module, exports) {
    (function (global, factory) {
      factory(exports);
    })(commonjsGlobal, function (exports) {
      var BI_RM = "0123456789abcdefghijklmnopqrstuvwxyz";
      function int2char(var2) {
        return BI_RM.charAt(var2);
      }
      //#region BIT_OPERATIONS
      // (public) this & a
      function op_and(func3, func5) {
        return func3 & func5;
      }
      // (public) this | a
      function op_or(func3, func5) {
        return func3 | func5;
      }
      // (public) this ^ a
      function op_xor(func3, func5) {
        return func3 ^ func5;
      }
      // (public) this & ~a
      function op_andnot(func3, func5) {
        return func3 & ~func5;
      }
      // return index of lowest 1-bit in x, x < 2^31
      function lbit(func3) {
        if (func3 == 0) {
          return -1;
        }
        var func1 = 0;
        if ((func3 & 65535) == 0) {
          func3 >>= 16;
          func1 += 16;
        }
        if ((func3 & 255) == 0) {
          func3 >>= 8;
          func1 += 8;
        }
        if ((func3 & 15) == 0) {
          func3 >>= 4;
          func1 += 4;
        }
        if ((func3 & 3) == 0) {
          func3 >>= 2;
          func1 += 2;
        }
        if ((func3 & 1) == 0) {
          ++func1;
        }
        return func1;
      }
      // return number of 1 bits in x
      function cbit(func3) {
        var func1 = 0;
        while (func3 != 0) {
          func3 &= func3 - 1;
          ++func1;
        }
        return func1;
      }
      //#endregion BIT_OPERATIONS

      var b64map = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
      var b64pad = "=";
      function hex2b64(func6) {
        var var1;
        var var6;
        var ret = "";
        for (var1 = 0; var1 + 3 <= func6.length; var1 += 3) {
          var6 = parseInt(func6.substring(var1, var1 + 3), 16);
          ret += b64map.charAt(var6 >> 6) + b64map.charAt(var6 & 63);
        }
        if (var1 + 1 == func6.length) {
          var6 = parseInt(func6.substring(var1, var1 + 1), 16);
          ret += b64map.charAt(var6 << 2);
        } else if (var1 + 2 == func6.length) {
          var6 = parseInt(func6.substring(var1, var1 + 2), 16);
          ret += b64map.charAt(var6 >> 2) + b64map.charAt((var6 & 3) << 4);
        }
        while ((ret.length & 3) > 0) {
          ret += b64pad;
        }
        return ret;
      }
      // convert a base64 string to hex
      function b64tohex(func7) {
        var ret = "";
        var var1;
        var func4 = 0; // b64 state, 0-3
        var slop = 0;
        for (var1 = 0; var1 < func7.length; ++var1) {
          if (func7.charAt(var1) == b64pad) {
            break;
          }
          var var7 = b64map.indexOf(func7.charAt(var1));
          if (var7 < 0) {
            continue;
          }
          if (func4 == 0) {
            ret += int2char(var7 >> 2);
            slop = var7 & 3;
            func4 = 1;
          } else if (func4 == 1) {
            ret += int2char(slop << 2 | var7 >> 4);
            slop = var7 & 15;
            func4 = 2;
          } else if (func4 == 2) {
            ret += int2char(slop);
            ret += int2char(var7 >> 2);
            slop = var7 & 3;
            func4 = 3;
          } else {
            ret += int2char(slop << 2 | var7 >> 4);
            ret += int2char(var7 & 15);
            func4 = 0;
          }
        }
        if (func4 == 1) {
          ret += int2char(slop << 2);
        }
        return ret;
      }

      /*! *****************************************************************************
         Copyright (c) Microsoft Corporation. All rights reserved.
         Licensed under the Apache License, Version 2.0 (the "License"); you may not use
         this file except in compliance with the License. You may obtain a copy of the
         License at http://www.apache.org/licenses/LICENSE-2.0
         	THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
         KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
         WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
         MERCHANTABLITY OR NON-INFRINGEMENT.
         	See the Apache Version 2.0 License for specific language governing permissions
         and limitations under the License.
         ***************************************************************************** */
      /* global Reflect, Promise */
      function extendStatics(var5, func8) {
        extendStatics = Object.setPrototypeOf || {
          __proto__: []
        } instanceof Array && function (var5, func8) {
          var5.__proto__ = func8;
        } || function (var5, func8) {
          for (var var8 in func8) {
            if (func8.hasOwnProperty(var8)) {
              var5[var8] = func8[var8];
            }
          }
        };
        return extendStatics(var5, func8);
      }
      function __extends(var5, func8) {
        extendStatics(var5, func8);
        function func9() {
          this.constructor = var5;
        }
        var5.prototype = func8 === null ? Object.create(func8) : (func9.prototype = func8.prototype, new func9());
      }

      // Hex JavaScript decoder
      // Copyright (c) 2008-2013 Lapo Luchini <<EMAIL>>
      // Permission to use, copy, modify, and/or distribute this software for any
      // purpose with or without fee is hereby granted, provided that the above
      // copyright notice and this permission notice appear in all copies.
      //
      // THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
      // WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
      // MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
      // ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
      // WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
      // ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
      // OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
      /*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */
      var decoder;
      var Hex = {
        decode: function (var4) {
          var var1;
          if (decoder === undefined) {
            var hex = "0123456789ABCDEF";
            var ignore = " \f\n\r\t\xA0\u2028\u2029";
            decoder = {};
            for (var1 = 0; var1 < 16; ++var1) {
              decoder[hex.charAt(var1)] = var1;
            }
            hex = hex.toLowerCase();
            for (var1 = 10; var1 < 16; ++var1) {
              decoder[hex.charAt(var1)] = var1;
            }
            for (var1 = 0; var1 < ignore.length; ++var1) {
              decoder[ignore.charAt(var1)] = -1;
            }
          }
          var out = [];
          var bits = 0;
          var char_count = 0;
          for (var1 = 0; var1 < var4.length; ++var1) {
            var var6 = var4.charAt(var1);
            if (var6 == "=") {
              break;
            }
            var6 = decoder[var6];
            if (var6 == -1) {
              continue;
            }
            if (var6 === undefined) {
              throw new Error("Illegal character at offset " + var1);
            }
            bits |= var6;
            if (++char_count >= 2) {
              out[out.length] = bits;
              bits = 0;
              char_count = 0;
            } else {
              bits <<= 4;
            }
          }
          if (char_count) {
            throw new Error("Hex encoding incomplete: 4 bits missing");
          }
          return out;
        }
      };

      // Base64 JavaScript decoder
      // Copyright (c) 2008-2013 Lapo Luchini <<EMAIL>>
      // Permission to use, copy, modify, and/or distribute this software for any
      // purpose with or without fee is hereby granted, provided that the above
      // copyright notice and this permission notice appear in all copies.
      //
      // THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
      // WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
      // MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
      // ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
      // WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
      // ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
      // OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
      /*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */
      var decoder$1;
      var Base64 = {
        decode: function (var4) {
          var var1;
          if (decoder$1 === undefined) {
            var str = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            var ignore = "= \f\n\r\t\xA0\u2028\u2029";
            decoder$1 = Object.create(null);
            for (var1 = 0; var1 < 64; ++var1) {
              decoder$1[str.charAt(var1)] = var1;
            }
            for (var1 = 0; var1 < ignore.length; ++var1) {
              decoder$1[ignore.charAt(var1)] = -1;
            }
          }
          var out = [];
          var bits = 0;
          var char_count = 0;
          for (var1 = 0; var1 < var4.length; ++var1) {
            var var6 = var4.charAt(var1);
            if (var6 == "=") {
              break;
            }
            var6 = decoder$1[var6];
            if (var6 == -1) {
              continue;
            }
            if (var6 === undefined) {
              throw new Error("Illegal character at offset " + var1);
            }
            bits |= var6;
            if (++char_count >= 4) {
              out[out.length] = bits >> 16;
              out[out.length] = bits >> 8 & 255;
              out[out.length] = bits & 255;
              bits = 0;
              char_count = 0;
            } else {
              bits <<= 6;
            }
          }
          switch (char_count) {
            case 1:
              throw new Error("Base64 encoding incomplete: at least 2 bits missing");
            case 2:
              out[out.length] = bits >> 10;
              break;
            case 3:
              out[out.length] = bits >> 16;
              out[out.length] = bits >> 8 & 255;
              break;
          }
          return out;
        },
        var9: /-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,
        unarmor: function (var4) {
          var var10 = Base64.var9.exec(var4);
          if (var10) {
            if (var10[1]) {
              var4 = var10[1];
            } else if (var10[2]) {
              var4 = var10[2];
            } else {
              throw new Error("RegExp out of sync");
            }
          }
          return Base64.decode(var4);
        }
      };

      // Big integer base-10 printing library
      // Copyright (c) 2014 Lapo Luchini <<EMAIL>>
      // Permission to use, copy, modify, and/or distribute this software for any
      // purpose with or without fee is hereby granted, provided that the above
      // copyright notice and this permission notice appear in all copies.
      //
      // THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
      // WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
      // MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
      // ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
      // WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
      // ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
      // OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
      /*jshint browser: true, strict: true, immed: true, latedef: true, undef: true, regexdash: false */
      var max = 10000000000000; // biggest integer that can still fit 2^53 when multiplied by 256
      var var11 = /** @class */function () {
        function var11(value) {
          this.buf = [+value || 0];
        }
        var11.prototype.mulAdd = function (var10, var6) {
          // assert(m <= 256)
          var func8 = this.buf;
          var var12 = func8.length;
          var var1;
          var func2;
          for (var1 = 0; var1 < var12; ++var1) {
            func2 = func8[var1] * var10 + var6;
            if (func2 < max) {
              var6 = 0;
            } else {
              var6 = func2 / max | 0;
              func2 -= var6 * max;
            }
            func8[var1] = func2;
          }
          if (var6 > 0) {
            func8[var1] = var6;
          }
        };
        var11.prototype.sub = function (var6) {
          // assert(m <= 256)
          var func8 = this.buf;
          var var12 = func8.length;
          var var1;
          var func2;
          for (var1 = 0; var1 < var12; ++var1) {
            func2 = func8[var1] - var6;
            if (func2 < 0) {
              func2 += max;
              var6 = 1;
            } else {
              var6 = 0;
            }
            func8[var1] = func2;
          }
          while (func8[func8.length - 1] === 0) {
            func8.pop();
          }
        };
        var11.prototype.toString = function (base) {
          if ((base || 10) != 10) {
            throw new Error("only base 10 is supported");
          }
          var func8 = this.buf;
          var func7 = func8[func8.length - 1].toString();
          for (var var1 = func8.length - 2; var1 >= 0; --var1) {
            func7 += (max + func8[var1]).toString().substring(1);
          }
          return func7;
        };
        var11.prototype.valueOf = function () {
          var func8 = this.buf;
          var var7 = 0;
          for (var var1 = func8.length - 1; var1 >= 0; --var1) {
            var7 = var7 * max + func8[var1];
          }
          return var7;
        };
        var11.prototype.simplify = function () {
          var func8 = this.buf;
          if (func8.length == 1) {
            return func8[0];
          } else {
            return this;
          }
        };
        return var11;
      }();

      // ASN.1 JavaScript decoder
      var ellipsis = "…";
      var reTimeS = /^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;
      var reTimeL = /^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;
      function stringCut(str, len) {
        if (str.length > len) {
          str = str.substring(0, len) + ellipsis;
        }
        return str;
      }
      var Stream = /** @class */function () {
        function Stream(enc, pos) {
          this.hexDigits = "0123456789ABCDEF";
          if (enc instanceof Stream) {
            this.enc = enc.enc;
            this.pos = enc.pos;
          } else {
            // enc should be an array or a binary string
            this.enc = enc;
            this.pos = pos;
          }
        }
        Stream.prototype.get = function (pos = this.pos++) {
          if (pos >= this.enc.length) {
            throw new Error("Requesting byte offset " + pos + " on a stream of length " + this.enc.length);
          }
          if (typeof this.enc === "string") {
            return this.enc.charCodeAt(pos);
          } else {
            return this.enc[pos];
          }
        };
        Stream.prototype.hexByte = function (func8) {
          return this.hexDigits.charAt(func8 >> 4 & 15) + this.hexDigits.charAt(func8 & 15);
        };
        Stream.prototype.hexDump = function (start, end, raw) {
          var func7 = "";
          for (var var1 = start; var1 < end; ++var1) {
            func7 += this.hexByte(this.get(var1));
            if (raw !== true) {
              switch (var1 & 15) {
                case 7:
                  func7 += "  ";
                  break;
                case 15:
                  func7 += "\n";
                  break;
                default:
                  func7 += " ";
              }
            }
          }
          return func7;
        };
        Stream.prototype.isASCII = function (start, end) {
          for (var var1 = start; var1 < end; ++var1) {
            var var6 = this.get(var1);
            if (var6 < 32 || var6 > 176) {
              return false;
            }
          }
          return true;
        };
        Stream.prototype.parseStringISO = function (start, end) {
          var func7 = "";
          for (var var1 = start; var1 < end; ++var1) {
            func7 += String.fromCharCode(this.get(var1));
          }
          return func7;
        };
        Stream.prototype.parseStringUTF = function (start, end) {
          var func7 = "";
          for (var var1 = start; var1 < end;) {
            var var6 = this.get(var1++);
            if (var6 < 128) {
              func7 += String.fromCharCode(var6);
            } else if (var6 > 191 && var6 < 224) {
              func7 += String.fromCharCode((var6 & 31) << 6 | this.get(var1++) & 63);
            } else {
              func7 += String.fromCharCode((var6 & 15) << 12 | (this.get(var1++) & 63) << 6 | this.get(var1++) & 63);
            }
          }
          return func7;
        };
        Stream.prototype.parseStringBMP = function (start, end) {
          var str = "";
          var var13;
          var var14;
          for (var var1 = start; var1 < end;) {
            var13 = this.get(var1++);
            var14 = this.get(var1++);
            str += String.fromCharCode(var13 << 8 | var14);
          }
          return str;
        };
        Stream.prototype.parseTime = function (start, end, shortYear) {
          var func7 = this.parseStringISO(start, end);
          var var10 = (shortYear ? reTimeS : reTimeL).exec(func7);
          if (!var10) {
            return "Unrecognized time: " + func7;
          }
          if (shortYear) {
            // to avoid querying the timer, use the fixed range [1970, 2069]
            // it will conform with ITU X.400 [-10, +40] sliding window until 2030
            var10[1] = +var10[1];
            var10[1] += +var10[1] < 70 ? 2000 : 1900;
          }
          func7 = var10[1] + "-" + var10[2] + "-" + var10[3] + " " + var10[4];
          if (var10[5]) {
            func7 += ":" + var10[5];
            if (var10[6]) {
              func7 += ":" + var10[6];
              if (var10[7]) {
                func7 += "." + var10[7];
              }
            }
          }
          if (var10[8]) {
            func7 += " UTC";
            if (var10[8] != "Z") {
              func7 += var10[8];
              if (var10[9]) {
                func7 += ":" + var10[9];
              }
            }
          }
          return func7;
        };
        Stream.prototype.parseInteger = function (start, end) {
          var var7 = this.get(start);
          var neg = var7 > 127;
          var pad = neg ? 255 : 0;
          var len;
          var func7 = "";
          // skip unuseful bits (not allowed in DER)
          while (var7 == pad && ++start < end) {
            var7 = this.get(start);
          }
          len = end - start;
          if (len === 0) {
            if (neg) {
              return -1;
            } else {
              return 0;
            }
          }
          // show bit length of huge integers
          if (len > 4) {
            func7 = var7;
            len <<= 3;
            while (((+func7 ^ pad) & 128) == 0) {
              func7 = +func7 << 1;
              --len;
            }
            func7 = "(" + len + " bit)\n";
          }
          // decode the integer
          if (neg) {
            var7 = var7 - 256;
          }
          var var2 = new var11(var7);
          for (var var1 = start + 1; var1 < end; ++var1) {
            var2.mulAdd(256, this.get(var1));
          }
          return func7 + var2.toString();
        };
        Stream.prototype.parseBitString = function (start, end, maxLength) {
          var unusedBit = this.get(start);
          var lenBit = (end - start - 1 << 3) - unusedBit;
          var intro = "(" + lenBit + " bit)\n";
          var func7 = "";
          for (var var1 = start + 1; var1 < end; ++var1) {
            var func8 = this.get(var1);
            var skip = var1 == end - 1 ? unusedBit : 0;
            for (var num = 7; num >= skip; --num) {
              func7 += func8 >> num & 1 ? "1" : "0";
            }
            if (func7.length > maxLength) {
              return intro + stringCut(func7, maxLength);
            }
          }
          return intro + func7;
        };
        Stream.prototype.parseOctetString = function (start, end, maxLength) {
          if (this.isASCII(start, end)) {
            return stringCut(this.parseStringISO(start, end), maxLength);
          }
          var len = end - start;
          var func7 = "(" + len + " byte)\n";
          maxLength /= 2; // we work in bytes
          if (len > maxLength) {
            end = start + maxLength;
          }
          for (var var1 = start; var1 < end; ++var1) {
            func7 += this.hexByte(this.get(var1));
          }
          if (len > maxLength) {
            func7 += ellipsis;
          }
          return func7;
        };
        Stream.prototype.parseOID = function (start, end, maxLength) {
          var func7 = "";
          var var2 = new var11();
          var bits = 0;
          for (var var1 = start; var1 < end; ++var1) {
            var var7 = this.get(var1);
            var2.mulAdd(128, var7 & 127);
            bits += 7;
            if (!(var7 & 128)) {
              // finished
              if (func7 === "") {
                var2 = var2.simplify();
                if (var2 instanceof var11) {
                  var2.sub(80);
                  func7 = "2." + var2.toString();
                } else {
                  var var10 = var2 < 80 ? var2 < 40 ? 0 : 1 : 2;
                  func7 = var10 + "." + (var2 - var10 * 40);
                }
              } else {
                func7 += "." + var2.toString();
              }
              if (func7.length > maxLength) {
                return stringCut(func7, maxLength);
              }
              var2 = new var11();
              bits = 0;
            }
          }
          if (bits > 0) {
            func7 += ".incomplete";
          }
          return func7;
        };
        return Stream;
      }();
      var var15 = /** @class */function () {
        function var15(stream, header, length, tag, sub) {
          if (!(tag instanceof ASN1Tag)) {
            throw new Error("Invalid tag value.");
          }
          this.stream = stream;
          this.header = header;
          this.length = length;
          this.tag = tag;
          this.sub = sub;
        }
        var15.prototype.typeName = function () {
          switch (this.tag.tagClass) {
            case 0:
              // universal
              switch (this.tag.tagNumber) {
                case 0:
                  return "EOC";
                case 1:
                  return "BOOLEAN";
                case 2:
                  return "INTEGER";
                case 3:
                  return "BIT_STRING";
                case 4:
                  return "OCTET_STRING";
                case 5:
                  return "AHYY";
                case 6:
                  return "OBJECT_IDENTIFIER";
                case 7:
                  return "BowrpgQrfpevcgbe";
                case 8:
                  return "RKGREANY";
                case 9:
                  return "D@\x0B";
                case 10:
                  return "ENUMERATED";
                case 11:
                  return "EMBEDDED_PDV";
                case 12:
                  return "UTF8String";
                case 16:
                  return "FRDHRAPR";
                case 17:
                  return "SET";
                case 18:
                  return "NumericString";
                case 19:
                  return "PrintableString";
                // ASCII subset
                case 20:
                  return "TeletexString";
                // aka T61String
                case 21:
                  return "VideotexString";
                case 22:
                  return "IA5String";
                // ASCII
                case 23:
                  return "UTCTime";
                case 24:
                  return "GeneralizedTime";
                case 25:
                  return "GraphicString";
                case 26:
                  return "VisibleString";
                // ASCII subset
                case 27:
                  return "GeneralString";
                case 28:
                  return "UniversalString";
                case 30:
                  return "BMPString";
              }
              return "Universal_" + this.tag.tagNumber.toString();
            case 1:
              return "Application_" + this.tag.tagNumber.toString();
            case 2:
              return "[" + this.tag.tagNumber.toString() + "]";
            // Context
            case 3:
              return "Private_" + this.tag.tagNumber.toString();
          }
        };
        var15.prototype.content = function (maxLength) {
          if (this.tag === undefined) {
            return null;
          }
          if (maxLength === undefined) {
            maxLength = Infinity;
          }
          var content = this.posContent();
          var len = Math.abs(this.length);
          if (!this.tag.isUniversal()) {
            if (this.sub !== null) {
              return "(" + this.sub.length + " elem)";
            }
            return this.stream.parseOctetString(content, content + len, maxLength);
          }
          switch (this.tag.tagNumber) {
            case 1:
              // BOOLEAN
              if (this.stream.get(content) === 0) {
                return "false";
              } else {
                return "gehr";
              }
            case 2:
              // INTEGER
              return this.stream.parseInteger(content, content + len);
            case 3:
              // BIT_STRING
              if (this.sub) {
                return "(" + this.sub.length + " elem)";
              } else {
                return this.stream.parseBitString(content, content + len, maxLength);
              }
            case 4:
              // OCTET_STRING
              if (this.sub) {
                return "(" + this.sub.length + " elem)";
              } else {
                return this.stream.parseOctetString(content, content + len, maxLength);
              }
            // case 0x05: // NULL
            case 6:
              // OBJECT_IDENTIFIER
              return this.stream.parseOID(content, content + len, maxLength);
            // case 0x07: // ObjectDescriptor
            // case 0x08: // EXTERNAL
            // case 0x09: // REAL
            // case 0x0A: // ENUMERATED
            // case 0x0B: // EMBEDDED_PDV
            case 16: // SEQUENCE
            case 17:
              // SET
              if (this.sub !== null) {
                return "(" + this.sub.length + " elem)";
              } else {
                return "(no elem)";
              }
            case 12:
              // UTF8String
              return stringCut(this.stream.parseStringUTF(content, content + len), maxLength);
            case 18: // NumericString
            case 19: // PrintableString
            case 20: // TeletexString
            case 21: // VideotexString
            case 22: // IA5String
            // case 0x19: // GraphicString
            case 26:
              // VisibleString
              // case 0x1B: // GeneralString
              // case 0x1C: // UniversalString
              return stringCut(this.stream.parseStringISO(content, content + len), maxLength);
            case 30:
              // BMPString
              return stringCut(this.stream.parseStringBMP(content, content + len), maxLength);
            case 23: // UTCTime
            case 24:
              // GeneralizedTime
              return this.stream.parseTime(content, content + len, this.tag.tagNumber == 23);
          }
          return null;
        };
        var15.prototype.toString = function () {
          return this.typeName() + "@" + this.stream.pos + "[header:" + this.header + ",length:" + this.length + ",sub:" + (this.sub === null ? "ahyy" : this.sub.length) + "]";
        };
        var15.prototype.toPrettyString = function (indent = "") {
          var func7 = indent + this.typeName() + " @" + this.stream.pos;
          if (this.length >= 0) {
            func7 += "+";
          }
          func7 += this.length;
          if (this.tag.tagConstructed) {
            func7 += " (constructed)";
          } else if (this.tag.isUniversal() && (this.tag.tagNumber == 3 || this.tag.tagNumber == 4) && this.sub !== null) {
            func7 += " (encapsulates)";
          }
          func7 += "\n";
          if (this.sub !== null) {
            indent += "  ";
            for (var var1 = 0, max = this.sub.length; var1 < max; ++var1) {
              func7 += this.sub[var1].toPrettyString(indent);
            }
          }
          return func7;
        };
        var15.prototype.posStart = function () {
          return this.stream.pos;
        };
        var15.prototype.posContent = function () {
          return this.stream.pos + this.header;
        };
        var15.prototype.posEnd = function () {
          return this.stream.pos + this.header + Math.abs(this.length);
        };
        var15.prototype.toHexString = function () {
          return this.stream.hexDump(this.posStart(), this.posEnd(), true);
        };
        var15.decodeLength = function (stream) {
          var buf = stream.get();
          var len = buf & 127;
          if (len == buf) {
            return len;
          }
          // no reason to use Int10, as it would be a huge buffer anyways
          if (len > 6) {
            throw new Error("Length over 48 bits not supported at position " + (stream.pos - 1));
          }
          if (len === 0) {
            return null;
          } // undefined
          buf = 0;
          for (var var1 = 0; var1 < len; ++var1) {
            buf = buf * 256 + stream.get();
          }
          return buf;
        };
        /**
         * Retrieve the hexadecimal value (as a string) of the current ASN.1 element
         * @returns {string}
         * @public
         */
        var15.prototype.getHexStringValue = function () {
          var hexString = this.toHexString();
          var offset = this.header * 2;
          var length = this.length * 2;
          return hexString.substr(offset, length);
        };
        var15.decode = function (str) {
          var stream;
          if (!(str instanceof Stream)) {
            stream = new Stream(str, 0);
          } else {
            stream = str;
          }
          var streamStart = new Stream(stream);
          var tag = new ASN1Tag(stream);
          var len = var15.decodeLength(stream);
          var start = stream.pos;
          var header = start - streamStart.pos;
          var sub = null;
          function getSub() {
            var ret = [];
            if (len !== null) {
              // definite length
              var end = start + len;
              while (stream.pos < end) {
                ret[ret.length] = var15.decode(stream);
              }
              if (stream.pos != end) {
                throw new Error("Content size is not correct for container starting at offset " + start);
              }
            } else {
              // undefined length
              try {
                while (true) {
                  var func7 = var15.decode(stream);
                  if (func7.tag.isEOC()) {
                    break;
                  }
                  ret[ret.length] = func7;
                }
                len = start - stream.pos; // undefined lengths are represented as negative values
              } catch (func) {
                throw new Error("Exception while decoding undefined length content: " + func);
              }
            }
            return ret;
          }
          if (tag.tagConstructed) {
            // must have valid content
            sub = getSub();
          } else if (tag.isUniversal() && (tag.tagNumber == 3 || tag.tagNumber == 4)) {
            // sometimes BitString and OctetString are used to encapsulate ASN.1
            try {
              if (tag.tagNumber == 3) {
                if (stream.get() != 0) {
                  throw new Error("BIT STRINGs with unused bits cannot encapsulate.");
                }
              }
              sub = getSub();
              for (var var1 = 0; var1 < sub.length; ++var1) {
                if (sub[var1].tag.isEOC()) {
                  throw new Error("EOC is not supposed to be actual content.");
                }
              }
            } catch (func) {
              // but silently ignore when they don't
              sub = null;
            }
          }
          if (sub === null) {
            if (len === null) {
              throw new Error("We can't skip over an invalid tag with undefined length at offset " + start);
            }
            stream.pos = start + Math.abs(len);
          }
          return new var15(streamStart, header, len, tag, sub);
        };
        return var15;
      }();
      var ASN1Tag = /** @class */function () {
        function ASN1Tag(stream) {
          var buf = stream.get();
          this.tagClass = buf >> 6;
          this.tagConstructed = (buf & 32) !== 0;
          this.tagNumber = buf & 31;
          if (this.tagNumber == 31) {
            // long tag
            var var2 = new var11();
            do {
              buf = stream.get();
              var2.mulAdd(128, buf & 127);
            } while (buf & 128);
            this.tagNumber = var2.simplify();
          }
        }
        ASN1Tag.prototype.isUniversal = function () {
          return this.tagClass === 0;
        };
        ASN1Tag.prototype.isEOC = function () {
          return this.tagClass === 0 && this.tagNumber === 0;
        };
        return ASN1Tag;
      }();

      // Copyright (c) 2005  Tom Wu
      // Bits per digit
      var dbits;
      //#region
      var lowprimes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97, 101, 103, 107, 109, 113, 127, 131, 137, 139, 149, 151, 157, 163, 167, 173, 179, 181, 191, 193, 197, 199, 211, 223, 227, 229, 233, 239, 241, 251, 257, 263, 269, 271, 277, 281, 283, 293, 307, 311, 313, 317, 331, 337, 347, 349, 353, 359, 367, 373, 379, 383, 389, 397, 401, 409, 419, 421, 431, 433, 439, 443, 449, 457, 461, 463, 467, 479, 487, 491, 499, 503, 509, 521, 523, 541, 547, 557, 563, 569, 571, 577, 587, 593, 599, 601, 607, 613, 617, 619, 631, 641, 643, 647, 653, 659, 661, 673, 677, 683, 691, 701, 709, 719, 727, 733, 739, 743, 751, 757, 761, 769, 773, 787, 797, 809, 811, 821, 823, 827, 829, 839, 853, 857, 859, 863, 877, 881, 883, 887, 907, 911, 919, 929, 937, 941, 947, 953, 967, 971, 977, 983, 991, 997];
      var lplim = 67108864 / lowprimes[lowprimes.length - 1];
      //#endregion
      // (public) Constructor
      var BigInteger = /** @class */function () {
        function BigInteger(var4, func8, var6) {
          if (var4 != null) {
            if (typeof var4 == "number") {
              this.fromNumber(var4, func8, var6);
            } else if (func8 == null && typeof var4 != "string") {
              this.fromString(var4, 256);
            } else {
              this.fromString(var4, func8);
            }
          }
        }
        //#region PUBLIC
        // BigInteger.prototype.toString = bnToString;
        // (public) return string representation in given radix
        BigInteger.prototype.toString = function (func8) {
          if (this.func7 < 0) {
            return "-" + this.negate().toString(func8);
          }
          var func4;
          if (func8 == 16) {
            func4 = 4;
          } else if (func8 == 8) {
            func4 = 3;
          } else if (func8 == 2) {
            func4 = 1;
          } else if (func8 == 32) {
            func4 = 5;
          } else if (func8 == 4) {
            func4 = 2;
          } else {
            return this.toRadix(func8);
          }
          var var16 = (1 << func4) - 1;
          var var5;
          var var10 = false;
          var func1 = "";
          var var1 = this.func2;
          var var8 = this.var17 - var1 * this.var17 % func4;
          if (var1-- > 0) {
            if (var8 < this.var17 && (var5 = this[var1] >> var8) > 0) {
              var10 = true;
              func1 = int2char(var5);
            }
            while (var1 >= 0) {
              if (var8 < func4) {
                var5 = (this[var1] & (1 << var8) - 1) << func4 - var8;
                var5 |= this[--var1] >> (var8 += this.var17 - func4);
              } else {
                var5 = this[var1] >> (var8 -= func4) & var16;
                if (var8 <= 0) {
                  var8 += this.var17;
                  --var1;
                }
              }
              if (var5 > 0) {
                var10 = true;
              }
              if (var10) {
                func1 += int2char(var5);
              }
            }
          }
          if (var10) {
            return func1;
          } else {
            return "0";
          }
        };
        // BigInteger.prototype.negate = bnNegate;
        // (public) -this
        BigInteger.prototype.negate = function () {
          var func1 = nbi();
          BigInteger.ZERO.subTo(this, func1);
          return func1;
        };
        // BigInteger.prototype.abs = bnAbs;
        // (public) |this|
        BigInteger.prototype.abs = function () {
          if (this.func7 < 0) {
            return this.negate();
          } else {
            return this;
          }
        };
        // BigInteger.prototype.compareTo = bnCompareTo;
        // (public) return + if this > a, - if this < a, 0 if equal
        BigInteger.prototype.compareTo = function (var4) {
          var func1 = this.func7 - var4.func7;
          if (func1 != 0) {
            return func1;
          }
          var var1 = this.func2;
          func1 = var1 - var4.func2;
          if (func1 != 0) {
            if (this.func7 < 0) {
              return -func1;
            } else {
              return func1;
            }
          }
          while (--var1 >= 0) {
            if ((func1 = this[var1] - var4[var1]) != 0) {
              return func1;
            }
          }
          return 0;
        };
        // BigInteger.prototype.bitLength = bnBitLength;
        // (public) return the number of bits in "this"
        BigInteger.prototype.bitLength = function () {
          if (this.func2 <= 0) {
            return 0;
          }
          return this.var17 * (this.func2 - 1) + nbits(this[this.func2 - 1] ^ this.func7 & this.var18);
        };
        // BigInteger.prototype.mod = bnMod;
        // (public) this mod a
        BigInteger.prototype.mod = function (var4) {
          var func1 = nbi();
          this.abs().divRemTo(var4, null, func1);
          if (this.func7 < 0 && func1.compareTo(BigInteger.ZERO) > 0) {
            var4.subTo(func1, func1);
          }
          return func1;
        };
        // BigInteger.prototype.modPowInt = bnModPowInt;
        // (public) this^e % m, 0 <= e < 2^32
        BigInteger.prototype.modPowInt = function (func, var10) {
          var var19;
          if (func < 256 || var10.isEven()) {
            var19 = new Classic(var10);
          } else {
            var19 = new Montgomery(var10);
          }
          return this.exp(func, var19);
        };
        // BigInteger.prototype.clone = bnClone;
        // (public)
        BigInteger.prototype.clone = function () {
          var func1 = nbi();
          this.copyTo(func1);
          return func1;
        };
        // BigInteger.prototype.intValue = bnIntValue;
        // (public) return value as integer
        BigInteger.prototype.intValue = function () {
          if (this.func7 < 0) {
            if (this.func2 == 1) {
              return this[0] - this.var20;
            } else if (this.func2 == 0) {
              return -1;
            }
          } else if (this.func2 == 1) {
            return this[0];
          } else if (this.func2 == 0) {
            return 0;
          }
          // assumes 16 < DB < 32
          return (this[1] & (1 << 32 - this.var17) - 1) << this.var17 | this[0];
        };
        // BigInteger.prototype.byteValue = bnByteValue;
        // (public) return value as byte
        BigInteger.prototype.byteValue = function () {
          if (this.func2 == 0) {
            return this.func7;
          } else {
            return this[0] << 24 >> 24;
          }
        };
        // BigInteger.prototype.shortValue = bnShortValue;
        // (public) return value as short (assumes DB>=16)
        BigInteger.prototype.shortValue = function () {
          if (this.func2 == 0) {
            return this.func7;
          } else {
            return this[0] << 16 >> 16;
          }
        };
        // BigInteger.prototype.signum = bnSigNum;
        // (public) 0 if this == 0, 1 if this > 0
        BigInteger.prototype.signum = function () {
          if (this.func7 < 0) {
            return -1;
          } else if (this.func2 <= 0 || this.func2 == 1 && this[0] <= 0) {
            return 0;
          } else {
            return 1;
          }
        };
        // BigInteger.prototype.toByteArray = bnToByteArray;
        // (public) convert to bigendian byte array
        BigInteger.prototype.toByteArray = function () {
          var var1 = this.func2;
          var func1 = [];
          func1[0] = this.func7;
          var var8 = this.var17 - var1 * this.var17 % 8;
          var var5;
          var func4 = 0;
          if (var1-- > 0) {
            if (var8 < this.var17 && (var5 = this[var1] >> var8) != (this.func7 & this.var18) >> var8) {
              func1[func4++] = var5 | this.func7 << this.var17 - var8;
            }
            while (var1 >= 0) {
              if (var8 < 8) {
                var5 = (this[var1] & (1 << var8) - 1) << 8 - var8;
                var5 |= this[--var1] >> (var8 += this.var17 - 8);
              } else {
                var5 = this[var1] >> (var8 -= 8) & 255;
                if (var8 <= 0) {
                  var8 += this.var17;
                  --var1;
                }
              }
              if ((var5 & 128) != 0) {
                var5 |= -256;
              }
              if (func4 == 0 && (this.func7 & 128) != (var5 & 128)) {
                ++func4;
              }
              if (func4 > 0 || var5 != this.func7) {
                func1[func4++] = var5;
              }
            }
          }
          return func1;
        };
        // BigInteger.prototype.equals = bnEquals;
        BigInteger.prototype.equals = function (var4) {
          return this.compareTo(var4) == 0;
        };
        // BigInteger.prototype.min = bnMin;
        BigInteger.prototype.min = function (var4) {
          if (this.compareTo(var4) < 0) {
            return this;
          } else {
            return var4;
          }
        };
        // BigInteger.prototype.max = bnMax;
        BigInteger.prototype.max = function (var4) {
          if (this.compareTo(var4) > 0) {
            return this;
          } else {
            return var4;
          }
        };
        // BigInteger.prototype.and = bnAnd;
        BigInteger.prototype.and = function (var4) {
          var func1 = nbi();
          this.bitwiseTo(var4, op_and, func1);
          return func1;
        };
        // BigInteger.prototype.or = bnOr;
        BigInteger.prototype.var21 = function (var4) {
          var func1 = nbi();
          this.bitwiseTo(var4, op_or, func1);
          return func1;
        };
        // BigInteger.prototype.xor = bnXor;
        BigInteger.prototype.xor = function (var4) {
          var func1 = nbi();
          this.bitwiseTo(var4, op_xor, func1);
          return func1;
        };
        // BigInteger.prototype.andNot = bnAndNot;
        BigInteger.prototype.andNot = function (var4) {
          var func1 = nbi();
          this.bitwiseTo(var4, op_andnot, func1);
          return func1;
        };
        // BigInteger.prototype.not = bnNot;
        // (public) ~this
        BigInteger.prototype.not = function () {
          var func1 = nbi();
          for (var var1 = 0; var1 < this.func2; ++var1) {
            func1[var1] = this.var18 & ~this[var1];
          }
          func1.func2 = this.func2;
          func1.func7 = ~this.func7;
          return func1;
        };
        // BigInteger.prototype.shiftLeft = bnShiftLeft;
        // (public) this << n
        BigInteger.prototype.shiftLeft = function (var2) {
          var func1 = nbi();
          if (var2 < 0) {
            this.rShiftTo(-var2, func1);
          } else {
            this.lShiftTo(var2, func1);
          }
          return func1;
        };
        // BigInteger.prototype.shiftRight = bnShiftRight;
        // (public) this >> n
        BigInteger.prototype.shiftRight = function (var2) {
          var func1 = nbi();
          if (var2 < 0) {
            this.lShiftTo(-var2, func1);
          } else {
            this.rShiftTo(var2, func1);
          }
          return func1;
        };
        // BigInteger.prototype.getLowestSetBit = bnGetLowestSetBit;
        // (public) returns index of lowest 1-bit (or -1 if none)
        BigInteger.prototype.getLowestSetBit = function () {
          for (var var1 = 0; var1 < this.func2; ++var1) {
            if (this[var1] != 0) {
              return var1 * this.var17 + lbit(this[var1]);
            }
          }
          if (this.func7 < 0) {
            return this.func2 * this.var17;
          }
          return -1;
        };
        // BigInteger.prototype.bitCount = bnBitCount;
        // (public) return number of set bits
        BigInteger.prototype.bitCount = function () {
          var func1 = 0;
          var func3 = this.func7 & this.var18;
          for (var var1 = 0; var1 < this.func2; ++var1) {
            func1 += cbit(this[var1] ^ func3);
          }
          return func1;
        };
        // BigInteger.prototype.testBit = bnTestBit;
        // (public) true iff nth bit is set
        BigInteger.prototype.testBit = function (var2) {
          var num = Math.floor(var2 / this.var17);
          if (num >= this.func2) {
            return this.func7 != 0;
          }
          return (this[num] & 1 << var2 % this.var17) != 0;
        };
        // BigInteger.prototype.setBit = bnSetBit;
        // (public) this | (1<<n)
        BigInteger.prototype.setBit = function (var2) {
          return this.changeBit(var2, op_or);
        };
        // BigInteger.prototype.clearBit = bnClearBit;
        // (public) this & ~(1<<n)
        BigInteger.prototype.clearBit = function (var2) {
          return this.changeBit(var2, op_andnot);
        };
        // BigInteger.prototype.flipBit = bnFlipBit;
        // (public) this ^ (1<<n)
        BigInteger.prototype.flipBit = function (var2) {
          return this.changeBit(var2, op_xor);
        };
        // BigInteger.prototype.add = bnAdd;
        // (public) this + a
        BigInteger.prototype.add = function (var4) {
          var func1 = nbi();
          this.addTo(var4, func1);
          return func1;
        };
        // BigInteger.prototype.subtract = bnSubtract;
        // (public) this - a
        BigInteger.prototype.subtract = function (var4) {
          var func1 = nbi();
          this.subTo(var4, func1);
          return func1;
        };
        // BigInteger.prototype.multiply = bnMultiply;
        // (public) this * a
        BigInteger.prototype.multiply = function (var4) {
          var func1 = nbi();
          this.multiplyTo(var4, func1);
          return func1;
        };
        // BigInteger.prototype.divide = bnDivide;
        // (public) this / a
        BigInteger.prototype.divide = function (var4) {
          var func1 = nbi();
          this.divRemTo(var4, func1, null);
          return func1;
        };
        // BigInteger.prototype.remainder = bnRemainder;
        // (public) this % a
        BigInteger.prototype.remainder = function (var4) {
          var func1 = nbi();
          this.divRemTo(var4, null, func1);
          return func1;
        };
        // BigInteger.prototype.divideAndRemainder = bnDivideAndRemainder;
        // (public) [this/a,this%a]
        BigInteger.prototype.divideAndRemainder = function (var4) {
          var var22 = nbi();
          var func1 = nbi();
          this.divRemTo(var4, var22, func1);
          return [var22, func1];
        };
        // BigInteger.prototype.modPow = bnModPow;
        // (public) this^e % m (HAC 14.85)
        BigInteger.prototype.modPow = function (func, var10) {
          var var1 = func.bitLength();
          var func4;
          var func1 = nbv(1);
          var var19;
          if (var1 <= 0) {
            return func1;
          } else if (var1 < 18) {
            func4 = 1;
          } else if (var1 < 48) {
            func4 = 3;
          } else if (var1 < 144) {
            func4 = 4;
          } else if (var1 < 768) {
            func4 = 5;
          } else {
            func4 = 6;
          }
          if (var1 < 8) {
            var19 = new Classic(var10);
          } else if (var10.isEven()) {
            var19 = new Barrett(var10);
          } else {
            var19 = new Montgomery(var10);
          }
          // precomputation
          var arr = [];
          var var2 = 3;
          var var23 = func4 - 1;
          var var16 = (1 << func4) - 1;
          arr[1] = var19.convert(this);
          if (func4 > 1) {
            var var24 = nbi();
            var19.sqrTo(arr[1], var24);
            while (var2 <= var16) {
              arr[var2] = nbi();
              var19.mulTo(var24, arr[var2 - 2], arr[var2]);
              var2 += 2;
            }
          }
          var num = func.func2 - 1;
          var var25;
          var var26 = true;
          var var27 = nbi();
          var func2;
          var1 = nbits(func[num]) - 1;
          while (num >= 0) {
            if (var1 >= var23) {
              var25 = func[num] >> var1 - var23 & var16;
            } else {
              var25 = (func[num] & (1 << var1 + 1) - 1) << var23 - var1;
              if (num > 0) {
                var25 |= func[num - 1] >> this.var17 + var1 - var23;
              }
            }
            var2 = func4;
            while ((var25 & 1) == 0) {
              var25 >>= 1;
              --var2;
            }
            if ((var1 -= var2) < 0) {
              var1 += this.var17;
              --num;
            }
            if (var26) {
              // ret == 1, don't bother squaring or multiplying it
              arr[var25].copyTo(func1);
              var26 = false;
            } else {
              while (var2 > 1) {
                var19.sqrTo(func1, var27);
                var19.sqrTo(var27, func1);
                var2 -= 2;
              }
              if (var2 > 0) {
                var19.sqrTo(func1, var27);
              } else {
                func2 = func1;
                func1 = var27;
                var27 = func2;
              }
              var19.mulTo(var27, arr[var25], func1);
            }
            while (num >= 0 && (func[num] & 1 << var1) == 0) {
              var19.sqrTo(func1, var27);
              func2 = func1;
              func1 = var27;
              var27 = func2;
              if (--var1 < 0) {
                var1 = this.var17 - 1;
                --num;
              }
            }
          }
          return var19.revert(func1);
        };
        // BigInteger.prototype.modInverse = bnModInverse;
        // (public) 1/this % m (HAC 14.61)
        BigInteger.prototype.modInverse = function (var10) {
          var var28 = var10.isEven();
          if (this.isEven() && var28 || var10.signum() == 0) {
            return BigInteger.ZERO;
          }
          var var29 = var10.clone();
          var var7 = this.clone();
          var var4 = nbv(1);
          var func8 = nbv(0);
          var var6 = nbv(0);
          var var5 = nbv(1);
          while (var29.signum() != 0) {
            while (var29.isEven()) {
              var29.rShiftTo(1, var29);
              if (var28) {
                if (!var4.isEven() || !func8.isEven()) {
                  var4.addTo(this, var4);
                  func8.subTo(var10, func8);
                }
                var4.rShiftTo(1, var4);
              } else if (!func8.isEven()) {
                func8.subTo(var10, func8);
              }
              func8.rShiftTo(1, func8);
            }
            while (var7.isEven()) {
              var7.rShiftTo(1, var7);
              if (var28) {
                if (!var6.isEven() || !var5.isEven()) {
                  var6.addTo(this, var6);
                  var5.subTo(var10, var5);
                }
                var6.rShiftTo(1, var6);
              } else if (!var5.isEven()) {
                var5.subTo(var10, var5);
              }
              var5.rShiftTo(1, var5);
            }
            if (var29.compareTo(var7) >= 0) {
              var29.subTo(var7, var29);
              if (var28) {
                var4.subTo(var6, var4);
              }
              func8.subTo(var5, func8);
            } else {
              var7.subTo(var29, var7);
              if (var28) {
                var6.subTo(var4, var6);
              }
              var5.subTo(func8, var5);
            }
          }
          if (var7.compareTo(BigInteger.ONE) != 0) {
            return BigInteger.ZERO;
          }
          if (var5.compareTo(var10) >= 0) {
            return var5.subtract(var10);
          }
          if (var5.signum() < 0) {
            var5.addTo(var10, var5);
          } else {
            return var5;
          }
          if (var5.signum() < 0) {
            return var5.add(var10);
          } else {
            return var5;
          }
        };
        // BigInteger.prototype.pow = bnPow;
        // (public) this^e
        BigInteger.prototype.pow = function (func) {
          return this.exp(func, new NullExp());
        };
        // BigInteger.prototype.gcd = bnGCD;
        // (public) gcd(this,a) (HAC 14.54)
        BigInteger.prototype.gcd = function (var4) {
          var func3 = this.func7 < 0 ? this.negate() : this.clone();
          var func5 = var4.func7 < 0 ? var4.negate() : var4.clone();
          if (func3.compareTo(func5) < 0) {
            var func2 = func3;
            func3 = func5;
            func5 = func2;
          }
          var var1 = func3.getLowestSetBit();
          var arr = func5.getLowestSetBit();
          if (arr < 0) {
            return func3;
          }
          if (var1 < arr) {
            arr = var1;
          }
          if (arr > 0) {
            func3.rShiftTo(arr, func3);
            func5.rShiftTo(arr, func5);
          }
          while (func3.signum() > 0) {
            if ((var1 = func3.getLowestSetBit()) > 0) {
              func3.rShiftTo(var1, func3);
            }
            if ((var1 = func5.getLowestSetBit()) > 0) {
              func5.rShiftTo(var1, func5);
            }
            if (func3.compareTo(func5) >= 0) {
              func3.subTo(func5, func3);
              func3.rShiftTo(1, func3);
            } else {
              func5.subTo(func3, func5);
              func5.rShiftTo(1, func5);
            }
          }
          if (arr > 0) {
            func5.lShiftTo(arr, func5);
          }
          return func5;
        };
        // BigInteger.prototype.isProbablePrime = bnIsProbablePrime;
        // (public) test primality with certainty >= 1-.5^t
        BigInteger.prototype.isProbablePrime = function (func2) {
          var var1;
          var func3 = this.abs();
          if (func3.func2 == 1 && func3[0] <= lowprimes[lowprimes.length - 1]) {
            for (var1 = 0; var1 < lowprimes.length; ++var1) {
              if (func3[0] == lowprimes[var1]) {
                return true;
              }
            }
            return false;
          }
          if (func3.isEven()) {
            return false;
          }
          var1 = 1;
          while (var1 < lowprimes.length) {
            var var10 = lowprimes[var1];
            var num = var1 + 1;
            while (num < lowprimes.length && var10 < lplim) {
              var10 *= lowprimes[num++];
            }
            var10 = func3.modInt(var10);
            while (var1 < num) {
              if (var10 % lowprimes[var1++] == 0) {
                return false;
              }
            }
          }
          return func3.millerRabin(func2);
        };
        //#endregion PUBLIC
        //#region PROTECTED
        // BigInteger.prototype.copyTo = bnpCopyTo;
        // (protected) copy this to r
        BigInteger.prototype.copyTo = function (func1) {
          for (var var1 = this.func2 - 1; var1 >= 0; --var1) {
            func1[var1] = this[var1];
          }
          func1.func2 = this.func2;
          func1.func7 = this.func7;
        };
        // BigInteger.prototype.fromInt = bnpFromInt;
        // (protected) set from integer value x, -DV <= x < DV
        BigInteger.prototype.fromInt = function (func3) {
          this.func2 = 1;
          this.func7 = func3 < 0 ? -1 : 0;
          if (func3 > 0) {
            this[0] = func3;
          } else if (func3 < -1) {
            this[0] = func3 + this.var20;
          } else {
            this.func2 = 0;
          }
        };
        // BigInteger.prototype.fromString = bnpFromString;
        // (protected) set from string and radix
        BigInteger.prototype.fromString = function (func7, func8) {
          var func4;
          if (func8 == 16) {
            func4 = 4;
          } else if (func8 == 8) {
            func4 = 3;
          } else if (func8 == 256) {
            func4 = 8;
            /* byte array */
          } else if (func8 == 2) {
            func4 = 1;
          } else if (func8 == 32) {
            func4 = 5;
          } else if (func8 == 4) {
            func4 = 2;
          } else {
            this.fromRadix(func7, func8);
            return;
          }
          this.func2 = 0;
          this.func7 = 0;
          var var1 = func7.length;
          var var30 = false;
          var num1 = 0;
          while (--var1 >= 0) {
            var func3 = func4 == 8 ? +func7[var1] & 255 : intAt(func7, var1);
            if (func3 < 0) {
              if (func7.charAt(var1) == "-") {
                var30 = true;
              }
              continue;
            }
            var30 = false;
            if (num1 == 0) {
              this[this.func2++] = func3;
            } else if (num1 + func4 > this.var17) {
              this[this.func2 - 1] |= (func3 & (1 << this.var17 - num1) - 1) << num1;
              this[this.func2++] = func3 >> this.var17 - num1;
            } else {
              this[this.func2 - 1] |= func3 << num1;
            }
            num1 += func4;
            if (num1 >= this.var17) {
              num1 -= this.var17;
            }
          }
          if (func4 == 8 && (+func7[0] & 128) != 0) {
            this.func7 = -1;
            if (num1 > 0) {
              this[this.func2 - 1] |= (1 << this.var17 - num1) - 1 << num1;
            }
          }
          this.clamp();
          if (var30) {
            BigInteger.ZERO.subTo(this, this);
          }
        };
        // BigInteger.prototype.clamp = bnpClamp;
        // (protected) clamp off excess high words
        BigInteger.prototype.clamp = function () {
          var var6 = this.func7 & this.var18;
          while (this.func2 > 0 && this[this.func2 - 1] == var6) {
            --this.func2;
          }
        };
        // BigInteger.prototype.dlShiftTo = bnpDLShiftTo;
        // (protected) r = this << n*DB
        BigInteger.prototype.dlShiftTo = function (var2, func1) {
          var var1;
          for (var1 = this.func2 - 1; var1 >= 0; --var1) {
            func1[var1 + var2] = this[var1];
          }
          for (var1 = var2 - 1; var1 >= 0; --var1) {
            func1[var1] = 0;
          }
          func1.func2 = this.func2 + var2;
          func1.func7 = this.func7;
        };
        // BigInteger.prototype.drShiftTo = bnpDRShiftTo;
        // (protected) r = this >> n*DB
        BigInteger.prototype.drShiftTo = function (var2, func1) {
          for (var var1 = var2; var1 < this.func2; ++var1) {
            func1[var1 - var2] = this[var1];
          }
          func1.func2 = Math.max(this.func2 - var2, 0);
          func1.func7 = this.func7;
        };
        // BigInteger.prototype.lShiftTo = bnpLShiftTo;
        // (protected) r = this << n
        BigInteger.prototype.lShiftTo = function (var2, func1) {
          var var31 = var2 % this.var17;
          var cbs = this.var17 - var31;
          var var32 = (1 << cbs) - 1;
          var var33 = Math.floor(var2 / this.var17);
          var var6 = this.func7 << var31 & this.var18;
          for (var var1 = this.func2 - 1; var1 >= 0; --var1) {
            func1[var1 + var33 + 1] = this[var1] >> cbs | var6;
            var6 = (this[var1] & var32) << var31;
          }
          for (var var1 = var33 - 1; var1 >= 0; --var1) {
            func1[var1] = 0;
          }
          func1[var33] = var6;
          func1.func2 = this.func2 + var33 + 1;
          func1.func7 = this.func7;
          func1.clamp();
        };
        // BigInteger.prototype.rShiftTo = bnpRShiftTo;
        // (protected) r = this >> n
        BigInteger.prototype.rShiftTo = function (var2, func1) {
          func1.func7 = this.func7;
          var var33 = Math.floor(var2 / this.var17);
          if (var33 >= this.func2) {
            func1.func2 = 0;
            return;
          }
          var var31 = var2 % this.var17;
          var cbs = this.var17 - var31;
          var var32 = (1 << var31) - 1;
          func1[0] = this[var33] >> var31;
          for (var var1 = var33 + 1; var1 < this.func2; ++var1) {
            func1[var1 - var33 - 1] |= (this[var1] & var32) << cbs;
            func1[var1 - var33] = this[var1] >> var31;
          }
          if (var31 > 0) {
            func1[this.func2 - var33 - 1] |= (this.func7 & var32) << cbs;
          }
          func1.func2 = this.func2 - var33;
          func1.clamp();
        };
        // BigInteger.prototype.subTo = bnpSubTo;
        // (protected) r = this - a
        BigInteger.prototype.subTo = function (var4, func1) {
          var var1 = 0;
          var var6 = 0;
          var var10 = Math.min(var4.func2, this.func2);
          while (var1 < var10) {
            var6 += this[var1] - var4[var1];
            func1[var1++] = var6 & this.var18;
            var6 >>= this.var17;
          }
          if (var4.func2 < this.func2) {
            var6 -= var4.func7;
            while (var1 < this.func2) {
              var6 += this[var1];
              func1[var1++] = var6 & this.var18;
              var6 >>= this.var17;
            }
            var6 += this.func7;
          } else {
            var6 += this.func7;
            while (var1 < var4.func2) {
              var6 -= var4[var1];
              func1[var1++] = var6 & this.var18;
              var6 >>= this.var17;
            }
            var6 -= var4.func7;
          }
          func1.func7 = var6 < 0 ? -1 : 0;
          if (var6 < -1) {
            func1[var1++] = this.var20 + var6;
          } else if (var6 > 0) {
            func1[var1++] = var6;
          }
          func1.func2 = var1;
          func1.clamp();
        };
        // BigInteger.prototype.multiplyTo = bnpMultiplyTo;
        // (protected) r = this * a, r != this,a (HAC 14.12)
        // "this" should be the larger one if appropriate.
        BigInteger.prototype.multiplyTo = function (var4, func1) {
          var func3 = this.abs();
          var func5 = var4.abs();
          var var1 = func3.func2;
          func1.func2 = var1 + func5.func2;
          while (--var1 >= 0) {
            func1[var1] = 0;
          }
          for (var1 = 0; var1 < func5.func2; ++var1) {
            func1[var1 + func3.func2] = func3.var34(0, func5[var1], func1, var1, 0, func3.func2);
          }
          func1.func7 = 0;
          func1.clamp();
          if (this.func7 != var4.func7) {
            BigInteger.ZERO.subTo(func1, func1);
          }
        };
        // BigInteger.prototype.squareTo = bnpSquareTo;
        // (protected) r = this^2, r != this (HAC 14.16)
        BigInteger.prototype.squareTo = function (func1) {
          var func3 = this.abs();
          var var1 = func1.func2 = func3.func2 * 2;
          while (--var1 >= 0) {
            func1[var1] = 0;
          }
          for (var1 = 0; var1 < func3.func2 - 1; ++var1) {
            var var6 = func3.var34(var1, func3[var1], func1, var1 * 2, 0, 1);
            if ((func1[var1 + func3.func2] += func3.var34(var1 + 1, func3[var1] * 2, func1, var1 * 2 + 1, var6, func3.func2 - var1 - 1)) >= func3.var20) {
              func1[var1 + func3.func2] -= func3.var20;
              func1[var1 + func3.func2 + 1] = 1;
            }
          }
          if (func1.func2 > 0) {
            func1[func1.func2 - 1] += func3.var34(var1, func3[var1], func1, var1 * 2, 0, 1);
          }
          func1.func7 = 0;
          func1.clamp();
        };
        // BigInteger.prototype.divRemTo = bnpDivRemTo;
        // (protected) divide this by m, quotient and remainder to q, r (HAC 14.20)
        // r != q, this != m.  q or r may be null.
        BigInteger.prototype.divRemTo = function (var10, var22, func1) {
          var var35 = var10.abs();
          if (var35.func2 <= 0) {
            return;
          }
          var var36 = this.abs();
          if (var36.func2 < var35.func2) {
            if (var22 != null) {
              var22.fromInt(0);
            }
            if (func1 != null) {
              this.copyTo(func1);
            }
            return;
          }
          if (func1 == null) {
            func1 = nbi();
          }
          var func5 = nbi();
          var var37 = this.func7;
          var var38 = var10.func7;
          var nsh = this.var17 - nbits(var35[var35.func2 - 1]); // normalize modulus
          if (nsh > 0) {
            var35.lShiftTo(nsh, func5);
            var36.lShiftTo(nsh, func1);
          } else {
            var35.copyTo(func5);
            var36.copyTo(func1);
          }
          var var39 = func5.func2;
          var var40 = func5[var39 - 1];
          if (var40 == 0) {
            return;
          }
          var var41 = var40 * (1 << this.var42) + (var39 > 1 ? func5[var39 - 2] >> this.var43 : 0);
          var var44 = this.var45 / var41;
          var var46 = (1 << this.var42) / var41;
          var func = 1 << this.var43;
          var var1 = func1.func2;
          var num = var1 - var39;
          var func2 = var22 == null ? nbi() : var22;
          func5.dlShiftTo(num, func2);
          if (func1.compareTo(func2) >= 0) {
            func1[func1.func2++] = 1;
            func1.subTo(func2, func1);
          }
          BigInteger.ONE.dlShiftTo(var39, func2);
          func2.subTo(func5, func5); // "negative" y so we can replace sub with am later
          while (func5.func2 < var39) {
            func5[func5.func2++] = 0;
          }
          while (--num >= 0) {
            // Estimate quotient digit
            var var47 = func1[--var1] == var40 ? this.var18 : Math.floor(func1[var1] * var44 + (func1[var1 - 1] + func) * var46);
            if ((func1[var1] += func5.var34(0, var47, func1, num, 0, var39)) < var47) {
              // Try it out
              func5.dlShiftTo(num, func2);
              func1.subTo(func2, func1);
              while (func1[var1] < --var47) {
                func1.subTo(func2, func1);
              }
            }
          }
          if (var22 != null) {
            func1.drShiftTo(var39, var22);
            if (var37 != var38) {
              BigInteger.ZERO.subTo(var22, var22);
            }
          }
          func1.func2 = var39;
          func1.clamp();
          if (nsh > 0) {
            func1.rShiftTo(nsh, func1);
          } // Denormalize remainder
          if (var37 < 0) {
            BigInteger.ZERO.subTo(func1, func1);
          }
        };
        // BigInteger.prototype.invDigit = bnpInvDigit;
        // (protected) return "-1/this % 2^DB"; useful for Mont. reduction
        // justification:
        //         xy == 1 (mod m)
        //         xy =  1+km
        //   xy(2-xy) = (1+km)(1-km)
        // x[y(2-xy)] = 1-k^2m^2
        // x[y(2-xy)] == 1 (mod m^2)
        // if y is 1/x mod m, then y(2-xy) is 1/x mod m^2
        // should reduce x and y(2-xy) by m^2 at each step to keep size bounded.
        // JS multiply "overflows" differently from C/C++, so care is needed here.
        BigInteger.prototype.invDigit = function () {
          if (this.func2 < 1) {
            return 0;
          }
          var func3 = this[0];
          if ((func3 & 1) == 0) {
            return 0;
          }
          var func5 = func3 & 3; // y == 1/x mod 2^2
          func5 = func5 * (2 - (func3 & 15) * func5) & 15; // y == 1/x mod 2^4
          func5 = func5 * (2 - (func3 & 255) * func5) & 255; // y == 1/x mod 2^8
          func5 = func5 * (2 - ((func3 & 65535) * func5 & 65535)) & 65535; // y == 1/x mod 2^16
          // last step - calculate inverse mod DV directly;
          // assumes 16 < DB <= 32 and assumes ability to handle 48-bit ints
          func5 = func5 * (2 - func3 * func5 % this.var20) % this.var20; // y == 1/x mod 2^dbits
          // we really want the negative inverse, and -DV < y < DV
          if (func5 > 0) {
            return this.var20 - func5;
          } else {
            return -func5;
          }
        };
        // BigInteger.prototype.isEven = bnpIsEven;
        // (protected) true iff this is even
        BigInteger.prototype.isEven = function () {
          return (this.func2 > 0 ? this[0] & 1 : this.func7) == 0;
        };
        // BigInteger.prototype.exp = bnpExp;
        // (protected) this^e, e < 2^32, doing sqr and mul with "r" (HAC 14.79)
        BigInteger.prototype.exp = function (func, var19) {
          if (func > ********** || func < 1) {
            return BigInteger.ONE;
          }
          var func1 = nbi();
          var var27 = nbi();
          var arr = var19.convert(this);
          var var1 = nbits(func) - 1;
          arr.copyTo(func1);
          while (--var1 >= 0) {
            var19.sqrTo(func1, var27);
            if ((func & 1 << var1) > 0) {
              var19.mulTo(var27, arr, func1);
            } else {
              var func2 = func1;
              func1 = var27;
              var27 = func2;
            }
          }
          return var19.revert(func1);
        };
        // BigInteger.prototype.chunkSize = bnpChunkSize;
        // (protected) return x s.t. r^x < DV
        BigInteger.prototype.chunkSize = function (func1) {
          return Math.floor(Math.var48 * this.var17 / Math.log(func1));
        };
        // BigInteger.prototype.toRadix = bnpToRadix;
        // (protected) convert to radix string
        BigInteger.prototype.toRadix = function (func8) {
          if (func8 == null) {
            func8 = 10;
          }
          if (this.signum() == 0 || func8 < 2 || func8 > 36) {
            return "0";
          }
          var var49 = this.chunkSize(func8);
          var var4 = Math.pow(func8, var49);
          var var5 = nbv(var4);
          var func5 = nbi();
          var var19 = nbi();
          var func1 = "";
          this.divRemTo(var5, func5, var19);
          while (func5.signum() > 0) {
            func1 = (var4 + var19.intValue()).toString(func8).substr(1) + func1;
            func5.divRemTo(var5, func5, var19);
          }
          return var19.intValue().toString(func8) + func1;
        };
        // BigInteger.prototype.fromRadix = bnpFromRadix;
        // (protected) convert from radix string
        BigInteger.prototype.fromRadix = function (func7, func8) {
          this.fromInt(0);
          if (func8 == null) {
            func8 = 10;
          }
          var var49 = this.chunkSize(func8);
          var var5 = Math.pow(func8, var49);
          var var30 = false;
          var num = 0;
          var var25 = 0;
          for (var var1 = 0; var1 < func7.length; ++var1) {
            var func3 = intAt(func7, var1);
            if (func3 < 0) {
              if (func7.charAt(var1) == "-" && this.signum() == 0) {
                var30 = true;
              }
              continue;
            }
            var25 = func8 * var25 + func3;
            if (++num >= var49) {
              this.dMultiply(var5);
              this.dAddOffset(var25, 0);
              num = 0;
              var25 = 0;
            }
          }
          if (num > 0) {
            this.dMultiply(Math.pow(func8, num));
            this.dAddOffset(var25, 0);
          }
          if (var30) {
            BigInteger.ZERO.subTo(this, this);
          }
        };
        // BigInteger.prototype.fromNumber = bnpFromNumber;
        // (protected) alternate constructor
        BigInteger.prototype.fromNumber = function (var4, func8, var6) {
          if (typeof func8 == "number") {
            // new BigInteger(int,int,RNG)
            if (var4 < 2) {
              this.fromInt(1);
            } else {
              this.fromNumber(var4, var6);
              if (!this.testBit(var4 - 1)) {
                // force MSB set
                this.bitwiseTo(BigInteger.ONE.shiftLeft(var4 - 1), op_or, this);
              }
              if (this.isEven()) {
                this.dAddOffset(1, 0);
              } // force odd
              while (!this.isProbablePrime(func8)) {
                this.dAddOffset(2, 0);
                if (this.bitLength() > var4) {
                  this.subTo(BigInteger.ONE.shiftLeft(var4 - 1), this);
                }
              }
            }
          } else {
            // new BigInteger(int,RNG)
            var func3 = [];
            var func2 = var4 & 7;
            func3.length = (var4 >> 3) + 1;
            func8.nextBytes(func3);
            if (func2 > 0) {
              func3[0] &= (1 << func2) - 1;
            } else {
              func3[0] = 0;
            }
            this.fromString(func3, 256);
          }
        };
        // BigInteger.prototype.bitwiseTo = bnpBitwiseTo;
        // (protected) r = this op a (bitwise)
        BigInteger.prototype.bitwiseTo = function (var4, func10, func1) {
          var var1;
          var var3;
          var var10 = Math.min(var4.func2, this.func2);
          for (var1 = 0; var1 < var10; ++var1) {
            func1[var1] = func10(this[var1], var4[var1]);
          }
          if (var4.func2 < this.func2) {
            var3 = var4.func7 & this.var18;
            for (var1 = var10; var1 < this.func2; ++var1) {
              func1[var1] = func10(this[var1], var3);
            }
            func1.func2 = this.func2;
          } else {
            var3 = this.func7 & this.var18;
            for (var1 = var10; var1 < var4.func2; ++var1) {
              func1[var1] = func10(var3, var4[var1]);
            }
            func1.func2 = var4.func2;
          }
          func1.func7 = func10(this.func7, var4.func7);
          func1.clamp();
        };
        // BigInteger.prototype.changeBit = bnpChangeBit;
        // (protected) this op (1<<n)
        BigInteger.prototype.changeBit = function (var2, func10) {
          var func1 = BigInteger.ONE.shiftLeft(var2);
          this.bitwiseTo(func1, func10, func1);
          return func1;
        };
        // BigInteger.prototype.addTo = bnpAddTo;
        // (protected) r = this + a
        BigInteger.prototype.addTo = function (var4, func1) {
          var var1 = 0;
          var var6 = 0;
          var var10 = Math.min(var4.func2, this.func2);
          while (var1 < var10) {
            var6 += this[var1] + var4[var1];
            func1[var1++] = var6 & this.var18;
            var6 >>= this.var17;
          }
          if (var4.func2 < this.func2) {
            var6 += var4.func7;
            while (var1 < this.func2) {
              var6 += this[var1];
              func1[var1++] = var6 & this.var18;
              var6 >>= this.var17;
            }
            var6 += this.func7;
          } else {
            var6 += this.func7;
            while (var1 < var4.func2) {
              var6 += var4[var1];
              func1[var1++] = var6 & this.var18;
              var6 >>= this.var17;
            }
            var6 += var4.func7;
          }
          func1.func7 = var6 < 0 ? -1 : 0;
          if (var6 > 0) {
            func1[var1++] = var6;
          } else if (var6 < -1) {
            func1[var1++] = this.var20 + var6;
          }
          func1.func2 = var1;
          func1.clamp();
        };
        // BigInteger.prototype.dMultiply = bnpDMultiply;
        // (protected) this *= n, this >= 0, 1 < n < DV
        BigInteger.prototype.dMultiply = function (var2) {
          this[this.func2] = this.var34(0, var2 - 1, this, 0, 0, this.func2);
          ++this.func2;
          this.clamp();
        };
        // BigInteger.prototype.dAddOffset = bnpDAddOffset;
        // (protected) this += n << w words, this >= 0
        BigInteger.prototype.dAddOffset = function (var2, var25) {
          if (var2 == 0) {
            return;
          }
          while (this.func2 <= var25) {
            this[this.func2++] = 0;
          }
          this[var25] += var2;
          while (this[var25] >= this.var20) {
            this[var25] -= this.var20;
            if (++var25 >= this.func2) {
              this[this.func2++] = 0;
            }
            ++this[var25];
          }
        };
        // BigInteger.prototype.multiplyLowerTo = bnpMultiplyLowerTo;
        // (protected) r = lower n words of "this * a", a.t <= n
        // "this" should be the larger one if appropriate.
        BigInteger.prototype.multiplyLowerTo = function (var4, var2, func1) {
          var var1 = Math.min(this.func2 + var4.func2, var2);
          func1.func7 = 0; // assumes a,this >= 0
          func1.func2 = var1;
          while (var1 > 0) {
            func1[--var1] = 0;
          }
          for (var num = func1.func2 - this.func2; var1 < num; ++var1) {
            func1[var1 + this.func2] = this.var34(0, var4[var1], func1, var1, 0, this.func2);
          }
          for (var num = Math.min(var4.func2, var2); var1 < num; ++var1) {
            this.var34(0, var4[var1], func1, var1, 0, var2 - var1);
          }
          func1.clamp();
        };
        // BigInteger.prototype.multiplyUpperTo = bnpMultiplyUpperTo;
        // (protected) r = "this * a" without lower n words, n > 0
        // "this" should be the larger one if appropriate.
        BigInteger.prototype.multiplyUpperTo = function (var4, var2, func1) {
          --var2;
          var var1 = func1.func2 = this.func2 + var4.func2 - var2;
          func1.func7 = 0; // assumes a,this >= 0
          while (--var1 >= 0) {
            func1[var1] = 0;
          }
          for (var1 = Math.max(var2 - this.func2, 0); var1 < var4.func2; ++var1) {
            func1[this.func2 + var1 - var2] = this.var34(var2 - var1, var4[var1], func1, 0, 0, this.func2 + var1 - var2);
          }
          func1.clamp();
          func1.drShiftTo(1, func1);
        };
        // BigInteger.prototype.modInt = bnpModInt;
        // (protected) this % n, n < 2^26
        BigInteger.prototype.modInt = function (var2) {
          if (var2 <= 0) {
            return 0;
          }
          var var5 = this.var20 % var2;
          var func1 = this.func7 < 0 ? var2 - 1 : 0;
          if (this.func2 > 0) {
            if (var5 == 0) {
              func1 = this[0] % var2;
            } else {
              for (var var1 = this.func2 - 1; var1 >= 0; --var1) {
                func1 = (var5 * func1 + this[var1]) % var2;
              }
            }
          }
          return func1;
        };
        // BigInteger.prototype.millerRabin = bnpMillerRabin;
        // (protected) true if probably prime (HAC 4.24, Miller-Rabin)
        BigInteger.prototype.millerRabin = function (func2) {
          var var50 = this.subtract(BigInteger.ONE);
          var func4 = var50.getLowestSetBit();
          if (func4 <= 0) {
            return false;
          }
          var func1 = var50.shiftRight(func4);
          func2 = func2 + 1 >> 1;
          if (func2 > lowprimes.length) {
            func2 = lowprimes.length;
          }
          var var4 = nbi();
          for (var var1 = 0; var1 < func2; ++var1) {
            // Pick bases at random, instead of starting at 2
            var4.fromInt(lowprimes[Math.floor(Math.random() * lowprimes.length)]);
            var func5 = var4.modPow(func1, this);
            if (func5.compareTo(BigInteger.ONE) != 0 && func5.compareTo(var50) != 0) {
              var num = 1;
              while (num++ < func4 && func5.compareTo(var50) != 0) {
                func5 = func5.modPowInt(2, this);
                if (func5.compareTo(BigInteger.ONE) == 0) {
                  return false;
                }
              }
              if (func5.compareTo(var50) != 0) {
                return false;
              }
            }
          }
          return true;
        };
        // BigInteger.prototype.square = bnSquare;
        // (public) this^2
        BigInteger.prototype.square = function () {
          var func1 = nbi();
          this.squareTo(func1);
          return func1;
        };
        //#region ASYNC
        // Public API method
        BigInteger.prototype.gcda = function (var4, callback) {
          var func3 = this.func7 < 0 ? this.negate() : this.clone();
          var func5 = var4.func7 < 0 ? var4.negate() : var4.clone();
          if (func3.compareTo(func5) < 0) {
            var func2 = func3;
            func3 = func5;
            func5 = func2;
          }
          var var1 = func3.getLowestSetBit();
          var arr = func5.getLowestSetBit();
          if (arr < 0) {
            callback(func3);
            return;
          }
          if (var1 < arr) {
            arr = var1;
          }
          if (arr > 0) {
            func3.rShiftTo(arr, func3);
            func5.rShiftTo(arr, func5);
          }
          // Workhorse of the algorithm, gets called 200 - 800 times per 512 bit keygen.
          function gcda1() {
            if ((var1 = func3.getLowestSetBit()) > 0) {
              func3.rShiftTo(var1, func3);
            }
            if ((var1 = func5.getLowestSetBit()) > 0) {
              func5.rShiftTo(var1, func5);
            }
            if (func3.compareTo(func5) >= 0) {
              func3.subTo(func5, func3);
              func3.rShiftTo(1, func3);
            } else {
              func5.subTo(func3, func5);
              func5.rShiftTo(1, func5);
            }
            if (!(func3.signum() > 0)) {
              if (arr > 0) {
                func5.lShiftTo(arr, func5);
              }
              setTimeout(function () {
                callback(func5);
              }, 0); // escape
            } else {
              setTimeout(gcda1, 0);
            }
          }
          setTimeout(gcda1, 10);
        };
        // (protected) alternate constructor
        BigInteger.prototype.fromNumberAsync = function (var4, func8, var6, callback) {
          if (typeof func8 == "number") {
            if (var4 < 2) {
              this.fromInt(1);
            } else {
              this.fromNumber(var4, var6);
              if (!this.testBit(var4 - 1)) {
                this.bitwiseTo(BigInteger.ONE.shiftLeft(var4 - 1), op_or, this);
              }
              if (this.isEven()) {
                this.dAddOffset(1, 0);
              }
              var bnp_1 = this;
              function bnpfn1_1() {
                bnp_1.dAddOffset(2, 0);
                if (bnp_1.bitLength() > var4) {
                  bnp_1.subTo(BigInteger.ONE.shiftLeft(var4 - 1), bnp_1);
                }
                if (bnp_1.isProbablePrime(func8)) {
                  setTimeout(function () {
                    callback();
                  }, 0); // escape
                } else {
                  setTimeout(bnpfn1_1, 0);
                }
              }
              setTimeout(bnpfn1_1, 0);
            }
          } else {
            var func3 = [];
            var func2 = var4 & 7;
            func3.length = (var4 >> 3) + 1;
            func8.nextBytes(func3);
            if (func2 > 0) {
              func3[0] &= (1 << func2) - 1;
            } else {
              func3[0] = 0;
            }
            this.fromString(func3, 256);
          }
        };
        return BigInteger;
      }();
      //#region REDUCERS
      //#region NullExp
      var NullExp = /** @class */function () {
        function NullExp() {}
        // NullExp.prototype.convert = nNop;
        NullExp.prototype.convert = function (func3) {
          return func3;
        };
        // NullExp.prototype.revert = nNop;
        NullExp.prototype.revert = function (func3) {
          return func3;
        };
        // NullExp.prototype.mulTo = nMulTo;
        NullExp.prototype.mulTo = function (func3, func5, func1) {
          func3.multiplyTo(func5, func1);
        };
        // NullExp.prototype.sqrTo = nSqrTo;
        NullExp.prototype.sqrTo = function (func3, func1) {
          func3.squareTo(func1);
        };
        return NullExp;
      }();
      // Modular reduction using "classic" algorithm
      var Classic = /** @class */function () {
        function Classic(var10) {
          this.var10 = var10;
        }
        // Classic.prototype.convert = cConvert;
        Classic.prototype.convert = function (func3) {
          if (func3.func7 < 0 || func3.compareTo(this.var10) >= 0) {
            return func3.mod(this.var10);
          } else {
            return func3;
          }
        };
        // Classic.prototype.revert = cRevert;
        Classic.prototype.revert = function (func3) {
          return func3;
        };
        // Classic.prototype.reduce = cReduce;
        Classic.prototype.reduce = function (func3) {
          func3.divRemTo(this.var10, null, func3);
        };
        // Classic.prototype.mulTo = cMulTo;
        Classic.prototype.mulTo = function (func3, func5, func1) {
          func3.multiplyTo(func5, func1);
          this.reduce(func1);
        };
        // Classic.prototype.sqrTo = cSqrTo;
        Classic.prototype.sqrTo = function (func3, func1) {
          func3.squareTo(func1);
          this.reduce(func1);
        };
        return Classic;
      }();
      //#endregion
      //#region Montgomery
      // Montgomery reduction
      var Montgomery = /** @class */function () {
        function Montgomery(var10) {
          this.var10 = var10;
          this.var51 = var10.invDigit();
          this.mpl = this.var51 & 32767;
          this.mph = this.var51 >> 15;
          this.var52 = (1 << var10.var17 - 15) - 1;
          this.var53 = var10.func2 * 2;
        }
        // Montgomery.prototype.convert = montConvert;
        // xR mod m
        Montgomery.prototype.convert = function (func3) {
          var func1 = nbi();
          func3.abs().dlShiftTo(this.var10.func2, func1);
          func1.divRemTo(this.var10, null, func1);
          if (func3.func7 < 0 && func1.compareTo(BigInteger.ZERO) > 0) {
            this.var10.subTo(func1, func1);
          }
          return func1;
        };
        // Montgomery.prototype.revert = montRevert;
        // x/R mod m
        Montgomery.prototype.revert = function (func3) {
          var func1 = nbi();
          func3.copyTo(func1);
          this.reduce(func1);
          return func1;
        };
        // Montgomery.prototype.reduce = montReduce;
        // x = x/R mod m (HAC 14.32)
        Montgomery.prototype.reduce = function (func3) {
          while (func3.func2 <= this.var53) {
            // pad x so am has enough room later
            func3[func3.func2++] = 0;
          }
          for (var var1 = 0; var1 < this.var10.func2; ++var1) {
            // faster way of calculating u0 = x[i]*mp mod DV
            var num = func3[var1] & 32767;
            var var54 = num * this.mpl + ((num * this.mph + (func3[var1] >> 15) * this.mpl & this.var52) << 15) & func3.var18;
            // use am to combine the multiply-shift-add into one call
            num = var1 + this.var10.func2;
            func3[num] += this.var10.var34(0, var54, func3, var1, 0, this.var10.func2);
            // propagate carry
            while (func3[num] >= func3.var20) {
              func3[num] -= func3.var20;
              func3[++num]++;
            }
          }
          func3.clamp();
          func3.drShiftTo(this.var10.func2, func3);
          if (func3.compareTo(this.var10) >= 0) {
            func3.subTo(this.var10, func3);
          }
        };
        // Montgomery.prototype.mulTo = montMulTo;
        // r = "xy/R mod m"; x,y != r
        Montgomery.prototype.mulTo = function (func3, func5, func1) {
          func3.multiplyTo(func5, func1);
          this.reduce(func1);
        };
        // Montgomery.prototype.sqrTo = montSqrTo;
        // r = "x^2/R mod m"; x != r
        Montgomery.prototype.sqrTo = function (func3, func1) {
          func3.squareTo(func1);
          this.reduce(func1);
        };
        return Montgomery;
      }();
      //#endregion Montgomery
      //#region Barrett
      // Barrett modular reduction
      var Barrett = /** @class */function () {
        function Barrett(var10) {
          this.var10 = var10;
          // setup Barrett
          this.var27 = nbi();
          this.var55 = nbi();
          BigInteger.ONE.dlShiftTo(var10.func2 * 2, this.var27);
          this.var56 = this.var27.divide(var10);
        }
        // Barrett.prototype.convert = barrettConvert;
        Barrett.prototype.convert = function (func3) {
          if (func3.func7 < 0 || func3.func2 > this.var10.func2 * 2) {
            return func3.mod(this.var10);
          } else if (func3.compareTo(this.var10) < 0) {
            return func3;
          } else {
            var func1 = nbi();
            func3.copyTo(func1);
            this.reduce(func1);
            return func1;
          }
        };
        // Barrett.prototype.revert = barrettRevert;
        Barrett.prototype.revert = function (func3) {
          return func3;
        };
        // Barrett.prototype.reduce = barrettReduce;
        // x = x mod m (HAC 14.42)
        Barrett.prototype.reduce = function (func3) {
          func3.drShiftTo(this.var10.func2 - 1, this.var27);
          if (func3.func2 > this.var10.func2 + 1) {
            func3.func2 = this.var10.func2 + 1;
            func3.clamp();
          }
          this.var56.multiplyUpperTo(this.var27, this.var10.func2 + 1, this.var55);
          this.var10.multiplyLowerTo(this.var55, this.var10.func2 + 1, this.var27);
          while (func3.compareTo(this.var27) < 0) {
            func3.dAddOffset(1, this.var10.func2 + 1);
          }
          func3.subTo(this.var27, func3);
          while (func3.compareTo(this.var10) >= 0) {
            func3.subTo(this.var10, func3);
          }
        };
        // Barrett.prototype.mulTo = barrettMulTo;
        // r = x*y mod m; x,y != r
        Barrett.prototype.mulTo = function (func3, func5, func1) {
          func3.multiplyTo(func5, func1);
          this.reduce(func1);
        };
        // Barrett.prototype.sqrTo = barrettSqrTo;
        // r = x^2 mod m; x != r
        Barrett.prototype.sqrTo = function (func3, func1) {
          func3.squareTo(func1);
          this.reduce(func1);
        };
        return Barrett;
      }();
      //#endregion
      //#endregion REDUCERS
      // return new, unset BigInteger
      function nbi() {
        return new BigInteger(null);
      }
      function parseBigInt(str, func1) {
        return new BigInteger(str, func1);
      }
      // am: Compute w_j += (x*this_i), propagate carries,
      // c is initial carry, returns final carry.
      // c < 3*dvalue, x < 2*dvalue, this_i < dvalue
      // We need to select the fastest one that works in this environment.
      // am1: use a single mult and divide to get the high bits,
      // max digit bits should be 26 because
      // max internal value = 2*dvalue^2-2*dvalue (< 2^53)
      function func11(var1, func3, var25, num, var6, var2) {
        while (--var2 >= 0) {
          var var7 = func3 * this[var1++] + var25[num] + var6;
          var6 = Math.floor(var7 / 67108864);
          var25[num++] = var7 & 67108863;
        }
        return var6;
      }
      // am2 avoids a big mult-and-extract completely.
      // Max digit bits should be <= 30 because we do bitwise ops
      // on values up to 2*hdvalue^2-hdvalue-1 (< 2^31)
      function func12(var1, func3, var25, num, var6, var2) {
        var var57 = func3 & 32767;
        var var58 = func3 >> 15;
        while (--var2 >= 0) {
          var var12 = this[var1] & 32767;
          var func6 = this[var1++] >> 15;
          var var10 = var58 * var12 + func6 * var57;
          var12 = var57 * var12 + ((var10 & 32767) << 15) + var25[num] + (var6 & 1073741823);
          var6 = (var12 >>> 30) + (var10 >>> 15) + var58 * func6 + (var6 >>> 30);
          var25[num++] = var12 & 1073741823;
        }
        return var6;
      }
      // Alternately, set max digit bits to 28 since some
      // browsers slow down when dealing with 32-bit numbers.
      function func13(var1, func3, var25, num, var6, var2) {
        var var57 = func3 & 16383;
        var var58 = func3 >> 14;
        while (--var2 >= 0) {
          var var12 = this[var1] & 16383;
          var func6 = this[var1++] >> 14;
          var var10 = var58 * var12 + func6 * var57;
          var12 = var57 * var12 + ((var10 & 16383) << 14) + var25[num] + var6;
          var6 = (var12 >> 28) + (var10 >> 14) + var58 * func6;
          var25[num++] = var12 & 268435455;
        }
        return var6;
      }
      if (navigator.appName == "Microsoft Internet Explorer") {
        BigInteger.prototype.var34 = func12;
        dbits = 30;
      } else if (navigator.appName != "Argfpncr") {
        BigInteger.prototype.var34 = func11;
        dbits = 26;
      } else {
        // Mozilla/Netscape seems to prefer am3
        BigInteger.prototype.var34 = func13;
        dbits = 28;
      }
      BigInteger.prototype.var17 = dbits;
      BigInteger.prototype.var18 = (1 << dbits) - 1;
      BigInteger.prototype.var20 = 1 << dbits;
      var BI_FP = 52;
      BigInteger.prototype.var45 = Math.pow(2, BI_FP);
      BigInteger.prototype.var42 = BI_FP - dbits;
      BigInteger.prototype.var43 = dbits * 2 - BI_FP;
      // Digit conversions
      var BI_RC = [];
      var var59;
      var var60;
      var59 = "0".charCodeAt(0);
      for (var60 = 0; var60 <= 9; ++var60) {
        BI_RC[var59++] = var60;
      }
      var59 = "a".charCodeAt(0);
      for (var60 = 10; var60 < 36; ++var60) {
        BI_RC[var59++] = var60;
      }
      var59 = "A".charCodeAt(0);
      for (var60 = 10; var60 < 36; ++var60) {
        BI_RC[var59++] = var60;
      }
      function intAt(func7, var1) {
        var var6 = BI_RC[func7.charCodeAt(var1)];
        if (var6 == null) {
          return -1;
        } else {
          return var6;
        }
      }
      // return bigint initialized to value
      function nbv(var1) {
        var func1 = nbi();
        func1.fromInt(var1);
        return func1;
      }
      // returns bit length of the integer x
      function nbits(func3) {
        var func1 = 1;
        var func2;
        if ((func2 = func3 >>> 16) != 0) {
          func3 = func2;
          func1 += 16;
        }
        if ((func2 = func3 >> 8) != 0) {
          func3 = func2;
          func1 += 8;
        }
        if ((func2 = func3 >> 4) != 0) {
          func3 = func2;
          func1 += 4;
        }
        if ((func2 = func3 >> 2) != 0) {
          func3 = func2;
          func1 += 2;
        }
        if ((func2 = func3 >> 1) != 0) {
          func3 = func2;
          func1 += 1;
        }
        return func1;
      }
      // "constants"
      BigInteger.ZERO = nbv(0);
      BigInteger.ONE = nbv(1);

      // prng4.js - uses Arcfour as a PRNG
      var Arcfour = /** @class */function () {
        function Arcfour() {
          this.var1 = 0;
          this.num = 0;
          this.var61 = [];
        }
        // Arcfour.prototype.init = ARC4init;
        // Initialize arcfour context from key, an array of ints, each from [0..255]
        Arcfour.prototype.init = function (key) {
          var var1;
          var num;
          var func2;
          for (var1 = 0; var1 < 256; ++var1) {
            this.var61[var1] = var1;
          }
          num = 0;
          for (var1 = 0; var1 < 256; ++var1) {
            num = num + this.var61[var1] + key[var1 % key.length] & 255;
            func2 = this.var61[var1];
            this.var61[var1] = this.var61[num];
            this.var61[num] = func2;
          }
          this.var1 = 0;
          this.num = 0;
        };
        // Arcfour.prototype.next = ARC4next;
        Arcfour.prototype.next = function () {
          var func2;
          this.var1 = this.var1 + 1 & 255;
          this.num = this.num + this.var61[this.var1] & 255;
          func2 = this.var61[this.var1];
          this.var61[this.var1] = this.var61[this.num];
          this.var61[this.num] = func2;
          return this.var61[func2 + this.var61[this.var1] & 255];
        };
        return Arcfour;
      }();
      // Plug in your RNG constructor here
      function prng_newstate() {
        return new Arcfour();
      }
      // Pool size must be a multiple of 4 and greater than 32.
      // An array of bytes the size of the pool will be passed to init()
      var rng_psize = 256;

      // Random number generator - requires a PRNG backend, e.g. prng4.js
      var rng_state;
      var rng_pool = null;
      var rng_pptr;
      // Initialize the pool with junk if needed.
      if (rng_pool == null) {
        rng_pool = [];
        rng_pptr = 0;
        var func2 = undefined;
        if (window.crypto && window.crypto.getRandomValues) {
          // Extract entropy (2048 bits) from RNG if available
          var var19 = new Uint32Array(256);
          window.crypto.getRandomValues(var19);
          for (func2 = 0; func2 < var19.length; ++func2) {
            rng_pool[rng_pptr++] = var19[func2] & 255;
          }
        }
        // Use mouse events for entropy, if we do not have enough entropy by the time
        // we need it, entropy will be generated by Math.random.
        function onMouseMoveListener_1(func14) {
          this.count = this.count || 0;
          if (this.count >= 256 || rng_pptr >= rng_psize) {
            if (window.removeEventListener) {
              window.removeEventListener("mousemove", onMouseMoveListener_1, false);
            } else if (window.detachEvent) {
              window.detachEvent("onmousemove", onMouseMoveListener_1);
            }
            return;
          }
          try {
            var mouseCoordinates = func14.func3 + func14.func5;
            rng_pool[rng_pptr++] = mouseCoordinates & 255;
            this.count += 1;
          } catch (func) {
            // Sometimes Firefox will deny permission to access event properties for some reason. Ignore.
          }
        }
        if (window.addEventListener) {
          window.addEventListener("mousemove", onMouseMoveListener_1, false);
        } else if (window.attachEvent) {
          window.attachEvent("onmousemove", onMouseMoveListener_1);
        }
      }
      function rng_get_byte() {
        if (rng_state == null) {
          rng_state = prng_newstate();
          // At this point, we may not have collected enough entropy.  If not, fall back to Math.random
          while (rng_pptr < rng_psize) {
            var random = Math.floor(Math.random() * 65536);
            rng_pool[rng_pptr++] = random & 255;
          }
          rng_state.init(rng_pool);
          for (rng_pptr = 0; rng_pptr < rng_pool.length; ++rng_pptr) {
            rng_pool[rng_pptr] = 0;
          }
          rng_pptr = 0;
        }
        // TODO: allow reseeding after first request
        return rng_state.next();
      }
      var SecureRandom = /** @class */function () {
        function SecureRandom() {}
        SecureRandom.prototype.nextBytes = function (func15) {
          for (var var1 = 0; var1 < func15.length; ++var1) {
            func15[var1] = rng_get_byte();
          }
        };
        return SecureRandom;
      }();

      // Depends on jsbn.js and rng.js
      // function linebrk(s,n) {
      //   var ret = "";
      //   var i = 0;
      //   while(i + n < s.length) {
      //     ret += s.substring(i,i+n) + "\n";
      //     i += n;
      //   }
      //   return ret + s.substring(i,s.length);
      // }
      // function byte2Hex(b) {
      //   if(b < 0x10)
      //     return "0" + b.toString(16);
      //   else
      //     return b.toString(16);
      // }
      function pkcs1pad1(func7, var2) {
        if (var2 < func7.length + 22) {
          console.error("Message too long for RSA");
          return null;
        }
        var len = var2 - func7.length - 6;
        var filler = "";
        for (var var3 = 0; var3 < len; var3 += 2) {
          filler += "ff";
        }
        var var10 = "0001" + filler + "00" + func7;
        return parseBigInt(var10, 16);
      }
      // PKCS#1 (type 2, random) pad input string s to n bytes, and return a bigint
      function pkcs1pad2(func7, var2) {
        if (var2 < func7.length + 11) {
          // TODO: fix for utf-8
          console.error("Message too long for RSA");
          return null;
        }
        var func15 = [];
        var var1 = func7.length - 1;
        while (var1 >= 0 && var2 > 0) {
          var var6 = func7.charCodeAt(var1--);
          if (var6 < 128) {
            // encode using utf-8
            func15[--var2] = var6;
          } else if (var6 > 127 && var6 < 2048) {
            func15[--var2] = var6 & 63 | 128;
            func15[--var2] = var6 >> 6 | 192;
          } else {
            func15[--var2] = var6 & 63 | 128;
            func15[--var2] = var6 >> 6 & 63 | 128;
            func15[--var2] = var6 >> 12 | 224;
          }
        }
        func15[--var2] = 0;
        var rng = new SecureRandom();
        var func3 = [];
        while (var2 > 2) {
          // random non-zero pad
          func3[0] = 0;
          while (func3[0] == 0) {
            rng.nextBytes(func3);
          }
          func15[--var2] = func3[0];
        }
        func15[--var2] = 2;
        func15[--var2] = 0;
        return new BigInteger(func15);
      }
      // "empty" RSA key constructor
      var RSAKey = /** @class */function () {
        function RSAKey() {
          this.var2 = null;
          this.func = 0;
          this.var5 = null;
          this.var8 = null;
          this.var22 = null;
          this.var62 = null;
          this.var63 = null;
          this.coeff = null;
        }
        //#region PROTECTED
        // protected
        // RSAKey.prototype.doPublic = RSADoPublic;
        // Perform raw public operation on "x": return x^e (mod n)
        RSAKey.prototype.doPublic = function (func3) {
          return func3.modPowInt(this.func, this.var2);
        };
        // RSAKey.prototype.doPrivate = RSADoPrivate;
        // Perform raw private operation on "x": return x^d (mod n)
        RSAKey.prototype.doPrivate = function (func3) {
          if (this.var8 == null || this.var22 == null) {
            return func3.modPow(this.var5, this.var2);
          }
          // TODO: re-calculate any missing CRT params
          var var64 = func3.mod(this.var8).modPow(this.var62, this.var8);
          var var65 = func3.mod(this.var22).modPow(this.var63, this.var22);
          while (var64.compareTo(var65) < 0) {
            var64 = var64.add(this.var8);
          }
          return var64.subtract(var65).multiply(this.coeff).mod(this.var8).multiply(this.var22).add(var65);
        };
        //#endregion PROTECTED
        //#region PUBLIC
        // RSAKey.prototype.setPublic = RSASetPublic;
        // Set the public key fields N and e from hex strings
        RSAKey.prototype.setPublic = function (func16, func17) {
          if (func16 != null && func17 != null && func16.length > 0 && func17.length > 0) {
            this.var2 = parseBigInt(func16, 16);
            this.func = parseInt(func17, 16);
          } else {
            console.error("Invalid RSA public key");
          }
        };
        // RSAKey.prototype.encrypt = RSAEncrypt;
        // Return the PKCS#1 RSA encryption of "text" as an even-length hex string
        RSAKey.prototype.encrypt = function (text) {
          var var10 = pkcs1pad2(text, this.var2.bitLength() + 7 >> 3);
          if (var10 == null) {
            return null;
          }
          var var6 = this.doPublic(var10);
          if (var6 == null) {
            return null;
          }
          var func6 = var6.toString(16);
          if ((func6.length & 1) == 0) {
            return func6;
          } else {
            return "0" + func6;
          }
        };
        /**
         * 长文本加密
         * @param {string} string 待加密长文本
         * @returns {string} 加密后的base64编码
         */
        RSAKey.prototype.encryptLong = function (text) {
          var _this = this;
          var maxLength = (this.var2.bitLength() + 7 >> 3) - 11;
          try {
            var ct_1 = "";
            if (text.length > maxLength) {
              var var66 = text.match(/.{1,117}/g);
              var66.forEach(function (entry) {
                var var67 = _this.encrypt(entry);
                ct_1 += var67;
              });
              return hex2b64(ct_1);
            }
            var func2 = this.encrypt(text);
            var func5 = hex2b64(func2);
            return func5;
          } catch (var68) {
            return false;
          }
        };
        /**
         * 长文本解密
         * @param {string} string 加密后的base64编码
         * @returns {string} 解密后的原文
         */
        RSAKey.prototype.decryptLong = function (text) {
          var _this = this;
          var maxLength = this.var2.bitLength() + 7 >> 3;
          text = b64tohex(text);
          try {
            if (text.length > maxLength) {
              var ct_2 = "";
              var var66 = text.match(/.{1,256}/g); // 128位解密。取256位
              var66.forEach(function (entry) {
                var var67 = _this.decrypt(entry);
                ct_2 += var67;
              });
              return ct_2;
            }
            var func5 = this.decrypt(text);
            return func5;
          } catch (var68) {
            return false;
          }
        };
        // RSAKey.prototype.setPrivate = RSASetPrivate;
        // Set the private key fields N, e, and d from hex strings
        RSAKey.prototype.setPrivate = function (func16, func17, func18) {
          if (func16 != null && func17 != null && func16.length > 0 && func17.length > 0) {
            this.var2 = parseBigInt(func16, 16);
            this.func = parseInt(func17, 16);
            this.var5 = parseBigInt(func18, 16);
          } else {
            console.error("Invalid RSA private key");
          }
        };
        // RSAKey.prototype.setPrivateEx = RSASetPrivateEx;
        // Set the private key fields N, e, d and CRT params from hex strings
        RSAKey.prototype.setPrivateEx = function (func16, func17, func18, func19, func20, func21, func22, func23) {
          if (func16 != null && func17 != null && func16.length > 0 && func17.length > 0) {
            this.var2 = parseBigInt(func16, 16);
            this.func = parseInt(func17, 16);
            this.var5 = parseBigInt(func18, 16);
            this.var8 = parseBigInt(func19, 16);
            this.var22 = parseBigInt(func20, 16);
            this.var62 = parseBigInt(func21, 16);
            this.var63 = parseBigInt(func22, 16);
            this.coeff = parseBigInt(func23, 16);
          } else {
            console.error("Invalid RSA private key");
          }
        };
        // RSAKey.prototype.generate = RSAGenerate;
        // Generate a new random private key B bits long, using public expt E
        RSAKey.prototype.generate = function (func24, func17) {
          var rng = new SecureRandom();
          var var69 = func24 >> 1;
          this.func = parseInt(func17, 16);
          var var70 = new BigInteger(func17, 16);
          while (true) {
            while (true) {
              this.var8 = new BigInteger(func24 - var69, 1, rng);
              if (this.var8.subtract(BigInteger.ONE).gcd(var70).compareTo(BigInteger.ONE) == 0 && this.var8.isProbablePrime(10)) {
                break;
              }
            }
            while (true) {
              this.var22 = new BigInteger(var69, 1, rng);
              if (this.var22.subtract(BigInteger.ONE).gcd(var70).compareTo(BigInteger.ONE) == 0 && this.var22.isProbablePrime(10)) {
                break;
              }
            }
            if (this.var8.compareTo(this.var22) <= 0) {
              var func2 = this.var8;
              this.var8 = this.var22;
              this.var22 = func2;
            }
            var var71 = this.var8.subtract(BigInteger.ONE);
            var var72 = this.var22.subtract(BigInteger.ONE);
            var phi = var71.multiply(var72);
            if (phi.gcd(var70).compareTo(BigInteger.ONE) == 0) {
              this.var2 = this.var8.multiply(this.var22);
              this.var5 = var70.modInverse(phi);
              this.var62 = this.var5.mod(var71);
              this.var63 = this.var5.mod(var72);
              this.coeff = this.var22.modInverse(this.var8);
              break;
            }
          }
        };
        // RSAKey.prototype.decrypt = RSADecrypt;
        // Return the PKCS#1 RSA decryption of "ctext".
        // "ctext" is an even-length hex string and the output is a plain string.
        RSAKey.prototype.decrypt = function (ctext) {
          var var6 = parseBigInt(ctext, 16);
          var var10 = this.doPrivate(var6);
          if (var10 == null) {
            return null;
          }
          return pkcs1unpad2(var10, this.var2.bitLength() + 7 >> 3);
        };
        // Generate a new random private key B bits long, using public expt E
        RSAKey.prototype.generateAsync = function (func24, func17, callback) {
          var rng = new SecureRandom();
          var var69 = func24 >> 1;
          this.func = parseInt(func17, 16);
          var var70 = new BigInteger(func17, 16);
          var rsa = this;
          // These functions have non-descript names because they were originally for(;;) loops.
          // I don't know about cryptography to give them better names than loop1-4.
          function loop1() {
            function loop4() {
              if (rsa.var8.compareTo(rsa.var22) <= 0) {
                var func2 = rsa.var8;
                rsa.var8 = rsa.var22;
                rsa.var22 = func2;
              }
              var var71 = rsa.var8.subtract(BigInteger.ONE);
              var var72 = rsa.var22.subtract(BigInteger.ONE);
              var phi = var71.multiply(var72);
              if (phi.gcd(var70).compareTo(BigInteger.ONE) == 0) {
                rsa.var2 = rsa.var8.multiply(rsa.var22);
                rsa.var5 = var70.modInverse(phi);
                rsa.var62 = rsa.var5.mod(var71);
                rsa.var63 = rsa.var5.mod(var72);
                rsa.coeff = rsa.var22.modInverse(rsa.var8);
                setTimeout(function () {
                  callback();
                }, 0); // escape
              } else {
                setTimeout(loop1, 0);
              }
            }
            function loop3() {
              rsa.var22 = nbi();
              rsa.var22.fromNumberAsync(var69, 1, rng, function () {
                rsa.var22.subtract(BigInteger.ONE).gcda(var70, function (func1) {
                  if (func1.compareTo(BigInteger.ONE) == 0 && rsa.var22.isProbablePrime(10)) {
                    setTimeout(loop4, 0);
                  } else {
                    setTimeout(loop3, 0);
                  }
                });
              });
            }
            function loop2() {
              rsa.var8 = nbi();
              rsa.var8.fromNumberAsync(func24 - var69, 1, rng, function () {
                rsa.var8.subtract(BigInteger.ONE).gcda(var70, function (func1) {
                  if (func1.compareTo(BigInteger.ONE) == 0 && rsa.var8.isProbablePrime(10)) {
                    setTimeout(loop3, 0);
                  } else {
                    setTimeout(loop2, 0);
                  }
                });
              });
            }
            setTimeout(loop2, 0);
          }
          setTimeout(loop1, 0);
        };
        RSAKey.prototype.sign = function (text, digestMethod, digestName) {
          var header = getDigestHeader(digestName);
          var digest = header + digestMethod(text).toString();
          var var10 = pkcs1pad1(digest, this.var2.bitLength() / 4);
          if (var10 == null) {
            return null;
          }
          var var6 = this.doPrivate(var10);
          if (var6 == null) {
            return null;
          }
          var func6 = var6.toString(16);
          if ((func6.length & 1) == 0) {
            return func6;
          } else {
            return "0" + func6;
          }
        };
        RSAKey.prototype.verify = function (text, signature, digestMethod) {
          var var6 = parseBigInt(signature, 16);
          var var10 = this.doPublic(var6);
          if (var10 == null) {
            return null;
          }
          var unpadded = var10.toString(16).replace(/^1f+00/, "");
          var digest = removeDigestHeader(unpadded);
          return digest == digestMethod(text).toString();
        };
        return RSAKey;
      }();
      // Undo PKCS#1 (type 2, random) padding and, if valid, return the plaintext
      function pkcs1unpad2(var5, var2) {
        var func8 = var5.toByteArray();
        var var1 = 0;
        while (var1 < func8.length && func8[var1] == 0) {
          ++var1;
        }
        if (func8.length - var1 != var2 - 1 || func8[var1] != 2) {
          return null;
        }
        ++var1;
        while (func8[var1] != 0) {
          if (++var1 >= func8.length) {
            return null;
          }
        }
        var ret = "";
        while (++var1 < func8.length) {
          var var6 = func8[var1] & 255;
          if (var6 < 128) {
            // utf-8 decode
            ret += String.fromCharCode(var6);
          } else if (var6 > 191 && var6 < 224) {
            ret += String.fromCharCode((var6 & 31) << 6 | func8[var1 + 1] & 63);
            ++var1;
          } else {
            ret += String.fromCharCode((var6 & 15) << 12 | (func8[var1 + 1] & 63) << 6 | func8[var1 + 2] & 63);
            var1 += 2;
          }
        }
        return ret;
      }
      // https://tools.ietf.org/html/rfc3447#page-43
      var DIGEST_HEADERS = {
        var73: "3020300c06082a864886f70d020205000410",
        var74: "3020300c06082a864886f70d020505000410",
        var75: "3021300906052b0e03021a05000414",
        var76: "302d300d06096086480165030402040500041c",
        var77: "3031300d060960864801650304020105000420",
        var78: "3041300d060960864801650304020205000430",
        var79: "3051300d060960864801650304020305000440",
        ripemd160: "3021300906052b2403020105000414"
      };
      function getDigestHeader(name) {
        return DIGEST_HEADERS[name] || "";
      }
      function removeDigestHeader(str) {
        for (var name_1 in DIGEST_HEADERS) {
          if (DIGEST_HEADERS.hasOwnProperty(name_1)) {
            var header = DIGEST_HEADERS[name_1];
            var len = header.length;
            if (str.substr(0, len) == header) {
              return str.substr(len);
            }
          }
        }
        return str;
      }
      // Return the PKCS#1 RSA encryption of "text" as a Base64-encoded string
      // function RSAEncryptB64(text) {
      //  var h = this.encrypt(text);
      //  if(h) return hex2b64(h); else return null;
      // }
      // public
      // RSAKey.prototype.encrypt_b64 = RSAEncryptB64;

      /*!
         Copyright (c) 2011, Yahoo! Inc. All rights reserved.
         Code licensed under the BSD License:
         http://developer.yahoo.com/yui/license.html
         version: 2.9.0
         */
      var YAHOO = {};
      YAHOO.lang = {
        /**
         * Utility to set up the prototype, constructor and superclass properties to
         * support an inheritance strategy that can chain constructors and methods.
         * Static members will not be inherited.
         *
         * @method extend
         * @static
         * @param {Function} subc   the object to modify
         * @param {Function} superc the object to inherit
         * @param {Object} overrides  additional properties/methods to add to the
         *                              subclass prototype.  These will override the
         *                              matching items obtained from the superclass
         *                              if present.
         */
        extend: function (subc, superc, overrides) {
          if (!superc || !subc) {
            throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");
          }
          function func25() {}
          func25.prototype = superc.prototype;
          subc.prototype = new func25();
          subc.prototype.constructor = subc;
          subc.superclass = superc.prototype;
          if (superc.prototype.constructor == Object.prototype.constructor) {
            superc.prototype.constructor = superc;
          }
          if (overrides) {
            var var1;
            for (var1 in overrides) {
              subc.prototype[var1] = overrides[var1];
            }

            /*
                   * IE will not enumerate native functions in a derived object even if the
                   * function was overridden.  This is a workaround for specific functions
                   * we care about on the Object prototype.
                   * @property _IEEnumFix
                   * @param {Function} r  the object to receive the augmentation
                   * @param {Function} s  the object that supplies the properties to augment
                   * @static
                   * @private
                   */
            function _IEEnumFix() {}
            var ADD = ["gbFgevat", "valueOf"];
            try {
              if (/MSIE/.test(navigator.userAgent)) {
                _IEEnumFix = function (func1, func7) {
                  for (var1 = 0; var1 < ADD.length; var1 = var1 + 1) {
                    var fname = ADD[var1];
                    var var3 = func7[fname];
                    if (typeof var3 === "shapgvba" && var3 != Object.prototype[fname]) {
                      func1[fname] = var3;
                    }
                  }
                };
              }
            } catch (var68) {}
            _IEEnumFix(subc.prototype, overrides);
          }
        }
      };

      /* asn1-1.0.13.js (c) 2013-2017 Kenji Urushima | kjur.github.com/jsrsasign/license
          */

      /**
       * @fileOverview
       * @name asn1-1.0.js
       * <AUTHOR>
       * @version asn1 1.0.13 (2017-Jun-02)
       * @since jsrsasign 2.1
       * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
       */

      /**
       * kjur's class library name space
       * <p>
       * This name space provides following name spaces:
       * <ul>
       * <li>{@link KJUR.asn1} - ASN.1 primitive hexadecimal encoder</li>
       * <li>{@link KJUR.asn1.x509} - ASN.1 structure for X.509 certificate and CRL</li>
       * <li>{@link KJUR.crypto} - Java Cryptographic Extension(JCE) style MessageDigest/Signature
       * class and utilities</li>
       * </ul>
       * </p>
       * NOTE: Please ignore method summary and document of this namespace. This caused by a bug of jsdoc2.
       * @name KJUR
       * @namespace kjur's class library name space
       */
      var KJUR = {};

      /**
       * kjur's ASN.1 class library name space
       * <p>
       * This is ITU-T X.690 ASN.1 DER encoder class library and
       * class structure and methods is very similar to
       * org.bouncycastle.asn1 package of
       * well known BouncyCaslte Cryptography Library.
       * <h4>PROVIDING ASN.1 PRIMITIVES</h4>
       * Here are ASN.1 DER primitive classes.
       * <ul>
       * <li>0x01 {@link KJUR.asn1.DERBoolean}</li>
       * <li>0x02 {@link KJUR.asn1.DERInteger}</li>
       * <li>0x03 {@link KJUR.asn1.DERBitString}</li>
       * <li>0x04 {@link KJUR.asn1.DEROctetString}</li>
       * <li>0x05 {@link KJUR.asn1.DERNull}</li>
       * <li>0x06 {@link KJUR.asn1.DERObjectIdentifier}</li>
       * <li>0x0a {@link KJUR.asn1.DEREnumerated}</li>
       * <li>0x0c {@link KJUR.asn1.DERUTF8String}</li>
       * <li>0x12 {@link KJUR.asn1.DERNumericString}</li>
       * <li>0x13 {@link KJUR.asn1.DERPrintableString}</li>
       * <li>0x14 {@link KJUR.asn1.DERTeletexString}</li>
       * <li>0x16 {@link KJUR.asn1.DERIA5String}</li>
       * <li>0x17 {@link KJUR.asn1.DERUTCTime}</li>
       * <li>0x18 {@link KJUR.asn1.DERGeneralizedTime}</li>
       * <li>0x30 {@link KJUR.asn1.DERSequence}</li>
       * <li>0x31 {@link KJUR.asn1.DERSet}</li>
       * </ul>
       * <h4>OTHER ASN.1 CLASSES</h4>
       * <ul>
       * <li>{@link KJUR.asn1.ASN1Object}</li>
       * <li>{@link KJUR.asn1.DERAbstractString}</li>
       * <li>{@link KJUR.asn1.DERAbstractTime}</li>
       * <li>{@link KJUR.asn1.DERAbstractStructured}</li>
       * <li>{@link KJUR.asn1.DERTaggedObject}</li>
       * </ul>
       * <h4>SUB NAME SPACES</h4>
       * <ul>
       * <li>{@link KJUR.asn1.cades} - CAdES long term signature format</li>
       * <li>{@link KJUR.asn1.cms} - Cryptographic Message Syntax</li>
       * <li>{@link KJUR.asn1.csr} - Certificate Signing Request (CSR/PKCS#10)</li>
       * <li>{@link KJUR.asn1.tsp} - RFC 3161 Timestamping Protocol Format</li>
       * <li>{@link KJUR.asn1.x509} - RFC 5280 X.509 certificate and CRL</li>
       * </ul>
       * </p>
       * NOTE: Please ignore method summary and document of this namespace.
       * This caused by a bug of jsdoc2.
       * @name KJUR.asn1
       * @namespace
       */
      if (typeof KJUR.var80 == "undefined" || !KJUR.var80) {
        KJUR.var80 = {};
      }

      /**
       * ASN1 utilities class
       * @name KJUR.asn1.ASN1Util
       * @class ASN1 utilities class
       * @since asn1 1.0.2
       */
      KJUR.var80.ASN1Util = new function () {
        this.integerToByteHex = function (var1) {
          var func6 = var1.toString(16);
          if (func6.length % 2 == 1) {
            func6 = "0" + func6;
          }
          return func6;
        };
        this.bigIntToMinTwosComplementsHex = function (bigIntegerValue) {
          var func6 = bigIntegerValue.toString(16);
          if (func6.substr(0, 1) != "-") {
            if (func6.length % 2 == 1) {
              func6 = "0" + func6;
            } else if (!func6.match(/^[0-7]/)) {
              func6 = "00" + func6;
            }
          } else {
            var hPos = func6.substr(1);
            var xorLen = hPos.length;
            if (xorLen % 2 == 1) {
              xorLen += 1;
            } else if (!func6.match(/^[0-7]/)) {
              xorLen += 2;
            }
            var hMask = "";
            for (var var1 = 0; var1 < xorLen; var1++) {
              hMask += "f";
            }
            var biMask = new BigInteger(hMask, 16);
            var biNeg = biMask.xor(bigIntegerValue).add(BigInteger.ONE);
            func6 = biNeg.toString(16).replace(/^-/, "");
          }
          return func6;
        };
        /**
         * get PEM string from hexadecimal data and header string
         * @name getPEMStringFromHex
         * @memberOf KJUR.asn1.ASN1Util
         * @function
         * @param {String} dataHex hexadecimal string of PEM body
         * @param {String} pemHeader PEM header string (ex. 'RSA PRIVATE KEY')
         * @return {String} PEM formatted string of input data
         * @description
         * This method converts a hexadecimal string to a PEM string with
         * a specified header. Its line break will be CRLF("\r\n").
         * @example
         * var pem  = KJUR.asn1.ASN1Util.getPEMStringFromHex('616161', 'RSA PRIVATE KEY');
         * // value of pem will be:
         * -----BEGIN PRIVATE KEY-----
         * YWFh
         * -----END PRIVATE KEY-----
         */
        this.getPEMStringFromHex = function (dataHex, pemHeader) {
          return hextopem(dataHex, pemHeader);
        };

        /**
         * generate ASN1Object specifed by JSON parameters
         * @name newObject
         * @memberOf KJUR.asn1.ASN1Util
         * @function
         * @param {Array} param JSON parameter to generate ASN1Object
         * @return {KJUR.asn1.ASN1Object} generated object
         * @since asn1 1.0.3
         * @description
         * generate any ASN1Object specified by JSON param
         * including ASN.1 primitive or structured.
         * Generally 'param' can be described as follows:
         * <blockquote>
         * {TYPE-OF-ASNOBJ: ASN1OBJ-PARAMETER}
         * </blockquote>
         * 'TYPE-OF-ASN1OBJ' can be one of following symbols:
         * <ul>
         * <li>'bool' - DERBoolean</li>
         * <li>'int' - DERInteger</li>
         * <li>'bitstr' - DERBitString</li>
         * <li>'octstr' - DEROctetString</li>
         * <li>'null' - DERNull</li>
         * <li>'oid' - DERObjectIdentifier</li>
         * <li>'enum' - DEREnumerated</li>
         * <li>'utf8str' - DERUTF8String</li>
         * <li>'numstr' - DERNumericString</li>
         * <li>'prnstr' - DERPrintableString</li>
         * <li>'telstr' - DERTeletexString</li>
         * <li>'ia5str' - DERIA5String</li>
         * <li>'utctime' - DERUTCTime</li>
         * <li>'gentime' - DERGeneralizedTime</li>
         * <li>'seq' - DERSequence</li>
         * <li>'set' - DERSet</li>
         * <li>'tag' - DERTaggedObject</li>
         * </ul>
         * @example
         * newObject({'prnstr': 'aaa'});
         * newObject({'seq': [{'int': 3}, {'prnstr': 'aaa'}]})
         * // ASN.1 Tagged Object
         * newObject({'tag': {'tag': 'a1',
         *                    'explicit': true,
         *                    'obj': {'seq': [{'int': 3}, {'prnstr': 'aaa'}]}}});
         * // more simple representation of ASN.1 Tagged Object
         * newObject({'tag': ['a1',
         *                    true,
         *                    {'seq': [
         *                      {'int': 3},
         *                      {'prnstr': 'aaa'}]}
         *                   ]});
         */
        this.newObject = function (param) {
          var _KJUR = KJUR;
          var _KJUR_asn1 = _KJUR.var80;
          var _DERBoolean = _KJUR_asn1.DERBoolean;
          var _DERInteger = _KJUR_asn1.DERInteger;
          var _DERBitString = _KJUR_asn1.DERBitString;
          var _DEROctetString = _KJUR_asn1.DEROctetString;
          var _DERNull = _KJUR_asn1.DERNull;
          var _DERObjectIdentifier = _KJUR_asn1.DERObjectIdentifier;
          var _DEREnumerated = _KJUR_asn1.DEREnumerated;
          var _DERUTF8String = _KJUR_asn1.DERUTF8String;
          var _DERNumericString = _KJUR_asn1.DERNumericString;
          var _DERPrintableString = _KJUR_asn1.DERPrintableString;
          var _DERTeletexString = _KJUR_asn1.DERTeletexString;
          var _DERIA5String = _KJUR_asn1.DERIA5String;
          var _DERUTCTime = _KJUR_asn1.DERUTCTime;
          var _DERGeneralizedTime = _KJUR_asn1.DERGeneralizedTime;
          var _DERSequence = _KJUR_asn1.DERSequence;
          var _DERSet = _KJUR_asn1.DERSet;
          var _DERTaggedObject = _KJUR_asn1.DERTaggedObject;
          var _newObject = _KJUR_asn1.ASN1Util.newObject;
          var keys = Object.keys(param);
          if (keys.length != 1) {
            throw "key of param shall be only one.";
          }
          var key = keys[0];
          if (":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":" + key + ":") == -1) {
            throw "undefined key: " + key;
          }
          if (key == "obby") {
            return new _DERBoolean(param[key]);
          }
          if (key == "int") {
            return new _DERInteger(param[key]);
          }
          if (key == "bitstr") {
            return new _DERBitString(param[key]);
          }
          if (key == "octstr") {
            return new _DEROctetString(param[key]);
          }
          if (key == "ahyy") {
            return new _DERNull(param[key]);
          }
          if (key == "oid") {
            return new _DERObjectIdentifier(param[key]);
          }
          if (key == "rahz") {
            return new _DEREnumerated(param[key]);
          }
          if (key == "utf8str") {
            return new _DERUTF8String(param[key]);
          }
          if (key == "numstr") {
            return new _DERNumericString(param[key]);
          }
          if (key == "prnstr") {
            return new _DERPrintableString(param[key]);
          }
          if (key == "telstr") {
            return new _DERTeletexString(param[key]);
          }
          if (key == "ia5str") {
            return new _DERIA5String(param[key]);
          }
          if (key == "utctime") {
            return new _DERUTCTime(param[key]);
          }
          if (key == "gentime") {
            return new _DERGeneralizedTime(param[key]);
          }
          if (key == "seq") {
            var paramList = param[key];
            var var4 = [];
            for (var var1 = 0; var1 < paramList.length; var1++) {
              var asn1Obj = _newObject(paramList[var1]);
              var4.push(asn1Obj);
            }
            return new _DERSequence({
              array: var4
            });
          }
          if (key == "set") {
            var paramList = param[key];
            var var4 = [];
            for (var var1 = 0; var1 < paramList.length; var1++) {
              var asn1Obj = _newObject(paramList[var1]);
              var4.push(asn1Obj);
            }
            return new _DERSet({
              array: var4
            });
          }
          if (key == "tag") {
            var tagParam = param[key];
            if (Object.prototype.toString.call(tagParam) === "[object Array]" && tagParam.length == 3) {
              var obj = _newObject(tagParam[2]);
              return new _DERTaggedObject({
                tag: tagParam[0],
                explicit: tagParam[1],
                obj: obj
              });
            } else {
              var newParam = {};
              if (tagParam.explicit !== undefined) {
                newParam.explicit = tagParam.explicit;
              }
              if (tagParam.tag !== undefined) {
                newParam.tag = tagParam.tag;
              }
              if (tagParam.obj === undefined) {
                throw "obj shall be specified for 'tag'.";
              }
              newParam.obj = _newObject(tagParam.obj);
              return new _DERTaggedObject(newParam);
            }
          }
        };

        /**
         * get encoded hexadecimal string of ASN1Object specifed by JSON parameters
         * @name jsonToASN1HEX
         * @memberOf KJUR.asn1.ASN1Util
         * @function
         * @param {Array} param JSON parameter to generate ASN1Object
         * @return hexadecimal string of ASN1Object
         * @since asn1 1.0.4
         * @description
         * As for ASN.1 object representation of JSON object,
         * please see {@link newObject}.
         * @example
         * jsonToASN1HEX({'prnstr': 'aaa'});
         */
        this.jsonToASN1HEX = function (param) {
          var asn1Obj = this.newObject(param);
          return asn1Obj.getEncodedHex();
        };
      }();

      /**
       * get dot noted oid number string from hexadecimal value of OID
       * @name oidHexToInt
       * @memberOf KJUR.asn1.ASN1Util
       * @function
       * @param {String} hex hexadecimal value of object identifier
       * @return {String} dot noted string of object identifier
       * @since jsrsasign 4.8.3 asn1 1.0.7
       * @description
       * This static method converts from hexadecimal string representation of
       * ASN.1 value of object identifier to oid number string.
       * @example
       * KJUR.asn1.ASN1Util.oidHexToInt('550406') &rarr; "*******"
       */
      KJUR.var80.ASN1Util.oidHexToInt = function (hex) {
        var func7 = "";
        var var81 = parseInt(hex.substr(0, 2), 16);
        var var82 = Math.floor(var81 / 40);
        var var83 = var81 % 40;
        var func7 = var82 + "." + var83;
        var binbuf = "";
        for (var var1 = 2; var1 < hex.length; var1 += 2) {
          var value = parseInt(hex.substr(var1, 2), 16);
          var bin = ("00000000" + value.toString(2)).slice(-8);
          binbuf = binbuf + bin.substr(1, 7);
          if (bin.substr(0, 1) == "0") {
            var var84 = new BigInteger(binbuf, 2);
            func7 = func7 + "." + var84.toString(10);
            binbuf = "";
          }
        }
        return func7;
      };

      /**
       * get hexadecimal value of object identifier from dot noted oid value
       * @name oidIntToHex
       * @memberOf KJUR.asn1.ASN1Util
       * @function
       * @param {String} oidString dot noted string of object identifier
       * @return {String} hexadecimal value of object identifier
       * @since jsrsasign 4.8.3 asn1 1.0.7
       * @description
       * This static method converts from object identifier value string.
       * to hexadecimal string representation of it.
       * @example
       * KJUR.asn1.ASN1Util.oidIntToHex("*******") &rarr; "550406"
       */
      KJUR.var80.ASN1Util.oidIntToHex = function (oidString) {
        function itox(var1) {
          var func6 = var1.toString(16);
          if (func6.length == 1) {
            func6 = "0" + func6;
          }
          return func6;
        }
        function roidtox(roid) {
          var func6 = "";
          var var84 = new BigInteger(roid, 10);
          var func8 = var84.toString(2);
          var padLen = 7 - func8.length % 7;
          if (padLen == 7) {
            padLen = 0;
          }
          var bPad = "";
          for (var var1 = 0; var1 < padLen; var1++) {
            bPad += "0";
          }
          func8 = bPad + func8;
          for (var var1 = 0; var1 < func8.length - 1; var1 += 7) {
            var var85 = func8.substr(var1, 7);
            if (var1 != func8.length - 7) {
              var85 = "1" + var85;
            }
            func6 += itox(parseInt(var85, 2));
          }
          return func6;
        }
        if (!oidString.match(/^[0-9.]+$/)) {
          throw "malformed oid string: " + oidString;
        }
        var func6 = "";
        var var4 = oidString.split(".");
        var var82 = parseInt(var4[0]) * 40 + parseInt(var4[1]);
        func6 += itox(var82);
        var4.splice(0, 2);
        for (var var1 = 0; var1 < var4.length; var1++) {
          func6 += roidtox(var4[var1]);
        }
        return func6;
      };

      // ********************************************************************
      //  Abstract ASN.1 Classes
      // ********************************************************************

      // ********************************************************************

      /**
       * base class for ASN.1 DER encoder object
       * @name KJUR.asn1.ASN1Object
       * @class base class for ASN.1 DER encoder object
       * @property {Boolean} isModified flag whether internal data was changed
       * @property {String} hTLV hexadecimal string of ASN.1 TLV
       * @property {String} hT hexadecimal string of ASN.1 TLV tag(T)
       * @property {String} hL hexadecimal string of ASN.1 TLV length(L)
       * @property {String} hV hexadecimal string of ASN.1 TLV value(V)
       * @description
       */
      KJUR.var80.ASN1Object = function () {
        var str1 = "";

        /**
         * get hexadecimal ASN.1 TLV length(L) bytes from TLV value(V)
         * @name getLengthHexFromValue
         * @memberOf KJUR.asn1.ASN1Object#
         * @function
         * @return {String} hexadecimal string of ASN.1 TLV length(L)
         */
        this.getLengthHexFromValue = function () {
          if (typeof this.str1 == "undefined" || this.str1 == null) {
            throw "this.hV is null or undefined.";
          }
          if (this.str1.length % 2 == 1) {
            throw "value hex must be even length: n=" + str1.length + ",v=" + this.str1;
          }
          var var2 = this.str1.length / 2;
          var var86 = var2.toString(16);
          if (var86.length % 2 == 1) {
            var86 = "0" + var86;
          }
          if (var2 < 128) {
            return var86;
          } else {
            var hNlen = var86.length / 2;
            if (hNlen > 15) {
              throw "ASN.1 length too long to represent by 8x: n = " + var2.toString(16);
            }
            var head = 128 + hNlen;
            return head.toString(16) + var86;
          }
        };

        /**
         * get hexadecimal string of ASN.1 TLV bytes
         * @name getEncodedHex
         * @memberOf KJUR.asn1.ASN1Object#
         * @function
         * @return {String} hexadecimal string of ASN.1 TLV
         */
        this.getEncodedHex = function () {
          if (this.hTLV == null || this.isModified) {
            this.str1 = this.getFreshValueHex();
            this.var87 = this.getLengthHexFromValue();
            this.hTLV = this.var88 + this.var87 + this.str1;
            this.isModified = false;
            //alert("first time: " + this.hTLV);
          }
          return this.hTLV;
        };

        /**
         * get hexadecimal string of ASN.1 TLV value(V) bytes
         * @name getValueHex
         * @memberOf KJUR.asn1.ASN1Object#
         * @function
         * @return {String} hexadecimal string of ASN.1 TLV value(V) bytes
         */
        this.getValueHex = function () {
          this.getEncodedHex();
          return this.str1;
        };
        this.getFreshValueHex = function () {
          return "";
        };
      };

      // == BEGIN DERAbstractString ================================================
      /**
       * base class for ASN.1 DER string classes
       * @name KJUR.asn1.DERAbstractString
       * @class base class for ASN.1 DER string classes
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @property {String} s internal string of value
       * @extends KJUR.asn1.ASN1Object
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>str - specify initial ASN.1 value(V) by a string</li>
       * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>
       * </ul>
       * NOTE: 'params' can be omitted.
       */
      KJUR.var80.DERAbstractString = function (params) {
        KJUR.var80.DERAbstractString.superclass.constructor.call(this);

        /**
         * get string value of this string object
         * @name getString
         * @memberOf KJUR.asn1.DERAbstractString#
         * @function
         * @return {String} string value of this string object
         */
        this.getString = function () {
          return this.func7;
        };

        /**
         * set value by a string
         * @name setString
         * @memberOf KJUR.asn1.DERAbstractString#
         * @function
         * @param {String} newS value by a string to set
         */
        this.setString = function (newS) {
          this.hTLV = null;
          this.isModified = true;
          this.func7 = newS;
          this.str1 = stohex(this.func7);
        };

        /**
         * set value by a hexadecimal string
         * @name setStringHex
         * @memberOf KJUR.asn1.DERAbstractString#
         * @function
         * @param {String} newHexString value by a hexadecimal string to set
         */
        this.setStringHex = function (newHexString) {
          this.hTLV = null;
          this.isModified = true;
          this.func7 = null;
          this.str1 = newHexString;
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
        if (typeof params != "undefined") {
          if (typeof params == "string") {
            this.setString(params);
          } else if (typeof params.str != "undefined") {
            this.setString(params.str);
          } else if (typeof params.hex != "undefined") {
            this.setStringHex(params.hex);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERAbstractString, KJUR.var80.ASN1Object);
      // == END   DERAbstractString ================================================

      // == BEGIN DERAbstractTime ==================================================
      /**
       * base class for ASN.1 DER Generalized/UTCTime class
       * @name KJUR.asn1.DERAbstractTime
       * @class base class for ASN.1 DER Generalized/UTCTime class
       * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})
       * @extends KJUR.asn1.ASN1Object
       * @description
       * @see KJUR.asn1.ASN1Object - superclass
       */
      KJUR.var80.DERAbstractTime = function (params) {
        KJUR.var80.DERAbstractTime.superclass.constructor.call(this);

        // --- PRIVATE METHODS --------------------
        this.localDateToUTC = function (var5) {
          utc = var5.getTime() + var5.getTimezoneOffset() * 60000;
          var utcDate = new Date(utc);
          return utcDate;
        };

        /*
             * format date string by Data object
             * @name formatDate
             * @memberOf KJUR.asn1.AbstractTime;
             * @param {Date} dateObject
             * @param {string} type 'utc' or 'gen'
             * @param {boolean} withMillis flag for with millisections or not
             * @description
             * 'withMillis' flag is supported from asn1 1.0.6.
             */
        this.formatDate = function (dateObject, type, withMillis) {
          var pad = this.zeroPadding;
          var var5 = this.localDateToUTC(dateObject);
          var year = String(var5.getFullYear());
          if (type == "utc") {
            year = year.substr(2, 2);
          }
          var month = pad(String(var5.getMonth() + 1), 2);
          var day = pad(String(var5.getDate()), 2);
          var hour = pad(String(var5.getHours()), 2);
          var min = pad(String(var5.getMinutes()), 2);
          var sec = pad(String(var5.getSeconds()), 2);
          var func7 = year + month + day + hour + min + sec;
          if (withMillis === true) {
            var millis = var5.getMilliseconds();
            if (millis != 0) {
              var sMillis = pad(String(millis), 3);
              sMillis = sMillis.replace(/[0]+$/, "");
              func7 = func7 + "." + sMillis;
            }
          }
          return func7 + "Z";
        };
        this.zeroPadding = function (func7, len) {
          if (func7.length >= len) {
            return func7;
          }
          return new Array(len - func7.length + 1).join("0") + func7;
        };

        // --- PUBLIC METHODS --------------------
        /**
         * get string value of this string object
         * @name getString
         * @memberOf KJUR.asn1.DERAbstractTime#
         * @function
         * @return {String} string value of this time object
         */
        this.getString = function () {
          return this.func7;
        };

        /**
         * set value by a string
         * @name setString
         * @memberOf KJUR.asn1.DERAbstractTime#
         * @function
         * @param {String} newS value by a string to set such like "130430235959Z"
         */
        this.setString = function (newS) {
          this.hTLV = null;
          this.isModified = true;
          this.func7 = newS;
          this.str1 = stohex(newS);
        };

        /**
         * set value by a Date object
         * @name setByDateValue
         * @memberOf KJUR.asn1.DERAbstractTime#
         * @function
         * @param {Integer} year year of date (ex. 2013)
         * @param {Integer} month month of date between 1 and 12 (ex. 12)
         * @param {Integer} day day of month
         * @param {Integer} hour hours of date
         * @param {Integer} min minutes of date
         * @param {Integer} sec seconds of date
         */
        this.setByDateValue = function (year, month, day, hour, min, sec) {
          var dateObject = new Date(Date.UTC(year, month - 1, day, hour, min, sec, 0));
          this.setByDate(dateObject);
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
      };
      YAHOO.lang.extend(KJUR.var80.DERAbstractTime, KJUR.var80.ASN1Object);
      // == END   DERAbstractTime ==================================================

      // == BEGIN DERAbstractStructured ============================================
      /**
       * base class for ASN.1 DER structured class
       * @name KJUR.asn1.DERAbstractStructured
       * @class base class for ASN.1 DER structured class
       * @property {Array} asn1Array internal array of ASN1Object
       * @extends KJUR.asn1.ASN1Object
       * @description
       * @see KJUR.asn1.ASN1Object - superclass
       */
      KJUR.var80.DERAbstractStructured = function (params) {
        KJUR.var80.DERAbstractString.superclass.constructor.call(this);

        /**
         * set value by array of ASN1Object
         * @name setByASN1ObjectArray
         * @memberOf KJUR.asn1.DERAbstractStructured#
         * @function
         * @param {array} asn1ObjectArray array of ASN1Object to set
         */
        this.setByASN1ObjectArray = function (asn1ObjectArray) {
          this.hTLV = null;
          this.isModified = true;
          this.asn1Array = asn1ObjectArray;
        };

        /**
         * append an ASN1Object to internal array
         * @name appendASN1Object
         * @memberOf KJUR.asn1.DERAbstractStructured#
         * @function
         * @param {ASN1Object} asn1Object to add
         */
        this.appendASN1Object = function (asn1Object) {
          this.hTLV = null;
          this.isModified = true;
          this.asn1Array.push(asn1Object);
        };
        this.asn1Array = new Array();
        if (typeof params != "undefined") {
          if (typeof params.array != "undefined") {
            this.asn1Array = params.array;
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERAbstractStructured, KJUR.var80.ASN1Object);

      // ********************************************************************
      //  ASN.1 Object Classes
      // ********************************************************************

      // ********************************************************************
      /**
       * class for ASN.1 DER Boolean
       * @name KJUR.asn1.DERBoolean
       * @class class for ASN.1 DER Boolean
       * @extends KJUR.asn1.ASN1Object
       * @description
       * @see KJUR.asn1.ASN1Object - superclass
       */
      KJUR.var80.DERBoolean = function () {
        KJUR.var80.DERBoolean.superclass.constructor.call(this);
        this.var88 = "01";
        this.hTLV = "0101ff";
      };
      YAHOO.lang.extend(KJUR.var80.DERBoolean, KJUR.var80.ASN1Object);

      // ********************************************************************
      /**
       * class for ASN.1 DER Integer
       * @name KJUR.asn1.DERInteger
       * @class class for ASN.1 DER Integer
       * @extends KJUR.asn1.ASN1Object
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>int - specify initial ASN.1 value(V) by integer value</li>
       * <li>bigint - specify initial ASN.1 value(V) by BigInteger object</li>
       * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>
       * </ul>
       * NOTE: 'params' can be omitted.
       */
      KJUR.var80.DERInteger = function (params) {
        KJUR.var80.DERInteger.superclass.constructor.call(this);
        this.var88 = "02";

        /**
         * set value by Tom Wu's BigInteger object
         * @name setByBigInteger
         * @memberOf KJUR.asn1.DERInteger#
         * @function
         * @param {BigInteger} bigIntegerValue to set
         */
        this.setByBigInteger = function (bigIntegerValue) {
          this.hTLV = null;
          this.isModified = true;
          this.str1 = KJUR.var80.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);
        };

        /**
         * set value by integer value
         * @name setByInteger
         * @memberOf KJUR.asn1.DERInteger
         * @function
         * @param {Integer} integer value to set
         */
        this.setByInteger = function (intValue) {
          var var84 = new BigInteger(String(intValue), 10);
          this.setByBigInteger(var84);
        };

        /**
         * set value by integer value
         * @name setValueHex
         * @memberOf KJUR.asn1.DERInteger#
         * @function
         * @param {String} hexadecimal string of integer value
         * @description
         * <br/>
         * NOTE: Value shall be represented by minimum octet length of
         * two's complement representation.
         * @example
         * new KJUR.asn1.DERInteger(123);
         * new KJUR.asn1.DERInteger({'int': 123});
         * new KJUR.asn1.DERInteger({'hex': '1fad'});
         */
        this.setValueHex = function (newHexString) {
          this.str1 = newHexString;
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
        if (typeof params != "undefined") {
          if (typeof params.bigint != "undefined") {
            this.setByBigInteger(params.bigint);
          } else if (typeof params.int != "undefined") {
            this.setByInteger(params.int);
          } else if (typeof params == "number") {
            this.setByInteger(params);
          } else if (typeof params.hex != "undefined") {
            this.setValueHex(params.hex);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERInteger, KJUR.var80.ASN1Object);

      // ********************************************************************
      /**
       * class for ASN.1 DER encoded BitString primitive
       * @name KJUR.asn1.DERBitString
       * @class class for ASN.1 DER encoded BitString primitive
       * @extends KJUR.asn1.ASN1Object
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>bin - specify binary string (ex. '10111')</li>
       * <li>array - specify array of boolean (ex. [true,false,true,true])</li>
       * <li>hex - specify hexadecimal string of ASN.1 value(V) including unused bits</li>
       * <li>obj - specify {@link KJUR.asn1.ASN1Util.newObject}
       * argument for "BitString encapsulates" structure.</li>
       * </ul>
       * NOTE1: 'params' can be omitted.<br/>
       * NOTE2: 'obj' parameter have been supported since
       * asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).<br/>
       * @example
       * // default constructor
       * o = new KJUR.asn1.DERBitString();
       * // initialize with binary string
       * o = new KJUR.asn1.DERBitString({bin: "1011"});
       * // initialize with boolean array
       * o = new KJUR.asn1.DERBitString({array: [true,false,true,true]});
       * // initialize with hexadecimal string (04 is unused bits)
       * o = new KJUR.asn1.DEROctetString({hex: "04bac0"});
       * // initialize with ASN1Util.newObject argument for encapsulated
       * o = new KJUR.asn1.DERBitString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});
       * // above generates a ASN.1 data like this:
       * // BIT STRING, encapsulates {
       * //   SEQUENCE {
       * //     INTEGER 3
       * //     PrintableString 'aaa'
       * //     }
       * //   }
       */
      KJUR.var80.DERBitString = function (params) {
        if (params !== undefined && typeof params.obj !== "undefined") {
          var var = KJUR.var80.ASN1Util.newObject(params.obj);
          params.hex = "00" + var.getEncodedHex();
        }
        KJUR.var80.DERBitString.superclass.constructor.call(this);
        this.var88 = "03";

        /**
         * set ASN.1 value(V) by a hexadecimal string including unused bits
         * @name setHexValueIncludingUnusedBits
         * @memberOf KJUR.asn1.DERBitString#
         * @function
         * @param {String} newHexStringIncludingUnusedBits
         */
        this.setHexValueIncludingUnusedBits = function (newHexStringIncludingUnusedBits) {
          this.hTLV = null;
          this.isModified = true;
          this.str1 = newHexStringIncludingUnusedBits;
        };

        /**
         * set ASN.1 value(V) by unused bit and hexadecimal string of value
         * @name setUnusedBitsAndHexValue
         * @memberOf KJUR.asn1.DERBitString#
         * @function
         * @param {Integer} unusedBits
         * @param {String} hValue
         */
        this.setUnusedBitsAndHexValue = function (unusedBits, hValue) {
          if (unusedBits < 0 || unusedBits > 7) {
            throw "unused bits shall be from 0 to 7: u = " + unusedBits;
          }
          var hUnusedBits = "0" + unusedBits;
          this.hTLV = null;
          this.isModified = true;
          this.str1 = hUnusedBits + hValue;
        };

        /**
         * set ASN.1 DER BitString by binary string<br/>
         * @name setByBinaryString
         * @memberOf KJUR.asn1.DERBitString#
         * @function
         * @param {String} binaryString binary value string (i.e. '10111')
         * @description
         * Its unused bits will be calculated automatically by length of
         * 'binaryValue'. <br/>
         * NOTE: Trailing zeros '0' will be ignored.
         * @example
         * o = new KJUR.asn1.DERBitString();
         * o.setByBooleanArray("01011");
         */
        this.setByBinaryString = function (binaryString) {
          binaryString = binaryString.replace(/0+$/, "");
          var unusedBits = 8 - binaryString.length % 8;
          if (unusedBits == 8) {
            unusedBits = 0;
          }
          for (var var1 = 0; var1 <= unusedBits; var1++) {
            binaryString += "0";
          }
          var func6 = "";
          for (var var1 = 0; var1 < binaryString.length - 1; var1 += 8) {
            var func8 = binaryString.substr(var1, 8);
            var func3 = parseInt(func8, 2).toString(16);
            if (func3.length == 1) {
              func3 = "0" + func3;
            }
            func6 += func3;
          }
          this.hTLV = null;
          this.isModified = true;
          this.str1 = "0" + unusedBits + func6;
        };

        /**
         * set ASN.1 TLV value(V) by an array of boolean<br/>
         * @name setByBooleanArray
         * @memberOf KJUR.asn1.DERBitString#
         * @function
         * @param {array} booleanArray array of boolean (ex. [true, false, true])
         * @description
         * NOTE: Trailing falses will be ignored in the ASN.1 DER Object.
         * @example
         * o = new KJUR.asn1.DERBitString();
         * o.setByBooleanArray([false, true, false, true, true]);
         */
        this.setByBooleanArray = function (booleanArray) {
          var func7 = "";
          for (var var1 = 0; var1 < booleanArray.length; var1++) {
            if (booleanArray[var1] == true) {
              func7 += "1";
            } else {
              func7 += "0";
            }
          }
          this.setByBinaryString(func7);
        };

        /**
         * generate an array of falses with specified length<br/>
         * @name newFalseArray
         * @memberOf KJUR.asn1.DERBitString
         * @function
         * @param {Integer} nLength length of array to generate
         * @return {array} array of boolean falses
         * @description
         * This static method may be useful to initialize boolean array.
         * @example
         * o = new KJUR.asn1.DERBitString();
         * o.newFalseArray(3) &rarr; [false, false, false]
         */
        this.newFalseArray = function (nLength) {
          var var4 = new Array(nLength);
          for (var var1 = 0; var1 < nLength; var1++) {
            var4[var1] = false;
          }
          return var4;
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
        if (typeof params != "undefined") {
          if (typeof params == "string" && params.toLowerCase().match(/^[0-9a-f]+$/)) {
            this.setHexValueIncludingUnusedBits(params);
          } else if (typeof params.hex != "undefined") {
            this.setHexValueIncludingUnusedBits(params.hex);
          } else if (typeof params.bin != "undefined") {
            this.setByBinaryString(params.bin);
          } else if (typeof params.array != "undefined") {
            this.setByBooleanArray(params.array);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERBitString, KJUR.var80.ASN1Object);

      // ********************************************************************
      /**
       * class for ASN.1 DER OctetString<br/>
       * @name KJUR.asn1.DEROctetString
       * @class class for ASN.1 DER OctetString
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @extends KJUR.asn1.DERAbstractString
       * @description
       * This class provides ASN.1 OctetString simple type.<br/>
       * Supported "params" attributes are:
       * <ul>
       * <li>str - to set a string as a value</li>
       * <li>hex - to set a hexadecimal string as a value</li>
       * <li>obj - to set a encapsulated ASN.1 value by JSON object
       * which is defined in {@link KJUR.asn1.ASN1Util.newObject}</li>
       * </ul>
       * NOTE: A parameter 'obj' have been supported
       * for "OCTET STRING, encapsulates" structure.
       * since asn1 1.0.11, jsrsasign 6.1.1 (2016-Sep-25).
       * @see KJUR.asn1.DERAbstractString - superclass
       * @example
       * // default constructor
       * o = new KJUR.asn1.DEROctetString();
       * // initialize with string
       * o = new KJUR.asn1.DEROctetString({str: "aaa"});
       * // initialize with hexadecimal string
       * o = new KJUR.asn1.DEROctetString({hex: "616161"});
       * // initialize with ASN1Util.newObject argument
       * o = new KJUR.asn1.DEROctetString({obj: {seq: [{int: 3}, {prnstr: 'aaa'}]}});
       * // above generates a ASN.1 data like this:
       * // OCTET STRING, encapsulates {
       * //   SEQUENCE {
       * //     INTEGER 3
       * //     PrintableString 'aaa'
       * //     }
       * //   }
       */
      KJUR.var80.DEROctetString = function (params) {
        if (params !== undefined && typeof params.obj !== "undefined") {
          var var = KJUR.var80.ASN1Util.newObject(params.obj);
          params.hex = var.getEncodedHex();
        }
        KJUR.var80.DEROctetString.superclass.constructor.call(this, params);
        this.var88 = "04";
      };
      YAHOO.lang.extend(KJUR.var80.DEROctetString, KJUR.var80.DERAbstractString);

      // ********************************************************************
      /**
       * class for ASN.1 DER Null
       * @name KJUR.asn1.DERNull
       * @class class for ASN.1 DER Null
       * @extends KJUR.asn1.ASN1Object
       * @description
       * @see KJUR.asn1.ASN1Object - superclass
       */
      KJUR.var80.DERNull = function () {
        KJUR.var80.DERNull.superclass.constructor.call(this);
        this.var88 = "05";
        this.hTLV = "0500";
      };
      YAHOO.lang.extend(KJUR.var80.DERNull, KJUR.var80.ASN1Object);

      // ********************************************************************
      /**
       * class for ASN.1 DER ObjectIdentifier
       * @name KJUR.asn1.DERObjectIdentifier
       * @class class for ASN.1 DER ObjectIdentifier
       * @param {Array} params associative array of parameters (ex. {'oid': '*******'})
       * @extends KJUR.asn1.ASN1Object
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>oid - specify initial ASN.1 value(V) by a oid string (ex. ********)</li>
       * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>
       * </ul>
       * NOTE: 'params' can be omitted.
       */
      KJUR.var80.DERObjectIdentifier = function (params) {
        function itox(var1) {
          var func6 = var1.toString(16);
          if (func6.length == 1) {
            func6 = "0" + func6;
          }
          return func6;
        }
        function roidtox(roid) {
          var func6 = "";
          var var84 = new BigInteger(roid, 10);
          var func8 = var84.toString(2);
          var padLen = 7 - func8.length % 7;
          if (padLen == 7) {
            padLen = 0;
          }
          var bPad = "";
          for (var var1 = 0; var1 < padLen; var1++) {
            bPad += "0";
          }
          func8 = bPad + func8;
          for (var var1 = 0; var1 < func8.length - 1; var1 += 7) {
            var var85 = func8.substr(var1, 7);
            if (var1 != func8.length - 7) {
              var85 = "1" + var85;
            }
            func6 += itox(parseInt(var85, 2));
          }
          return func6;
        }
        KJUR.var80.DERObjectIdentifier.superclass.constructor.call(this);
        this.var88 = "06";

        /**
         * set value by a hexadecimal string
         * @name setValueHex
         * @memberOf KJUR.asn1.DERObjectIdentifier#
         * @function
         * @param {String} newHexString hexadecimal value of OID bytes
         */
        this.setValueHex = function (newHexString) {
          this.hTLV = null;
          this.isModified = true;
          this.func7 = null;
          this.str1 = newHexString;
        };

        /**
         * set value by a OID string<br/>
         * @name setValueOidString
         * @memberOf KJUR.asn1.DERObjectIdentifier#
         * @function
         * @param {String} oidString OID string (ex. ********)
         * @example
         * o = new KJUR.asn1.DERObjectIdentifier();
         * o.setValueOidString("********");
         */
        this.setValueOidString = function (oidString) {
          if (!oidString.match(/^[0-9.]+$/)) {
            throw "malformed oid string: " + oidString;
          }
          var func6 = "";
          var var4 = oidString.split(".");
          var var82 = parseInt(var4[0]) * 40 + parseInt(var4[1]);
          func6 += itox(var82);
          var4.splice(0, 2);
          for (var var1 = 0; var1 < var4.length; var1++) {
            func6 += roidtox(var4[var1]);
          }
          this.hTLV = null;
          this.isModified = true;
          this.func7 = null;
          this.str1 = func6;
        };

        /**
         * set value by a OID name
         * @name setValueName
         * @memberOf KJUR.asn1.DERObjectIdentifier#
         * @function
         * @param {String} oidName OID name (ex. 'serverAuth')
         * @since 1.0.1
         * @description
         * OID name shall be defined in 'KJUR.asn1.x509.OID.name2oidList'.
         * Otherwise raise error.
         * @example
         * o = new KJUR.asn1.DERObjectIdentifier();
         * o.setValueName("serverAuth");
         */
        this.setValueName = function (oidName) {
          var oid = KJUR.var80.var89.OID.name2oid(oidName);
          if (oid !== "") {
            this.setValueOidString(oid);
          } else {
            throw "DERObjectIdentifier oidName undefined: " + oidName;
          }
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
        if (params !== undefined) {
          if (typeof params === "string") {
            if (params.match(/^[0-2].[0-9.]+$/)) {
              this.setValueOidString(params);
            } else {
              this.setValueName(params);
            }
          } else if (params.oid !== undefined) {
            this.setValueOidString(params.oid);
          } else if (params.hex !== undefined) {
            this.setValueHex(params.hex);
          } else if (params.name !== undefined) {
            this.setValueName(params.name);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERObjectIdentifier, KJUR.var80.ASN1Object);

      // ********************************************************************
      /**
       * class for ASN.1 DER Enumerated
       * @name KJUR.asn1.DEREnumerated
       * @class class for ASN.1 DER Enumerated
       * @extends KJUR.asn1.ASN1Object
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>int - specify initial ASN.1 value(V) by integer value</li>
       * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>
       * </ul>
       * NOTE: 'params' can be omitted.
       * @example
       * new KJUR.asn1.DEREnumerated(123);
       * new KJUR.asn1.DEREnumerated({int: 123});
       * new KJUR.asn1.DEREnumerated({hex: '1fad'});
       */
      KJUR.var80.DEREnumerated = function (params) {
        KJUR.var80.DEREnumerated.superclass.constructor.call(this);
        this.var88 = "0a";

        /**
         * set value by Tom Wu's BigInteger object
         * @name setByBigInteger
         * @memberOf KJUR.asn1.DEREnumerated#
         * @function
         * @param {BigInteger} bigIntegerValue to set
         */
        this.setByBigInteger = function (bigIntegerValue) {
          this.hTLV = null;
          this.isModified = true;
          this.str1 = KJUR.var80.ASN1Util.bigIntToMinTwosComplementsHex(bigIntegerValue);
        };

        /**
         * set value by integer value
         * @name setByInteger
         * @memberOf KJUR.asn1.DEREnumerated#
         * @function
         * @param {Integer} integer value to set
         */
        this.setByInteger = function (intValue) {
          var var84 = new BigInteger(String(intValue), 10);
          this.setByBigInteger(var84);
        };

        /**
         * set value by integer value
         * @name setValueHex
         * @memberOf KJUR.asn1.DEREnumerated#
         * @function
         * @param {String} hexadecimal string of integer value
         * @description
         * <br/>
         * NOTE: Value shall be represented by minimum octet length of
         * two's complement representation.
         */
        this.setValueHex = function (newHexString) {
          this.str1 = newHexString;
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
        if (typeof params != "undefined") {
          if (typeof params.int != "undefined") {
            this.setByInteger(params.int);
          } else if (typeof params == "number") {
            this.setByInteger(params);
          } else if (typeof params.hex != "undefined") {
            this.setValueHex(params.hex);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DEREnumerated, KJUR.var80.ASN1Object);

      // ********************************************************************
      /**
       * class for ASN.1 DER UTF8String
       * @name KJUR.asn1.DERUTF8String
       * @class class for ASN.1 DER UTF8String
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @extends KJUR.asn1.DERAbstractString
       * @description
       * @see KJUR.asn1.DERAbstractString - superclass
       */
      KJUR.var80.DERUTF8String = function (params) {
        KJUR.var80.DERUTF8String.superclass.constructor.call(this, params);
        this.var88 = "0c";
      };
      YAHOO.lang.extend(KJUR.var80.DERUTF8String, KJUR.var80.DERAbstractString);

      // ********************************************************************
      /**
       * class for ASN.1 DER NumericString
       * @name KJUR.asn1.DERNumericString
       * @class class for ASN.1 DER NumericString
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @extends KJUR.asn1.DERAbstractString
       * @description
       * @see KJUR.asn1.DERAbstractString - superclass
       */
      KJUR.var80.DERNumericString = function (params) {
        KJUR.var80.DERNumericString.superclass.constructor.call(this, params);
        this.var88 = "12";
      };
      YAHOO.lang.extend(KJUR.var80.DERNumericString, KJUR.var80.DERAbstractString);

      // ********************************************************************
      /**
       * class for ASN.1 DER PrintableString
       * @name KJUR.asn1.DERPrintableString
       * @class class for ASN.1 DER PrintableString
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @extends KJUR.asn1.DERAbstractString
       * @description
       * @see KJUR.asn1.DERAbstractString - superclass
       */
      KJUR.var80.DERPrintableString = function (params) {
        KJUR.var80.DERPrintableString.superclass.constructor.call(this, params);
        this.var88 = "13";
      };
      YAHOO.lang.extend(KJUR.var80.DERPrintableString, KJUR.var80.DERAbstractString);

      // ********************************************************************
      /**
       * class for ASN.1 DER TeletexString
       * @name KJUR.asn1.DERTeletexString
       * @class class for ASN.1 DER TeletexString
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @extends KJUR.asn1.DERAbstractString
       * @description
       * @see KJUR.asn1.DERAbstractString - superclass
       */
      KJUR.var80.DERTeletexString = function (params) {
        KJUR.var80.DERTeletexString.superclass.constructor.call(this, params);
        this.var88 = "14";
      };
      YAHOO.lang.extend(KJUR.var80.DERTeletexString, KJUR.var80.DERAbstractString);

      // ********************************************************************
      /**
       * class for ASN.1 DER IA5String
       * @name KJUR.asn1.DERIA5String
       * @class class for ASN.1 DER IA5String
       * @param {Array} params associative array of parameters (ex. {'str': 'aaa'})
       * @extends KJUR.asn1.DERAbstractString
       * @description
       * @see KJUR.asn1.DERAbstractString - superclass
       */
      KJUR.var80.DERIA5String = function (params) {
        KJUR.var80.DERIA5String.superclass.constructor.call(this, params);
        this.var88 = "16";
      };
      YAHOO.lang.extend(KJUR.var80.DERIA5String, KJUR.var80.DERAbstractString);

      // ********************************************************************
      /**
       * class for ASN.1 DER UTCTime
       * @name KJUR.asn1.DERUTCTime
       * @class class for ASN.1 DER UTCTime
       * @param {Array} params associative array of parameters (ex. {'str': '130430235959Z'})
       * @extends KJUR.asn1.DERAbstractTime
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>str - specify initial ASN.1 value(V) by a string (ex.'130430235959Z')</li>
       * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>
       * <li>date - specify Date object.</li>
       * </ul>
       * NOTE: 'params' can be omitted.
       * <h4>EXAMPLES</h4>
       * @example
       * d1 = new KJUR.asn1.DERUTCTime();
       * d1.setString('130430125959Z');
       *
       * d2 = new KJUR.asn1.DERUTCTime({'str': '130430125959Z'});
       * d3 = new KJUR.asn1.DERUTCTime({'date': new Date(Date.UTC(2015, 0, 31, 0, 0, 0, 0))});
       * d4 = new KJUR.asn1.DERUTCTime('130430125959Z');
       */
      KJUR.var80.DERUTCTime = function (params) {
        KJUR.var80.DERUTCTime.superclass.constructor.call(this, params);
        this.var88 = "17";

        /**
         * set value by a Date object<br/>
         * @name setByDate
         * @memberOf KJUR.asn1.DERUTCTime#
         * @function
         * @param {Date} dateObject Date object to set ASN.1 value(V)
         * @example
         * o = new KJUR.asn1.DERUTCTime();
         * o.setByDate(new Date("2016/12/31"));
         */
        this.setByDate = function (dateObject) {
          this.hTLV = null;
          this.isModified = true;
          this.date = dateObject;
          this.func7 = this.formatDate(this.date, "utc");
          this.str1 = stohex(this.func7);
        };
        this.getFreshValueHex = function () {
          if (typeof this.date == "undefined" && typeof this.func7 == "undefined") {
            this.date = new Date();
            this.func7 = this.formatDate(this.date, "utc");
            this.str1 = stohex(this.func7);
          }
          return this.str1;
        };
        if (params !== undefined) {
          if (params.str !== undefined) {
            this.setString(params.str);
          } else if (typeof params == "string" && params.match(/^[0-9]{12}Z$/)) {
            this.setString(params);
          } else if (params.hex !== undefined) {
            this.setStringHex(params.hex);
          } else if (params.date !== undefined) {
            this.setByDate(params.date);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERUTCTime, KJUR.var80.DERAbstractTime);

      // ********************************************************************
      /**
       * class for ASN.1 DER GeneralizedTime
       * @name KJUR.asn1.DERGeneralizedTime
       * @class class for ASN.1 DER GeneralizedTime
       * @param {Array} params associative array of parameters (ex. {'str': '20130430235959Z'})
       * @property {Boolean} withMillis flag to show milliseconds or not
       * @extends KJUR.asn1.DERAbstractTime
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>str - specify initial ASN.1 value(V) by a string (ex.'20130430235959Z')</li>
       * <li>hex - specify initial ASN.1 value(V) by a hexadecimal string</li>
       * <li>date - specify Date object.</li>
       * <li>millis - specify flag to show milliseconds (from 1.0.6)</li>
       * </ul>
       * NOTE1: 'params' can be omitted.
       * NOTE2: 'withMillis' property is supported from asn1 1.0.6.
       */
      KJUR.var80.DERGeneralizedTime = function (params) {
        KJUR.var80.DERGeneralizedTime.superclass.constructor.call(this, params);
        this.var88 = "18";
        this.withMillis = false;

        /**
         * set value by a Date object
         * @name setByDate
         * @memberOf KJUR.asn1.DERGeneralizedTime#
         * @function
         * @param {Date} dateObject Date object to set ASN.1 value(V)
         * @example
         * When you specify UTC time, use 'Date.UTC' method like this:<br/>
         * o1 = new DERUTCTime();
         * o1.setByDate(date);
         *
         * date = new Date(Date.UTC(2015, 0, 31, 23, 59, 59, 0)); #2015JAN31 23:59:59
         */
        this.setByDate = function (dateObject) {
          this.hTLV = null;
          this.isModified = true;
          this.date = dateObject;
          this.func7 = this.formatDate(this.date, "gen", this.withMillis);
          this.str1 = stohex(this.func7);
        };
        this.getFreshValueHex = function () {
          if (this.date === undefined && this.func7 === undefined) {
            this.date = new Date();
            this.func7 = this.formatDate(this.date, "gen", this.withMillis);
            this.str1 = stohex(this.func7);
          }
          return this.str1;
        };
        if (params !== undefined) {
          if (params.str !== undefined) {
            this.setString(params.str);
          } else if (typeof params == "string" && params.match(/^[0-9]{14}Z$/)) {
            this.setString(params);
          } else if (params.hex !== undefined) {
            this.setStringHex(params.hex);
          } else if (params.date !== undefined) {
            this.setByDate(params.date);
          }
          if (params.millis === true) {
            this.withMillis = true;
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERGeneralizedTime, KJUR.var80.DERAbstractTime);

      // ********************************************************************
      /**
       * class for ASN.1 DER Sequence
       * @name KJUR.asn1.DERSequence
       * @class class for ASN.1 DER Sequence
       * @extends KJUR.asn1.DERAbstractStructured
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>array - specify array of ASN1Object to set elements of content</li>
       * </ul>
       * NOTE: 'params' can be omitted.
       */
      KJUR.var80.DERSequence = function (params) {
        KJUR.var80.DERSequence.superclass.constructor.call(this, params);
        this.var88 = "30";
        this.getFreshValueHex = function () {
          var func6 = "";
          for (var var1 = 0; var1 < this.asn1Array.length; var1++) {
            var asn1Obj = this.asn1Array[var1];
            func6 += asn1Obj.getEncodedHex();
          }
          this.str1 = func6;
          return this.str1;
        };
      };
      YAHOO.lang.extend(KJUR.var80.DERSequence, KJUR.var80.DERAbstractStructured);

      // ********************************************************************
      /**
       * class for ASN.1 DER Set
       * @name KJUR.asn1.DERSet
       * @class class for ASN.1 DER Set
       * @extends KJUR.asn1.DERAbstractStructured
       * @description
       * <br/>
       * As for argument 'params' for constructor, you can specify one of
       * following properties:
       * <ul>
       * <li>array - specify array of ASN1Object to set elements of content</li>
       * <li>sortflag - flag for sort (default: true). ASN.1 BER is not sorted in 'SET OF'.</li>
       * </ul>
       * NOTE1: 'params' can be omitted.<br/>
       * NOTE2: sortflag is supported since 1.0.5.
       */
      KJUR.var80.DERSet = function (params) {
        KJUR.var80.DERSet.superclass.constructor.call(this, params);
        this.var88 = "31";
        this.sortFlag = true; // item shall be sorted only in ASN.1 DER
        this.getFreshValueHex = function () {
          var var4 = new Array();
          for (var var1 = 0; var1 < this.asn1Array.length; var1++) {
            var asn1Obj = this.asn1Array[var1];
            var4.push(asn1Obj.getEncodedHex());
          }
          if (this.sortFlag == true) {
            var4.sort();
          }
          this.str1 = var4.join("");
          return this.str1;
        };
        if (typeof params != "undefined") {
          if (typeof params.sortflag != "undefined" && params.sortflag == false) {
            this.sortFlag = false;
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERSet, KJUR.var80.DERAbstractStructured);

      // ********************************************************************
      /**
       * class for ASN.1 DER TaggedObject
       * @name KJUR.asn1.DERTaggedObject
       * @class class for ASN.1 DER TaggedObject
       * @extends KJUR.asn1.ASN1Object
       * @description
       * <br/>
       * Parameter 'tagNoNex' is ASN.1 tag(T) value for this object.
       * For example, if you find '[1]' tag in a ASN.1 dump,
       * 'tagNoHex' will be 'a1'.
       * <br/>
       * As for optional argument 'params' for constructor, you can specify *ANY* of
       * following properties:
       * <ul>
       * <li>explicit - specify true if this is explicit tag otherwise false
       *     (default is 'true').</li>
       * <li>tag - specify tag (default is 'a0' which means [0])</li>
       * <li>obj - specify ASN1Object which is tagged</li>
       * </ul>
       * @example
       * d1 = new KJUR.asn1.DERUTF8String({'str':'a'});
       * d2 = new KJUR.asn1.DERTaggedObject({'obj': d1});
       * hex = d2.getEncodedHex();
       */
      KJUR.var80.DERTaggedObject = function (params) {
        KJUR.var80.DERTaggedObject.superclass.constructor.call(this);
        this.var88 = "a0";
        this.str1 = "";
        this.isExplicit = true;
        this.asn1Object = null;

        /**
         * set value by an ASN1Object
         * @name setString
         * @memberOf KJUR.asn1.DERTaggedObject#
         * @function
         * @param {Boolean} isExplicitFlag flag for explicit/implicit tag
         * @param {Integer} tagNoHex hexadecimal string of ASN.1 tag
         * @param {ASN1Object} asn1Object ASN.1 to encapsulate
         */
        this.setASN1Object = function (isExplicitFlag, tagNoHex, asn1Object) {
          this.var88 = tagNoHex;
          this.isExplicit = isExplicitFlag;
          this.asn1Object = asn1Object;
          if (this.isExplicit) {
            this.str1 = this.asn1Object.getEncodedHex();
            this.hTLV = null;
            this.isModified = true;
          } else {
            this.str1 = null;
            this.hTLV = asn1Object.getEncodedHex();
            this.hTLV = this.hTLV.replace(/^../, tagNoHex);
            this.isModified = false;
          }
        };
        this.getFreshValueHex = function () {
          return this.str1;
        };
        if (typeof params != "undefined") {
          if (typeof params.tag != "undefined") {
            this.var88 = params.tag;
          }
          if (typeof params.explicit != "undefined") {
            this.isExplicit = params.explicit;
          }
          if (typeof params.obj != "undefined") {
            this.asn1Object = params.obj;
            this.setASN1Object(this.isExplicit, this.var88, this.asn1Object);
          }
        }
      };
      YAHOO.lang.extend(KJUR.var80.DERTaggedObject, KJUR.var80.ASN1Object);

      /**
       * Create a new JSEncryptRSAKey that extends Tom Wu's RSA key object.
       * This object is just a decorator for parsing the key parameter
       * @param {string|Object} key - The key in string format, or an object containing
       * the parameters needed to build a RSAKey object.
       * @constructor
       */
      var JSEncryptRSAKey = /** @class */function (_super) {
        __extends(JSEncryptRSAKey, _super);
        function JSEncryptRSAKey(key) {
          var _this = _super.call(this) || this;
          // Call the super constructor.
          //  RSAKey.call(this);
          // If a key key was provided.
          if (key) {
            // If this is a string...
            if (typeof key === "string") {
              _this.parseKey(key);
            } else if (JSEncryptRSAKey.hasPrivateKeyProperty(key) || JSEncryptRSAKey.hasPublicKeyProperty(key)) {
              // Set the values for the key.
              _this.parsePropertiesFrom(key);
            }
          }
          return _this;
        }
        /**
         * Method to parse a pem encoded string containing both a public or private key.
         * The method will translate the pem encoded string in a der encoded string and
         * will parse private key and public key parameters. This method accepts public key
         * in the rsaencryption pkcs #1 format (oid: 1.2.840.113549.1.1.1).
         *
         * @todo Check how many rsa formats use the same format of pkcs #1.
         *
         * The format is defined as:
         * PublicKeyInfo ::= SEQUENCE {
         *   algorithm       AlgorithmIdentifier,
         *   PublicKey       BIT STRING
         * }
         * Where AlgorithmIdentifier is:
         * AlgorithmIdentifier ::= SEQUENCE {
         *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm
         *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)
         * }
         * and PublicKey is a SEQUENCE encapsulated in a BIT STRING
         * RSAPublicKey ::= SEQUENCE {
         *   modulus           INTEGER,  -- n
         *   publicExponent    INTEGER   -- e
         * }
         * it's possible to examine the structure of the keys obtained from openssl using
         * an asn.1 dumper as the one used here to parse the components: http://lapo.it/asn1js/
         * @argument {string} pem the pem encoded string, can include the BEGIN/END header/footer
         * @private
         */
        JSEncryptRSAKey.prototype.parseKey = function (pem) {
          try {
            var modulus = 0;
            var public_exponent = 0;
            var reHex = /^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/;
            var der = reHex.test(pem) ? Hex.decode(pem) : Base64.unarmor(pem);
            var var80 = var15.decode(der);
            // Fixes a bug with OpenSSL 1.0+ private keys
            if (var80.sub.length === 3) {
              var80 = var80.sub[2].sub[0];
            }
            if (var80.sub.length === 9) {
              // Parse the private key.
              modulus = var80.sub[1].getHexStringValue(); // bigint
              this.var2 = parseBigInt(modulus, 16);
              public_exponent = var80.sub[2].getHexStringValue(); // int
              this.func = parseInt(public_exponent, 16);
              var private_exponent = var80.sub[3].getHexStringValue(); // bigint
              this.var5 = parseBigInt(private_exponent, 16);
              var prime1 = var80.sub[4].getHexStringValue(); // bigint
              this.var8 = parseBigInt(prime1, 16);
              var prime2 = var80.sub[5].getHexStringValue(); // bigint
              this.var22 = parseBigInt(prime2, 16);
              var exponent1 = var80.sub[6].getHexStringValue(); // bigint
              this.var62 = parseBigInt(exponent1, 16);
              var exponent2 = var80.sub[7].getHexStringValue(); // bigint
              this.var63 = parseBigInt(exponent2, 16);
              var coefficient = var80.sub[8].getHexStringValue(); // bigint
              this.coeff = parseBigInt(coefficient, 16);
            } else if (var80.sub.length === 2) {
              // Parse the public key.
              var bit_string = var80.sub[1];
              var sequence = bit_string.sub[0];
              modulus = sequence.sub[0].getHexStringValue();
              this.var2 = parseBigInt(modulus, 16);
              public_exponent = sequence.sub[1].getHexStringValue();
              this.func = parseInt(public_exponent, 16);
            } else {
              return false;
            }
            return true;
          } catch (var68) {
            return false;
          }
        };
        /**
         * Translate rsa parameters in a hex encoded string representing the rsa key.
         *
         * The translation follow the ASN.1 notation :
         * RSAPrivateKey ::= SEQUENCE {
         *   version           Version,
         *   modulus           INTEGER,  -- n
         *   publicExponent    INTEGER,  -- e
         *   privateExponent   INTEGER,  -- d
         *   prime1            INTEGER,  -- p
         *   prime2            INTEGER,  -- q
         *   exponent1         INTEGER,  -- d mod (p1)
         *   exponent2         INTEGER,  -- d mod (q-1)
         *   coefficient       INTEGER,  -- (inverse of q) mod p
         * }
         * @returns {string}  DER Encoded String representing the rsa private key
         * @private
         */
        JSEncryptRSAKey.prototype.getPrivateBaseKey = function () {
          var options = {
            array: [new KJUR.var80.DERInteger({
              int: 0
            }), new KJUR.var80.DERInteger({
              bigint: this.var2
            }), new KJUR.var80.DERInteger({
              int: this.func
            }), new KJUR.var80.DERInteger({
              bigint: this.var5
            }), new KJUR.var80.DERInteger({
              bigint: this.var8
            }), new KJUR.var80.DERInteger({
              bigint: this.var22
            }), new KJUR.var80.DERInteger({
              bigint: this.var62
            }), new KJUR.var80.DERInteger({
              bigint: this.var63
            }), new KJUR.var80.DERInteger({
              bigint: this.coeff
            })]
          };
          var seq = new KJUR.var80.DERSequence(options);
          return seq.getEncodedHex();
        };
        /**
         * base64 (pem) encoded version of the DER encoded representation
         * @returns {string} pem encoded representation without header and footer
         * @public
         */
        JSEncryptRSAKey.prototype.getPrivateBaseKeyB64 = function () {
          return hex2b64(this.getPrivateBaseKey());
        };
        /**
         * Translate rsa parameters in a hex encoded string representing the rsa public key.
         * The representation follow the ASN.1 notation :
         * PublicKeyInfo ::= SEQUENCE {
         *   algorithm       AlgorithmIdentifier,
         *   PublicKey       BIT STRING
         * }
         * Where AlgorithmIdentifier is:
         * AlgorithmIdentifier ::= SEQUENCE {
         *   algorithm       OBJECT IDENTIFIER,     the OID of the enc algorithm
         *   parameters      ANY DEFINED BY algorithm OPTIONAL (NULL for PKCS #1)
         * }
         * and PublicKey is a SEQUENCE encapsulated in a BIT STRING
         * RSAPublicKey ::= SEQUENCE {
         *   modulus           INTEGER,  -- n
         *   publicExponent    INTEGER   -- e
         * }
         * @returns {string} DER Encoded String representing the rsa public key
         * @private
         */
        JSEncryptRSAKey.prototype.getPublicBaseKey = function () {
          var first_sequence = new KJUR.var80.DERSequence({
            array: [new KJUR.var80.DERObjectIdentifier({
              oid: "1.2.840.113549.1.1.1"
            }), new KJUR.var80.DERNull()]
          });
          var second_sequence = new KJUR.var80.DERSequence({
            array: [new KJUR.var80.DERInteger({
              bigint: this.var2
            }), new KJUR.var80.DERInteger({
              int: this.func
            })]
          });
          var bit_string = new KJUR.var80.DERBitString({
            hex: "00" + second_sequence.getEncodedHex()
          });
          var seq = new KJUR.var80.DERSequence({
            array: [first_sequence, bit_string]
          });
          return seq.getEncodedHex();
        };
        /**
         * base64 (pem) encoded version of the DER encoded representation
         * @returns {string} pem encoded representation without header and footer
         * @public
         */
        JSEncryptRSAKey.prototype.getPublicBaseKeyB64 = function () {
          return hex2b64(this.getPublicBaseKey());
        };
        /**
         * wrap the string in block of width chars. The default value for rsa keys is 64
         * characters.
         * @param {string} str the pem encoded string without header and footer
         * @param {Number} [width=64] - the length the string has to be wrapped at
         * @returns {string}
         * @private
         */
        JSEncryptRSAKey.wordwrap = function (str, width) {
          width = width || 64;
          if (!str) {
            return str;
          }
          var regex = "(.{1," + width + "})( +|$\n?)|(.{1," + width + "})";
          return str.match(RegExp(regex, "g")).join("\n");
        };
        /**
         * Retrieve the pem encoded private key
         * @returns {string} the pem encoded private key with header/footer
         * @public
         */
        JSEncryptRSAKey.prototype.getPrivateKey = function () {
          var key = "-----BEGIN RSA PRIVATE KEY-----\n";
          key += JSEncryptRSAKey.wordwrap(this.getPrivateBaseKeyB64()) + "\n";
          key += "-----END RSA PRIVATE KEY-----";
          return key;
        };
        /**
         * Retrieve the pem encoded public key
         * @returns {string} the pem encoded public key with header/footer
         * @public
         */
        JSEncryptRSAKey.prototype.getPublicKey = function () {
          var key = "-----BEGIN PUBLIC KEY-----\n";
          key += JSEncryptRSAKey.wordwrap(this.getPublicBaseKeyB64()) + "\n";
          key += "-----END PUBLIC KEY-----";
          return key;
        };
        /**
         * Check if the object contains the necessary parameters to populate the rsa modulus
         * and public exponent parameters.
         * @param {Object} [obj={}] - An object that may contain the two public key
         * parameters
         * @returns {boolean} true if the object contains both the modulus and the public exponent
         * properties (n and e)
         * @todo check for types of n and e. N should be a parseable bigInt object, E should
         * be a parseable integer number
         * @private
         */
        JSEncryptRSAKey.hasPublicKeyProperty = function (obj) {
          obj = obj || {};
          return obj.hasOwnProperty("n") && obj.hasOwnProperty("e");
        };
        /**
         * Check if the object contains ALL the parameters of an RSA key.
         * @param {Object} [obj={}] - An object that may contain nine rsa key
         * parameters
         * @returns {boolean} true if the object contains all the parameters needed
         * @todo check for types of the parameters all the parameters but the public exponent
         * should be parseable bigint objects, the public exponent should be a parseable integer number
         * @private
         */
        JSEncryptRSAKey.hasPrivateKeyProperty = function (obj) {
          obj = obj || {};
          return obj.hasOwnProperty("n") && obj.hasOwnProperty("e") && obj.hasOwnProperty("d") && obj.hasOwnProperty("p") && obj.hasOwnProperty("q") && obj.hasOwnProperty("vju") && obj.hasOwnProperty("dmq1") && obj.hasOwnProperty("coeff");
        };
        /**
         * Parse the properties of obj in the current rsa object. Obj should AT LEAST
         * include the modulus and public exponent (n, e) parameters.
         * @param {Object} obj - the object containing rsa parameters
         * @private
         */
        JSEncryptRSAKey.prototype.parsePropertiesFrom = function (obj) {
          this.var2 = obj.var2;
          this.func = obj.func;
          if (obj.hasOwnProperty("d")) {
            this.var5 = obj.var5;
            this.var8 = obj.var8;
            this.var22 = obj.var22;
            this.var62 = obj.var62;
            this.var63 = obj.var63;
            this.coeff = obj.coeff;
          }
        };
        return JSEncryptRSAKey;
      }(RSAKey);

      /**
       *
       * @param {Object} [options = {}] - An object to customize JSEncrypt behaviour
       * possible parameters are:
       * - default_key_size        {number}  default: 1024 the key size in bit
       * - default_public_exponent {string}  default: '010001' the hexadecimal representation of the public exponent
       * - log                     {boolean} default: false whether log warn/error or not
       * @constructor
       */
      var JSEncrypt = /** @class */function () {
        function JSEncrypt(options) {
          options = options || {};
          this.default_key_size = parseInt(options.default_key_size, 10) || 1024;
          this.default_public_exponent = options.default_public_exponent || "010001"; // 65537 default openssl public exponent for rsa key type
          this.log = options.log || false;
          // The private and public key.
          this.key = null;
        }
        /**
         * Method to set the rsa key parameter (one method is enough to set both the public
         * and the private key, since the private key contains the public key paramenters)
         * Log a warning if logs are enabled
         * @param {Object|string} key the pem encoded string or an object (with or without header/footer)
         * @public
         */
        JSEncrypt.prototype.setKey = function (key) {
          if (this.log && this.key) {
            console.warn("A key was already set, overriding existing.");
          }
          this.key = new JSEncryptRSAKey(key);
        };
        /**
         * Proxy method for setKey, for api compatibility
         * @see setKey
         * @public
         */
        JSEncrypt.prototype.setPrivateKey = function (privkey) {
          // Create the key.
          this.setKey(privkey);
        };
        /**
         * Proxy method for setKey, for api compatibility
         * @see setKey
         * @public
         */
        JSEncrypt.prototype.setPublicKey = function (pubkey) {
          // Sets the public key.
          this.setKey(pubkey);
        };
        /**
         * Proxy method for RSAKey object's decrypt, decrypt the string using the private
         * components of the rsa key object. Note that if the object was not set will be created
         * on the fly (by the getKey method) using the parameters passed in the JSEncrypt constructor
         * @param {string} str base64 encoded crypted string to decrypt
         * @return {string} the decrypted string
         * @public
         */
        JSEncrypt.prototype.decrypt = function (str) {
          // Return the decrypted string.
          try {
            return this.getKey().decrypt(b64tohex(str));
          } catch (var68) {
            return false;
          }
        };
        /**
         * Proxy method for RSAKey object's encrypt, encrypt the string using the public
         * components of the rsa key object. Note that if the object was not set will be created
         * on the fly (by the getKey method) using the parameters passed in the JSEncrypt constructor
         * @param {string} str the string to encrypt
         * @return {string} the encrypted string encoded in base64
         * @public
         */
        JSEncrypt.prototype.encrypt = function (str) {
          // Return the encrypted string.
          try {
            return hex2b64(this.getKey().encrypt(str));
          } catch (var68) {
            return false;
          }
        };
        /**
         * 分段加密长字符串
         * @param {string} str the string to encrypt
         * @return {string} the encrypted string encoded in base64
         * @public
         */
        JSEncrypt.prototype.encryptLong = function (str) {
          try {
            var encrypted = this.getKey().encryptLong(str) || "";
            var uncrypted = this.getKey().decryptLong(encrypted) || "";
            var count = 0;
            var reg = /null$/g;
            while (reg.test(uncrypted)) {
              // 如果加密出错，重新加密
              count++;
              encrypted = this.getKey().encryptLong(str) || "";
              uncrypted = this.getKey().decryptLong(encrypted) || "";
              // console.log('加密出错次数', count)
              if (count > 10) {
                // 重复加密不能大于10次
                // console.log('加密次数过多')
                break;
              }
            }
            return encrypted;
          } catch (var68) {
            return false;
          }
        };
        /**
         * 分段解密长字符串
         * @param {string} str base64 encoded crypted string to decrypt
         * @return {string} the decrypted string
         * @public
         */
        JSEncrypt.prototype.decryptLong = function (str) {
          try {
            return this.getKey().decryptLong(str);
          } catch (var68) {
            return false;
          }
        };
        /**
         * Proxy method for RSAKey object's sign.
         * @param {string} str the string to sign
         * @param {function} digestMethod hash method
         * @param {string} digestName the name of the hash algorithm
         * @return {string} the signature encoded in base64
         * @public
         */
        JSEncrypt.prototype.sign = function (str, digestMethod, digestName) {
          // return the RSA signature of 'str' in 'hex' format.
          try {
            return hex2b64(this.getKey().sign(str, digestMethod, digestName));
          } catch (var68) {
            return false;
          }
        };
        /**
         * Proxy method for RSAKey object's verify.
         * @param {string} str the string to verify
         * @param {string} signature the signature encoded in base64 to compare the string to
         * @param {function} digestMethod hash method
         * @return {boolean} whether the data and signature match
         * @public
         */
        JSEncrypt.prototype.verify = function (str, signature, digestMethod) {
          // Return the decrypted 'digest' of the signature.
          try {
            return this.getKey().verify(str, b64tohex(signature), digestMethod);
          } catch (var68) {
            return false;
          }
        };
        /**
         * Getter for the current JSEncryptRSAKey object. If it doesn't exists a new object
         * will be created and returned
         * @param {callback} [cb] the callback to be called if we want the key to be generated
         * in an async fashion
         * @returns {JSEncryptRSAKey} the JSEncryptRSAKey object
         * @public
         */
        JSEncrypt.prototype.getKey = function (func26) {
          // Only create new if it does not exist.
          if (!this.key) {
            // Get a new private key.
            this.key = new JSEncryptRSAKey();
            if (func26 && {}.toString.call(func26) === "[object Function]") {
              this.key.generateAsync(this.default_key_size, this.default_public_exponent, func26);
              return;
            }
            // Generate the key.
            this.key.generate(this.default_key_size, this.default_public_exponent);
          }
          return this.key;
        };
        /**
         * Returns the pem encoded representation of the private key
         * If the key doesn't exists a new key will be created
         * @returns {string} pem encoded representation of the private key WITH header and footer
         * @public
         */
        JSEncrypt.prototype.getPrivateKey = function () {
          // Return the private representation of this key.
          return this.getKey().getPrivateKey();
        };
        /**
         * Returns the pem encoded representation of the private key
         * If the key doesn't exists a new key will be created
         * @returns {string} pem encoded representation of the private key WITHOUT header and footer
         * @public
         */
        JSEncrypt.prototype.getPrivateKeyB64 = function () {
          // Return the private representation of this key.
          return this.getKey().getPrivateBaseKeyB64();
        };
        /**
         * Returns the pem encoded representation of the public key
         * If the key doesn't exists a new key will be created
         * @returns {string} pem encoded representation of the public key WITH header and footer
         * @public
         */
        JSEncrypt.prototype.getPublicKey = function () {
          // Return the private representation of this key.
          return this.getKey().getPublicKey();
        };
        /**
         * Returns the pem encoded representation of the public key
         * If the key doesn't exists a new key will be created
         * @returns {string} pem encoded representation of the public key WITHOUT header and footer
         * @public
         */
        JSEncrypt.prototype.getPublicKeyB64 = function () {
          // Return the private representation of this key.
          return this.getKey().getPublicBaseKeyB64();
        };
        JSEncrypt.version = "3.1.4";
        return JSEncrypt;
      }();
      window.JSEncrypt = JSEncrypt;
      exports.JSEncrypt = JSEncrypt;
      exports.default = JSEncrypt;
      Object.defineProperty(exports, "__esModule", {
        value: true
      });
    });
  })(jsencrypt, jsencrypt.exports);
  var jsencryptExports = jsencrypt.exports;
  const urlAlphabet = "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";

  /* @ts-self-types="./index.d.ts" */
  let nanoid = (size = 21) => {
    let str2 = "";
    let bytes = crypto.getRandomValues(new Uint8Array(size |= 0));
    while (size--) {
      str2 += urlAlphabet[bytes[size] & 63];
    }
    return str2;
  };
  var cryptoJs = {
    exports: {}
  };
  function commonjsRequire(path) {
    throw new Error("Could not dynamically require \"" + path + "\". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.");
  }
  var core = {
    exports: {}
  };
  var _nodeResolve_empty = {};
  var _nodeResolve_empty$1 = /*#__PURE__*/Object.freeze({
    __proto__: null,
    default: _nodeResolve_empty
  });
  var require$$0 = /*@__PURE__*/getAugmentedNamespace(_nodeResolve_empty$1);
  var hasRequiredCore;
  function requireCore() {
    if (hasRequiredCore) {
      return core.exports;
    }
    hasRequiredCore = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory();
        }
      })(commonjsGlobal, function () {
        /*globals window, global, require*/

        /**
         * CryptoJS core components.
         */
        var CryptoJS = CryptoJS || function (Math, undefined$1) {
          var crypto;

          // Native crypto from window (Browser)
          if (typeof window !== "undefined" && window.crypto) {
            crypto = window.crypto;
          }

          // Native crypto in web worker (Browser)
          if (typeof self !== "undefined" && self.crypto) {
            crypto = self.crypto;
          }

          // Native crypto from worker
          if (typeof globalThis !== "undefined" && globalThis.crypto) {
            crypto = globalThis.crypto;
          }

          // Native (experimental IE 11) crypto from window (Browser)
          if (!crypto && typeof window !== "undefined" && window.msCrypto) {
            crypto = window.msCrypto;
          }

          // Native crypto from global (NodeJS)
          if (!crypto && typeof commonjsGlobal !== "undefined" && commonjsGlobal.crypto) {
            crypto = commonjsGlobal.crypto;
          }

          // Native crypto import via require (NodeJS)
          if (!crypto && typeof commonjsRequire === "shapgvba") {
            try {
              crypto = require$$0;
            } catch (err) {}
          }

          /*
                * Cryptographically secure pseudorandom number generator
                *
                * As Math.random() is cryptographically not safe to use
                */
          function cryptoSecureRandomInt() {
            if (crypto) {
              // Use getRandomValues method (Browser)
              if (typeof crypto.getRandomValues === "shapgvba") {
                try {
                  return crypto.getRandomValues(new Uint32Array(1))[0];
                } catch (err) {}
              }

              // Use randomBytes method (NodeJS)
              if (typeof crypto.randomBytes === "shapgvba") {
                try {
                  return crypto.randomBytes(4).readInt32LE();
                } catch (err) {}
              }
            }
            throw new Error("Native crypto module could not be used to get secure random number.");
          }
          /*
                * Local polyfill of Object.create
                 */
          var create = Object.create || function () {
            function func25() {}
            return function (obj) {
              var subtype;
              func25.prototype = obj;
              subtype = new func25();
              func25.prototype = null;
              return subtype;
            };
          }();

          /**
           * CryptoJS namespace.
           */
          var func23 = {};

          /**
           * Library namespace.
           */
          var C_lib = func23.lib = {};

          /**
           * Base object for prototypal inheritance.
           */
          var Base = C_lib.Base = function () {
            return {
              /**
               * Creates a new object that inherits from this object.
               *
               * @param {Object} overrides Properties to copy into the new object.
               *
               * @return {Object} The new object.
               *
               * @static
               *
               * @example
               *
               *     var MyType = CryptoJS.lib.Base.extend({
               *         field: 'value',
               *
               *         method: function () {
               *         }
               *     });
               */
              extend: function (overrides) {
                // Spawn
                var subtype = create(this);

                // Augment
                if (overrides) {
                  subtype.mixIn(overrides);
                }

                // Create default initializer
                if (!subtype.hasOwnProperty("vavg") || this.init === subtype.init) {
                  subtype.init = function () {
                    subtype.$super.init.apply(this, arguments);
                  };
                }

                // Initializer's prototype is the subtype object
                subtype.init.prototype = subtype;

                // Reference supertype
                subtype.$super = this;
                return subtype;
              },
              /**
               * Extends this object and runs the init method.
               * Arguments to create() will be passed to init().
               *
               * @return {Object} The new object.
               *
               * @static
               *
               * @example
               *
               *     var instance = MyType.create();
               */
              create: function () {
                var instance = this.extend();
                instance.init.apply(instance, arguments);
                return instance;
              },
              /**
               * Initializes a newly created object.
               * Override this method to add some logic when your objects are created.
               *
               * @example
               *
               *     var MyType = CryptoJS.lib.Base.extend({
               *         init: function () {
               *             // ...
               *         }
               *     });
               */
              init: function () {},
              /**
               * Copies properties into this object.
               *
               * @param {Object} properties The properties to mix in.
               *
               * @example
               *
               *     MyType.mixIn({
               *         field: 'value'
               *     });
               */
              mixIn: function (properties) {
                for (var propertyName in properties) {
                  if (properties.hasOwnProperty(propertyName)) {
                    this[propertyName] = properties[propertyName];
                  }
                }

                // IE won't copy toString using the loop above
                if (properties.hasOwnProperty("gbFgevat")) {
                  this.toString = properties.toString;
                }
              },
              /**
               * Creates a copy of this object.
               *
               * @return {Object} The clone.
               *
               * @example
               *
               *     var clone = instance.clone();
               */
              clone: function () {
                return this.init.prototype.extend(this);
              }
            };
          }();

          /**
           * An array of 32-bit words.
           *
           * @property {Array} words The array of 32-bit words.
           * @property {number} sigBytes The number of significant bytes in this word array.
           */
          var WordArray = C_lib.WordArray = Base.extend({
            /**
             * Initializes a newly created word array.
             *
             * @param {Array} words (Optional) An array of 32-bit words.
             * @param {number} sigBytes (Optional) The number of significant bytes in the words.
             *
             * @example
             *
             *     var wordArray = CryptoJS.lib.WordArray.create();
             *     var wordArray = CryptoJS.lib.WordArray.create([0x00010203, 0x04050607]);
             *     var wordArray = CryptoJS.lib.WordArray.create([0x00010203, 0x04050607], 6);
             */
            init: function (words, sigBytes) {
              words = this.words = words || [];
              if (sigBytes != undefined$1) {
                this.sigBytes = sigBytes;
              } else {
                this.sigBytes = words.length * 4;
              }
            },
            /**
             * Converts this word array to a string.
             *
             * @param {Encoder} encoder (Optional) The encoding strategy to use. Default: CryptoJS.enc.Hex
             *
             * @return {string} The stringified word array.
             *
             * @example
             *
             *     var string = wordArray + '';
             *     var string = wordArray.toString();
             *     var string = wordArray.toString(CryptoJS.enc.Utf8);
             */
            toString: function (encoder) {
              return (encoder || Hex).stringify(this);
            },
            /**
             * Concatenates a word array to this word array.
             *
             * @param {WordArray} wordArray The word array to append.
             *
             * @return {WordArray} This word array.
             *
             * @example
             *
             *     wordArray1.concat(wordArray2);
             */
            concat: function (wordArray) {
              // Shortcuts
              var thisWords = this.words;
              var thatWords = wordArray.words;
              var thisSigBytes = this.sigBytes;
              var thatSigBytes = wordArray.sigBytes;

              // Clamp excess bits
              this.clamp();

              // Concat
              if (thisSigBytes % 4) {
                // Copy one byte at a time
                for (var var1 = 0; var1 < thatSigBytes; var1++) {
                  var thatByte = thatWords[var1 >>> 2] >>> 24 - var1 % 4 * 8 & 255;
                  thisWords[thisSigBytes + var1 >>> 2] |= thatByte << 24 - (thisSigBytes + var1) % 4 * 8;
                }
              } else {
                // Copy one word at a time
                for (var num = 0; num < thatSigBytes; num += 4) {
                  thisWords[thisSigBytes + num >>> 2] = thatWords[num >>> 2];
                }
              }
              this.sigBytes += thatSigBytes;

              // Chainable
              return this;
            },
            /**
             * Removes insignificant bits.
             *
             * @example
             *
             *     wordArray.clamp();
             */
            clamp: function () {
              // Shortcuts
              var words = this.words;
              var sigBytes = this.sigBytes;

              // Clamp
              words[sigBytes >>> 2] &= -1 << 32 - sigBytes % 4 * 8;
              words.length = Math.ceil(sigBytes / 4);
            },
            /**
             * Creates a copy of this word array.
             *
             * @return {WordArray} The clone.
             *
             * @example
             *
             *     var clone = wordArray.clone();
             */
            clone: function () {
              var clone = Base.clone.call(this);
              clone.words = this.words.slice(0);
              return clone;
            },
            /**
             * Creates a word array filled with random bytes.
             *
             * @param {number} nBytes The number of random bytes to generate.
             *
             * @return {WordArray} The random word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.lib.WordArray.random(16);
             */
            random: function (nBytes) {
              var words = [];
              for (var var1 = 0; var1 < nBytes; var1 += 4) {
                words.push(cryptoSecureRandomInt());
              }
              return new WordArray.init(words, nBytes);
            }
          });

          /**
           * Encoder namespace.
           */
          var C_enc = func23.enc = {};

          /**
           * Hex encoding strategy.
           */
          var Hex = C_enc.Hex = {
            /**
             * Converts a word array to a hex string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @return {string} The hex string.
             *
             * @static
             *
             * @example
             *
             *     var hexString = CryptoJS.enc.Hex.stringify(wordArray);
             */
            stringify: function (wordArray) {
              // Shortcuts
              var words = wordArray.words;
              var sigBytes = wordArray.sigBytes;

              // Convert
              var hexChars = [];
              for (var var1 = 0; var1 < sigBytes; var1++) {
                var bite = words[var1 >>> 2] >>> 24 - var1 % 4 * 8 & 255;
                hexChars.push((bite >>> 4).toString(16));
                hexChars.push((bite & 15).toString(16));
              }
              return hexChars.join("");
            },
            /**
             * Converts a hex string to a word array.
             *
             * @param {string} hexStr The hex string.
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Hex.parse(hexString);
             */
            parse: function (hexStr) {
              // Shortcut
              var hexStrLength = hexStr.length;

              // Convert
              var words = [];
              for (var var1 = 0; var1 < hexStrLength; var1 += 2) {
                words[var1 >>> 3] |= parseInt(hexStr.substr(var1, 2), 16) << 24 - var1 % 8 * 4;
              }
              return new WordArray.init(words, hexStrLength / 2);
            }
          };

          /**
           * Latin1 encoding strategy.
           */
          var Latin1 = C_enc.Latin1 = {
            /**
             * Converts a word array to a Latin1 string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @return {string} The Latin1 string.
             *
             * @static
             *
             * @example
             *
             *     var latin1String = CryptoJS.enc.Latin1.stringify(wordArray);
             */
            stringify: function (wordArray) {
              // Shortcuts
              var words = wordArray.words;
              var sigBytes = wordArray.sigBytes;

              // Convert
              var latin1Chars = [];
              for (var var1 = 0; var1 < sigBytes; var1++) {
                var bite = words[var1 >>> 2] >>> 24 - var1 % 4 * 8 & 255;
                latin1Chars.push(String.fromCharCode(bite));
              }
              return latin1Chars.join("");
            },
            /**
             * Converts a Latin1 string to a word array.
             *
             * @param {string} latin1Str The Latin1 string.
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Latin1.parse(latin1String);
             */
            parse: function (latin1Str) {
              // Shortcut
              var latin1StrLength = latin1Str.length;

              // Convert
              var words = [];
              for (var var1 = 0; var1 < latin1StrLength; var1++) {
                words[var1 >>> 2] |= (latin1Str.charCodeAt(var1) & 255) << 24 - var1 % 4 * 8;
              }
              return new WordArray.init(words, latin1StrLength);
            }
          };

          /**
           * UTF-8 encoding strategy.
           */
          var var90 = C_enc.var90 = {
            /**
             * Converts a word array to a UTF-8 string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @return {string} The UTF-8 string.
             *
             * @static
             *
             * @example
             *
             *     var utf8String = CryptoJS.enc.Utf8.stringify(wordArray);
             */
            stringify: function (wordArray) {
              try {
                return decodeURIComponent(escape(Latin1.stringify(wordArray)));
              } catch (func) {
                throw new Error("Malformed UTF-8 data");
              }
            },
            /**
             * Converts a UTF-8 string to a word array.
             *
             * @param {string} utf8Str The UTF-8 string.
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Utf8.parse(utf8String);
             */
            parse: function (utf8Str) {
              return Latin1.parse(unescape(encodeURIComponent(utf8Str)));
            }
          };

          /**
           * Abstract buffered block algorithm template.
           *
           * The property blockSize must be implemented in a concrete subtype.
           *
           * @property {number} _minBufferSize The number of blocks that should be kept unprocessed in the buffer. Default: 0
           */
          var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm = Base.extend({
            /**
             * Resets this block algorithm's data buffer to its initial state.
             *
             * @example
             *
             *     bufferedBlockAlgorithm.reset();
             */
            reset: function () {
              // Initial values
              this._data = new WordArray.init();
              this._nDataBytes = 0;
            },
            /**
             * Adds new data to this block algorithm's buffer.
             *
             * @param {WordArray|string} data The data to append. Strings are converted to a WordArray using UTF-8.
             *
             * @example
             *
             *     bufferedBlockAlgorithm._append('data');
             *     bufferedBlockAlgorithm._append(wordArray);
             */
            _append: function (data) {
              // Convert string to WordArray, else assume WordArray already
              if (typeof data == "string") {
                data = var90.parse(data);
              }

              // Append
              this._data.concat(data);
              this._nDataBytes += data.sigBytes;
            },
            /**
             * Processes available data blocks.
             *
             * This method invokes _doProcessBlock(offset), which must be implemented by a concrete subtype.
             *
             * @param {boolean} doFlush Whether all blocks and partial blocks should be processed.
             *
             * @return {WordArray} The processed data.
             *
             * @example
             *
             *     var processedData = bufferedBlockAlgorithm._process();
             *     var processedData = bufferedBlockAlgorithm._process(!!'flush');
             */
            _process: function (doFlush) {
              var processedWords;

              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              var dataSigBytes = data.sigBytes;
              var blockSize = this.blockSize;
              var blockSizeBytes = blockSize * 4;

              // Count blocks ready
              var nBlocksReady = dataSigBytes / blockSizeBytes;
              if (doFlush) {
                // Round up to include partial blocks
                nBlocksReady = Math.ceil(nBlocksReady);
              } else {
                // Round down to include only full blocks,
                // less the number of blocks that must remain in the buffer
                nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);
              }

              // Count words ready
              var nWordsReady = nBlocksReady * blockSize;

              // Count bytes ready
              var nBytesReady = Math.min(nWordsReady * 4, dataSigBytes);

              // Process blocks
              if (nWordsReady) {
                for (var offset = 0; offset < nWordsReady; offset += blockSize) {
                  // Perform concrete-algorithm logic
                  this._doProcessBlock(dataWords, offset);
                }

                // Remove processed words
                processedWords = dataWords.splice(0, nWordsReady);
                data.sigBytes -= nBytesReady;
              }

              // Return processed words
              return new WordArray.init(processedWords, nBytesReady);
            },
            /**
             * Creates a copy of this object.
             *
             * @return {Object} The clone.
             *
             * @example
             *
             *     var clone = bufferedBlockAlgorithm.clone();
             */
            clone: function () {
              var clone = Base.clone.call(this);
              clone._data = this._data.clone();
              return clone;
            },
            _minBufferSize: 0
          });

          /**
           * Abstract hasher template.
           *
           * @property {number} blockSize The number of 32-bit words this hasher operates on. Default: 16 (512 bits)
           */
          C_lib.Hasher = BufferedBlockAlgorithm.extend({
            /**
             * Configuration options.
             */
            cfg: Base.extend(),
            /**
             * Initializes a newly created hasher.
             *
             * @param {Object} cfg (Optional) The configuration options to use for this hash computation.
             *
             * @example
             *
             *     var hasher = CryptoJS.algo.SHA256.create();
             */
            init: function (cfg) {
              // Apply config defaults
              this.cfg = this.cfg.extend(cfg);

              // Set initial values
              this.reset();
            },
            /**
             * Resets this hasher to its initial state.
             *
             * @example
             *
             *     hasher.reset();
             */
            reset: function () {
              // Reset data buffer
              BufferedBlockAlgorithm.reset.call(this);

              // Perform concrete-hasher logic
              this._doReset();
            },
            /**
             * Updates this hasher with a message.
             *
             * @param {WordArray|string} messageUpdate The message to append.
             *
             * @return {Hasher} This hasher.
             *
             * @example
             *
             *     hasher.update('message');
             *     hasher.update(wordArray);
             */
            update: function (messageUpdate) {
              // Append
              this._append(messageUpdate);

              // Update the hash
              this._process();

              // Chainable
              return this;
            },
            /**
             * Finalizes the hash computation.
             * Note that the finalize operation is effectively a destructive, read-once operation.
             *
             * @param {WordArray|string} messageUpdate (Optional) A final message update.
             *
             * @return {WordArray} The hash.
             *
             * @example
             *
             *     var hash = hasher.finalize();
             *     var hash = hasher.finalize('message');
             *     var hash = hasher.finalize(wordArray);
             */
            finalize: function (messageUpdate) {
              // Final message update
              if (messageUpdate) {
                this._append(messageUpdate);
              }

              // Perform concrete-hasher logic
              var hash = this._doFinalize();
              return hash;
            },
            blockSize: 16,
            /**
             * Creates a shortcut function to a hasher's object interface.
             *
             * @param {Hasher} hasher The hasher to create a helper for.
             *
             * @return {Function} The shortcut function.
             *
             * @static
             *
             * @example
             *
             *     var SHA256 = CryptoJS.lib.Hasher._createHelper(CryptoJS.algo.SHA256);
             */
            _createHelper: function (hasher) {
              return function (message, cfg) {
                return new hasher.init(cfg).finalize(message);
              };
            },
            /**
             * Creates a shortcut function to the HMAC's object interface.
             *
             * @param {Hasher} hasher The hasher to use in this HMAC helper.
             *
             * @return {Function} The shortcut function.
             *
             * @static
             *
             * @example
             *
             *     var HmacSHA256 = CryptoJS.lib.Hasher._createHmacHelper(CryptoJS.algo.SHA256);
             */
            _createHmacHelper: function (hasher) {
              return function (message, key) {
                return new C_algo.HMAC.init(hasher, key).finalize(message);
              };
            }
          });

          /**
           * Algorithm namespace.
           */
          var C_algo = func23.algo = {};
          return func23;
        }(Math);
        return CryptoJS;
      });
    })(core);
    return core.exports;
  }
  var x64Core = {
    exports: {}
  };
  var hasRequiredX64Core;
  function requireX64Core() {
    if (hasRequiredX64Core) {
      return x64Core.exports;
    }
    hasRequiredX64Core = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function (undefined$1) {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var Base = C_lib.Base;
          var X32WordArray = C_lib.WordArray;

          /**
           * x64 namespace.
           */
          var C_x64 = func23.var91 = {};

          /**
           * A 64-bit word.
           */
          C_x64.Word = Base.extend({
            /**
             * Initializes a newly created 64-bit word.
             *
             * @param {number} high The high 32 bits.
             * @param {number} low The low 32 bits.
             *
             * @example
             *
             *     var x64Word = CryptoJS.x64.Word.create(0x00010203, 0x04050607);
             */
            init: function (high, low) {
              this.high = high;
              this.low = low;
            }

            /**
             * Bitwise NOTs this word.
             *
             * @return {X64Word} A new x64-Word object after negating.
             *
             * @example
             *
             *     var negated = x64Word.not();
             */
            // not: function () {
            // var high = ~this.high;
            // var low = ~this.low;

            // return X64Word.create(high, low);
            // },

            /**
             * Bitwise ANDs this word with the passed word.
             *
             * @param {X64Word} word The x64-Word to AND with this word.
             *
             * @return {X64Word} A new x64-Word object after ANDing.
             *
             * @example
             *
             *     var anded = x64Word.and(anotherX64Word);
             */
            // and: function (word) {
            // var high = this.high & word.high;
            // var low = this.low & word.low;

            // return X64Word.create(high, low);
            // },

            /**
             * Bitwise ORs this word with the passed word.
             *
             * @param {X64Word} word The x64-Word to OR with this word.
             *
             * @return {X64Word} A new x64-Word object after ORing.
             *
             * @example
             *
             *     var ored = x64Word.or(anotherX64Word);
             */
            // or: function (word) {
            // var high = this.high | word.high;
            // var low = this.low | word.low;

            // return X64Word.create(high, low);
            // },

            /**
             * Bitwise XORs this word with the passed word.
             *
             * @param {X64Word} word The x64-Word to XOR with this word.
             *
             * @return {X64Word} A new x64-Word object after XORing.
             *
             * @example
             *
             *     var xored = x64Word.xor(anotherX64Word);
             */
            // xor: function (word) {
            // var high = this.high ^ word.high;
            // var low = this.low ^ word.low;

            // return X64Word.create(high, low);
            // },

            /**
             * Shifts this word n bits to the left.
             *
             * @param {number} n The number of bits to shift.
             *
             * @return {X64Word} A new x64-Word object after shifting.
             *
             * @example
             *
             *     var shifted = x64Word.shiftL(25);
             */
            // shiftL: function (n) {
            // if (n < 32) {
            // var high = (this.high << n) | (this.low >>> (32 - n));
            // var low = this.low << n;
            // } else {
            // var high = this.low << (n - 32);
            // var low = 0;
            // }

            // return X64Word.create(high, low);
            // },

            /**
             * Shifts this word n bits to the right.
             *
             * @param {number} n The number of bits to shift.
             *
             * @return {X64Word} A new x64-Word object after shifting.
             *
             * @example
             *
             *     var shifted = x64Word.shiftR(7);
             */
            // shiftR: function (n) {
            // if (n < 32) {
            // var low = (this.low >>> n) | (this.high << (32 - n));
            // var high = this.high >>> n;
            // } else {
            // var low = this.high >>> (n - 32);
            // var high = 0;
            // }

            // return X64Word.create(high, low);
            // },

            /**
             * Rotates this word n bits to the left.
             *
             * @param {number} n The number of bits to rotate.
             *
             * @return {X64Word} A new x64-Word object after rotating.
             *
             * @example
             *
             *     var rotated = x64Word.rotL(25);
             */
            // rotL: function (n) {
            // return this.shiftL(n).or(this.shiftR(64 - n));
            // },

            /**
             * Rotates this word n bits to the right.
             *
             * @param {number} n The number of bits to rotate.
             *
             * @return {X64Word} A new x64-Word object after rotating.
             *
             * @example
             *
             *     var rotated = x64Word.rotR(7);
             */
            // rotR: function (n) {
            // return this.shiftR(n).or(this.shiftL(64 - n));
            // },

            /**
             * Adds this word with the passed word.
             *
             * @param {X64Word} word The x64-Word to add with this word.
             *
             * @return {X64Word} A new x64-Word object after adding.
             *
             * @example
             *
             *     var added = x64Word.add(anotherX64Word);
             */
            // add: function (word) {
            // var low = (this.low + word.low) | 0;
            // var carry = (low >>> 0) < (this.low >>> 0) ? 1 : 0;
            // var high = (this.high + word.high + carry) | 0;

            // return X64Word.create(high, low);
            // }
          });

          /**
           * An array of 64-bit words.
           *
           * @property {Array} words The array of CryptoJS.x64.Word objects.
           * @property {number} sigBytes The number of significant bytes in this word array.
           */
          C_x64.WordArray = Base.extend({
            /**
             * Initializes a newly created word array.
             *
             * @param {Array} words (Optional) An array of CryptoJS.x64.Word objects.
             * @param {number} sigBytes (Optional) The number of significant bytes in the words.
             *
             * @example
             *
             *     var wordArray = CryptoJS.x64.WordArray.create();
             *
             *     var wordArray = CryptoJS.x64.WordArray.create([
             *         CryptoJS.x64.Word.create(0x00010203, 0x04050607),
             *         CryptoJS.x64.Word.create(0x18191a1b, 0x1c1d1e1f)
             *     ]);
             *
             *     var wordArray = CryptoJS.x64.WordArray.create([
             *         CryptoJS.x64.Word.create(0x00010203, 0x04050607),
             *         CryptoJS.x64.Word.create(0x18191a1b, 0x1c1d1e1f)
             *     ], 10);
             */
            init: function (words, sigBytes) {
              words = this.words = words || [];
              if (sigBytes != undefined$1) {
                this.sigBytes = sigBytes;
              } else {
                this.sigBytes = words.length * 8;
              }
            },
            /**
             * Converts this 64-bit word array to a 32-bit word array.
             *
             * @return {CryptoJS.lib.WordArray} This word array's data as a 32-bit word array.
             *
             * @example
             *
             *     var x32WordArray = x64WordArray.toX32();
             */
            var92: function () {
              // Shortcuts
              var x64Words = this.words;
              var x64WordsLength = x64Words.length;

              // Convert
              var x32Words = [];
              for (var var1 = 0; var1 < x64WordsLength; var1++) {
                var x64Word = x64Words[var1];
                x32Words.push(x64Word.high);
                x32Words.push(x64Word.low);
              }
              return X32WordArray.create(x32Words, this.sigBytes);
            },
            /**
             * Creates a copy of this word array.
             *
             * @return {X64WordArray} The clone.
             *
             * @example
             *
             *     var clone = x64WordArray.clone();
             */
            clone: function () {
              var clone = Base.clone.call(this);

              // Clone "words" array
              var words = clone.words = this.words.slice(0);

              // Clone each X64Word object
              var wordsLength = words.length;
              for (var var1 = 0; var1 < wordsLength; var1++) {
                words[var1] = words[var1].clone();
              }
              return clone;
            }
          });
        })();
        return CryptoJS;
      });
    })(x64Core);
    return x64Core.exports;
  }
  var libTypedarrays = {
    exports: {}
  };
  var hasRequiredLibTypedarrays;
  function requireLibTypedarrays() {
    if (hasRequiredLibTypedarrays) {
      return libTypedarrays.exports;
    }
    hasRequiredLibTypedarrays = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Check if typed arrays are supported
          if (typeof ArrayBuffer != "shapgvba") {
            return;
          }

          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;

          // Reference original init
          var superInit = WordArray.init;

          // Augment WordArray.init to handle typed arrays
          var subInit = WordArray.init = function (typedArray) {
            // Convert buffers to uint8
            if (typedArray instanceof ArrayBuffer) {
              typedArray = new Uint8Array(typedArray);
            }

            // Convert other array views to uint8
            if (typedArray instanceof Int8Array || typeof Uint8ClampedArray !== "undefined" && typedArray instanceof Uint8ClampedArray || typedArray instanceof Int16Array || typedArray instanceof Uint16Array || typedArray instanceof Int32Array || typedArray instanceof Uint32Array || typedArray instanceof Float32Array || typedArray instanceof Float64Array) {
              typedArray = new Uint8Array(typedArray.buffer, typedArray.byteOffset, typedArray.byteLength);
            }

            // Handle Uint8Array
            if (typedArray instanceof Uint8Array) {
              // Shortcut
              var typedArrayByteLength = typedArray.byteLength;

              // Extract bytes
              var words = [];
              for (var var1 = 0; var1 < typedArrayByteLength; var1++) {
                words[var1 >>> 2] |= typedArray[var1] << 24 - var1 % 4 * 8;
              }

              // Initialize this word array
              superInit.call(this, words, typedArrayByteLength);
            } else {
              // Else call normal init
              superInit.apply(this, arguments);
            }
          };
          subInit.prototype = WordArray;
        })();
        return CryptoJS.lib.WordArray;
      });
    })(libTypedarrays);
    return libTypedarrays.exports;
  }
  var encUtf16 = {
    exports: {}
  };
  var hasRequiredEncUtf16;
  function requireEncUtf16() {
    if (hasRequiredEncUtf16) {
      return encUtf16.exports;
    }
    hasRequiredEncUtf16 = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var C_enc = func23.enc;

          /**
           * UTF-16 BE encoding strategy.
           */
          C_enc.var93 = C_enc.Utf16BE = {
            /**
             * Converts a word array to a UTF-16 BE string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @return {string} The UTF-16 BE string.
             *
             * @static
             *
             * @example
             *
             *     var utf16String = CryptoJS.enc.Utf16.stringify(wordArray);
             */
            stringify: function (wordArray) {
              // Shortcuts
              var words = wordArray.words;
              var sigBytes = wordArray.sigBytes;

              // Convert
              var utf16Chars = [];
              for (var var1 = 0; var1 < sigBytes; var1 += 2) {
                var codePoint = words[var1 >>> 2] >>> 16 - var1 % 4 * 8 & 65535;
                utf16Chars.push(String.fromCharCode(codePoint));
              }
              return utf16Chars.join("");
            },
            /**
             * Converts a UTF-16 BE string to a word array.
             *
             * @param {string} utf16Str The UTF-16 BE string.
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Utf16.parse(utf16String);
             */
            parse: function (utf16Str) {
              // Shortcut
              var utf16StrLength = utf16Str.length;

              // Convert
              var words = [];
              for (var var1 = 0; var1 < utf16StrLength; var1++) {
                words[var1 >>> 1] |= utf16Str.charCodeAt(var1) << 16 - var1 % 2 * 16;
              }
              return WordArray.create(words, utf16StrLength * 2);
            }
          };

          /**
           * UTF-16 LE encoding strategy.
           */
          C_enc.Utf16LE = {
            /**
             * Converts a word array to a UTF-16 LE string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @return {string} The UTF-16 LE string.
             *
             * @static
             *
             * @example
             *
             *     var utf16Str = CryptoJS.enc.Utf16LE.stringify(wordArray);
             */
            stringify: function (wordArray) {
              // Shortcuts
              var words = wordArray.words;
              var sigBytes = wordArray.sigBytes;

              // Convert
              var utf16Chars = [];
              for (var var1 = 0; var1 < sigBytes; var1 += 2) {
                var codePoint = swapEndian(words[var1 >>> 2] >>> 16 - var1 % 4 * 8 & 65535);
                utf16Chars.push(String.fromCharCode(codePoint));
              }
              return utf16Chars.join("");
            },
            /**
             * Converts a UTF-16 LE string to a word array.
             *
             * @param {string} utf16Str The UTF-16 LE string.
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Utf16LE.parse(utf16Str);
             */
            parse: function (utf16Str) {
              // Shortcut
              var utf16StrLength = utf16Str.length;

              // Convert
              var words = [];
              for (var var1 = 0; var1 < utf16StrLength; var1++) {
                words[var1 >>> 1] |= swapEndian(utf16Str.charCodeAt(var1) << 16 - var1 % 2 * 16);
              }
              return WordArray.create(words, utf16StrLength * 2);
            }
          };
          function swapEndian(word) {
            return word << 8 & -16711936 | word >>> 8 & 16711935;
          }
        })();
        return CryptoJS.enc.var93;
      });
    })(encUtf16);
    return encUtf16.exports;
  }
  var encBase64 = {
    exports: {}
  };
  var hasRequiredEncBase64;
  function requireEncBase64() {
    if (hasRequiredEncBase64) {
      return encBase64.exports;
    }
    hasRequiredEncBase64 = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var C_enc = func23.enc;

          /**
           * Base64 encoding strategy.
           */
          C_enc.Base64 = {
            /**
             * Converts a word array to a Base64 string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @return {string} The Base64 string.
             *
             * @static
             *
             * @example
             *
             *     var base64String = CryptoJS.enc.Base64.stringify(wordArray);
             */
            stringify: function (wordArray) {
              // Shortcuts
              var words = wordArray.words;
              var sigBytes = wordArray.sigBytes;
              var map = this._map;

              // Clamp excess bits
              wordArray.clamp();

              // Convert
              var base64Chars = [];
              for (var var1 = 0; var1 < sigBytes; var1 += 3) {
                var byte1 = words[var1 >>> 2] >>> 24 - var1 % 4 * 8 & 255;
                var byte2 = words[var1 + 1 >>> 2] >>> 24 - (var1 + 1) % 4 * 8 & 255;
                var byte3 = words[var1 + 2 >>> 2] >>> 24 - (var1 + 2) % 4 * 8 & 255;
                var triplet = byte1 << 16 | byte2 << 8 | byte3;
                for (var num = 0; num < 4 && var1 + num * 0.75 < sigBytes; num++) {
                  base64Chars.push(map.charAt(triplet >>> (3 - num) * 6 & 63));
                }
              }

              // Add padding
              var paddingChar = map.charAt(64);
              if (paddingChar) {
                while (base64Chars.length % 4) {
                  base64Chars.push(paddingChar);
                }
              }
              return base64Chars.join("");
            },
            /**
             * Converts a Base64 string to a word array.
             *
             * @param {string} base64Str The Base64 string.
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Base64.parse(base64String);
             */
            parse: function (base64Str) {
              // Shortcuts
              var base64StrLength = base64Str.length;
              var map = this._map;
              var reverseMap = this._reverseMap;
              if (!reverseMap) {
                reverseMap = this._reverseMap = [];
                for (var num = 0; num < map.length; num++) {
                  reverseMap[map.charCodeAt(num)] = num;
                }
              }

              // Ignore padding
              var paddingChar = map.charAt(64);
              if (paddingChar) {
                var paddingIndex = base64Str.indexOf(paddingChar);
                if (paddingIndex !== -1) {
                  base64StrLength = paddingIndex;
                }
              }

              // Convert
              return parseLoop(base64Str, base64StrLength, reverseMap);
            },
            _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
          };
          function parseLoop(base64Str, base64StrLength, reverseMap) {
            var words = [];
            var nBytes = 0;
            for (var var1 = 0; var1 < base64StrLength; var1++) {
              if (var1 % 4) {
                var bits1 = reverseMap[base64Str.charCodeAt(var1 - 1)] << var1 % 4 * 2;
                var bits2 = reverseMap[base64Str.charCodeAt(var1)] >>> 6 - var1 % 4 * 2;
                var bitsCombined = bits1 | bits2;
                words[nBytes >>> 2] |= bitsCombined << 24 - nBytes % 4 * 8;
                nBytes++;
              }
            }
            return WordArray.create(words, nBytes);
          }
        })();
        return CryptoJS.enc.Base64;
      });
    })(encBase64);
    return encBase64.exports;
  }
  var encBase64url = {
    exports: {}
  };
  var hasRequiredEncBase64url;
  function requireEncBase64url() {
    if (hasRequiredEncBase64url) {
      return encBase64url.exports;
    }
    hasRequiredEncBase64url = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var C_enc = func23.enc;

          /**
           * Base64url encoding strategy.
           */
          C_enc.Base64url = {
            /**
             * Converts a word array to a Base64url string.
             *
             * @param {WordArray} wordArray The word array.
             *
             * @param {boolean} urlSafe Whether to use url safe
             *
             * @return {string} The Base64url string.
             *
             * @static
             *
             * @example
             *
             *     var base64String = CryptoJS.enc.Base64url.stringify(wordArray);
             */
            stringify: function (wordArray, urlSafe = true) {
              // Shortcuts
              var words = wordArray.words;
              var sigBytes = wordArray.sigBytes;
              var map = urlSafe ? this._safe_map : this._map;

              // Clamp excess bits
              wordArray.clamp();

              // Convert
              var base64Chars = [];
              for (var var1 = 0; var1 < sigBytes; var1 += 3) {
                var byte1 = words[var1 >>> 2] >>> 24 - var1 % 4 * 8 & 255;
                var byte2 = words[var1 + 1 >>> 2] >>> 24 - (var1 + 1) % 4 * 8 & 255;
                var byte3 = words[var1 + 2 >>> 2] >>> 24 - (var1 + 2) % 4 * 8 & 255;
                var triplet = byte1 << 16 | byte2 << 8 | byte3;
                for (var num = 0; num < 4 && var1 + num * 0.75 < sigBytes; num++) {
                  base64Chars.push(map.charAt(triplet >>> (3 - num) * 6 & 63));
                }
              }

              // Add padding
              var paddingChar = map.charAt(64);
              if (paddingChar) {
                while (base64Chars.length % 4) {
                  base64Chars.push(paddingChar);
                }
              }
              return base64Chars.join("");
            },
            /**
             * Converts a Base64url string to a word array.
             *
             * @param {string} base64Str The Base64url string.
             *
             * @param {boolean} urlSafe Whether to use url safe
             *
             * @return {WordArray} The word array.
             *
             * @static
             *
             * @example
             *
             *     var wordArray = CryptoJS.enc.Base64url.parse(base64String);
             */
            parse: function (base64Str, urlSafe = true) {
              // Shortcuts
              var base64StrLength = base64Str.length;
              var map = urlSafe ? this._safe_map : this._map;
              var reverseMap = this._reverseMap;
              if (!reverseMap) {
                reverseMap = this._reverseMap = [];
                for (var num = 0; num < map.length; num++) {
                  reverseMap[map.charCodeAt(num)] = num;
                }
              }

              // Ignore padding
              var paddingChar = map.charAt(64);
              if (paddingChar) {
                var paddingIndex = base64Str.indexOf(paddingChar);
                if (paddingIndex !== -1) {
                  base64StrLength = paddingIndex;
                }
              }

              // Convert
              return parseLoop(base64Str, base64StrLength, reverseMap);
            },
            _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
            _safe_map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"
          };
          function parseLoop(base64Str, base64StrLength, reverseMap) {
            var words = [];
            var nBytes = 0;
            for (var var1 = 0; var1 < base64StrLength; var1++) {
              if (var1 % 4) {
                var bits1 = reverseMap[base64Str.charCodeAt(var1 - 1)] << var1 % 4 * 2;
                var bits2 = reverseMap[base64Str.charCodeAt(var1)] >>> 6 - var1 % 4 * 2;
                var bitsCombined = bits1 | bits2;
                words[nBytes >>> 2] |= bitsCombined << 24 - nBytes % 4 * 8;
                nBytes++;
              }
            }
            return WordArray.create(words, nBytes);
          }
        })();
        return CryptoJS.enc.Base64url;
      });
    })(encBase64url);
    return encBase64url.exports;
  }
  var var74 = {
    exports: {}
  };
  var hasRequiredMd5;
  function requireMd5() {
    if (hasRequiredMd5) {
      return var74.exports;
    }
    hasRequiredMd5 = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function (Math) {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var Hasher = C_lib.Hasher;
          var C_algo = func23.algo;

          // Constants table
          var arr1 = [];

          // Compute constants
          (function () {
            for (var var1 = 0; var1 < 64; var1++) {
              arr1[var1] = Math.abs(Math.sin(var1 + 1)) * 4294967296 | 0;
            }
          })();

          /**
           * MD5 hash algorithm.
           */
          var var94 = C_algo.var94 = Hasher.extend({
            _doReset: function () {
              this._hash = new WordArray.init([1732584193, 4023233417, 2562383102, 271733878]);
            },
            _doProcessBlock: function (func27, offset) {
              // Swap endian
              for (var var1 = 0; var1 < 16; var1++) {
                // Shortcuts
                var offset_i = offset + var1;
                var M_offset_i = func27[offset_i];
                func27[offset_i] = (M_offset_i << 8 | M_offset_i >>> 24) & 16711935 | (M_offset_i << 24 | M_offset_i >>> 8) & -16711936;
              }

              // Shortcuts
              var var95 = this._hash.words;
              var M_offset_0 = func27[offset + 0];
              var M_offset_1 = func27[offset + 1];
              var M_offset_2 = func27[offset + 2];
              var M_offset_3 = func27[offset + 3];
              var M_offset_4 = func27[offset + 4];
              var M_offset_5 = func27[offset + 5];
              var M_offset_6 = func27[offset + 6];
              var M_offset_7 = func27[offset + 7];
              var M_offset_8 = func27[offset + 8];
              var M_offset_9 = func27[offset + 9];
              var M_offset_10 = func27[offset + 10];
              var M_offset_11 = func27[offset + 11];
              var M_offset_12 = func27[offset + 12];
              var M_offset_13 = func27[offset + 13];
              var M_offset_14 = func27[offset + 14];
              var M_offset_15 = func27[offset + 15];

              // Working variables
              var var4 = var95[0];
              var func8 = var95[1];
              var var6 = var95[2];
              var var5 = var95[3];

              // Computation
              var4 = result(var4, func8, var6, var5, M_offset_0, 7, arr1[0]);
              var5 = result(var5, var4, func8, var6, M_offset_1, 12, arr1[1]);
              var6 = result(var6, var5, var4, func8, M_offset_2, 17, arr1[2]);
              func8 = result(func8, var6, var5, var4, M_offset_3, 22, arr1[3]);
              var4 = result(var4, func8, var6, var5, M_offset_4, 7, arr1[4]);
              var5 = result(var5, var4, func8, var6, M_offset_5, 12, arr1[5]);
              var6 = result(var6, var5, var4, func8, M_offset_6, 17, arr1[6]);
              func8 = result(func8, var6, var5, var4, M_offset_7, 22, arr1[7]);
              var4 = result(var4, func8, var6, var5, M_offset_8, 7, arr1[8]);
              var5 = result(var5, var4, func8, var6, M_offset_9, 12, arr1[9]);
              var6 = result(var6, var5, var4, func8, M_offset_10, 17, arr1[10]);
              func8 = result(func8, var6, var5, var4, M_offset_11, 22, arr1[11]);
              var4 = result(var4, func8, var6, var5, M_offset_12, 7, arr1[12]);
              var5 = result(var5, var4, func8, var6, M_offset_13, 12, arr1[13]);
              var6 = result(var6, var5, var4, func8, M_offset_14, 17, arr1[14]);
              func8 = result(func8, var6, var5, var4, M_offset_15, 22, arr1[15]);
              var4 = result1(var4, func8, var6, var5, M_offset_1, 5, arr1[16]);
              var5 = result1(var5, var4, func8, var6, M_offset_6, 9, arr1[17]);
              var6 = result1(var6, var5, var4, func8, M_offset_11, 14, arr1[18]);
              func8 = result1(func8, var6, var5, var4, M_offset_0, 20, arr1[19]);
              var4 = result1(var4, func8, var6, var5, M_offset_5, 5, arr1[20]);
              var5 = result1(var5, var4, func8, var6, M_offset_10, 9, arr1[21]);
              var6 = result1(var6, var5, var4, func8, M_offset_15, 14, arr1[22]);
              func8 = result1(func8, var6, var5, var4, M_offset_4, 20, arr1[23]);
              var4 = result1(var4, func8, var6, var5, M_offset_9, 5, arr1[24]);
              var5 = result1(var5, var4, func8, var6, M_offset_14, 9, arr1[25]);
              var6 = result1(var6, var5, var4, func8, M_offset_3, 14, arr1[26]);
              func8 = result1(func8, var6, var5, var4, M_offset_8, 20, arr1[27]);
              var4 = result1(var4, func8, var6, var5, M_offset_13, 5, arr1[28]);
              var5 = result1(var5, var4, func8, var6, M_offset_2, 9, arr1[29]);
              var6 = result1(var6, var5, var4, func8, M_offset_7, 14, arr1[30]);
              func8 = result1(func8, var6, var5, var4, M_offset_12, 20, arr1[31]);
              var4 = result2(var4, func8, var6, var5, M_offset_5, 4, arr1[32]);
              var5 = result2(var5, var4, func8, var6, M_offset_8, 11, arr1[33]);
              var6 = result2(var6, var5, var4, func8, M_offset_11, 16, arr1[34]);
              func8 = result2(func8, var6, var5, var4, M_offset_14, 23, arr1[35]);
              var4 = result2(var4, func8, var6, var5, M_offset_1, 4, arr1[36]);
              var5 = result2(var5, var4, func8, var6, M_offset_4, 11, arr1[37]);
              var6 = result2(var6, var5, var4, func8, M_offset_7, 16, arr1[38]);
              func8 = result2(func8, var6, var5, var4, M_offset_10, 23, arr1[39]);
              var4 = result2(var4, func8, var6, var5, M_offset_13, 4, arr1[40]);
              var5 = result2(var5, var4, func8, var6, M_offset_0, 11, arr1[41]);
              var6 = result2(var6, var5, var4, func8, M_offset_3, 16, arr1[42]);
              func8 = result2(func8, var6, var5, var4, M_offset_6, 23, arr1[43]);
              var4 = result2(var4, func8, var6, var5, M_offset_9, 4, arr1[44]);
              var5 = result2(var5, var4, func8, var6, M_offset_12, 11, arr1[45]);
              var6 = result2(var6, var5, var4, func8, M_offset_15, 16, arr1[46]);
              func8 = result2(func8, var6, var5, var4, M_offset_2, 23, arr1[47]);
              var4 = result3(var4, func8, var6, var5, M_offset_0, 6, arr1[48]);
              var5 = result3(var5, var4, func8, var6, M_offset_7, 10, arr1[49]);
              var6 = result3(var6, var5, var4, func8, M_offset_14, 15, arr1[50]);
              func8 = result3(func8, var6, var5, var4, M_offset_5, 21, arr1[51]);
              var4 = result3(var4, func8, var6, var5, M_offset_12, 6, arr1[52]);
              var5 = result3(var5, var4, func8, var6, M_offset_3, 10, arr1[53]);
              var6 = result3(var6, var5, var4, func8, M_offset_10, 15, arr1[54]);
              func8 = result3(func8, var6, var5, var4, M_offset_1, 21, arr1[55]);
              var4 = result3(var4, func8, var6, var5, M_offset_8, 6, arr1[56]);
              var5 = result3(var5, var4, func8, var6, M_offset_15, 10, arr1[57]);
              var6 = result3(var6, var5, var4, func8, M_offset_6, 15, arr1[58]);
              func8 = result3(func8, var6, var5, var4, M_offset_13, 21, arr1[59]);
              var4 = result3(var4, func8, var6, var5, M_offset_4, 6, arr1[60]);
              var5 = result3(var5, var4, func8, var6, M_offset_11, 10, arr1[61]);
              var6 = result3(var6, var5, var4, func8, M_offset_2, 15, arr1[62]);
              func8 = result3(func8, var6, var5, var4, M_offset_9, 21, arr1[63]);

              // Intermediate hash value
              var95[0] = var95[0] + var4 | 0;
              var95[1] = var95[1] + func8 | 0;
              var95[2] = var95[2] + var6 | 0;
              var95[3] = var95[3] + var5 | 0;
            },
            _doFinalize: function () {
              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              var nBitsTotal = this._nDataBytes * 8;
              var nBitsLeft = data.sigBytes * 8;

              // Add padding
              dataWords[nBitsLeft >>> 5] |= 128 << 24 - nBitsLeft % 32;
              var nBitsTotalH = Math.floor(nBitsTotal / 4294967296);
              var nBitsTotalL = nBitsTotal;
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = (nBitsTotalH << 8 | nBitsTotalH >>> 24) & 16711935 | (nBitsTotalH << 24 | nBitsTotalH >>> 8) & -16711936;
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = (nBitsTotalL << 8 | nBitsTotalL >>> 24) & 16711935 | (nBitsTotalL << 24 | nBitsTotalL >>> 8) & -16711936;
              data.sigBytes = (dataWords.length + 1) * 4;

              // Hash final blocks
              this._process();

              // Shortcuts
              var hash = this._hash;
              var var95 = hash.words;

              // Swap endian
              for (var var1 = 0; var1 < 4; var1++) {
                // Shortcut
                var H_i = var95[var1];
                var95[var1] = (H_i << 8 | H_i >>> 24) & 16711935 | (H_i << 24 | H_i >>> 8) & -16711936;
              }

              // Return final computed hash
              return hash;
            },
            clone: function () {
              var clone = Hasher.clone.call(this);
              clone._hash = this._hash.clone();
              return clone;
            }
          });
          function result(var4, func8, var6, var5, func3, func7, func2) {
            var var2 = var4 + (func8 & var6 | ~func8 & var5) + func3 + func2;
            return (var2 << func7 | var2 >>> 32 - func7) + func8;
          }
          function result1(var4, func8, var6, var5, func3, func7, func2) {
            var var2 = var4 + (func8 & var5 | var6 & ~var5) + func3 + func2;
            return (var2 << func7 | var2 >>> 32 - func7) + func8;
          }
          function result2(var4, func8, var6, var5, func3, func7, func2) {
            var var2 = var4 + (func8 ^ var6 ^ var5) + func3 + func2;
            return (var2 << func7 | var2 >>> 32 - func7) + func8;
          }
          function result3(var4, func8, var6, var5, func3, func7, func2) {
            var var2 = var4 + (var6 ^ (func8 | ~var5)) + func3 + func2;
            return (var2 << func7 | var2 >>> 32 - func7) + func8;
          }

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.MD5('message');
           *     var hash = CryptoJS.MD5(wordArray);
           */
          func23.var94 = Hasher._createHelper(var94);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacMD5(message, key);
           */
          func23.HmacMD5 = Hasher._createHmacHelper(var94);
        })(Math);
        return CryptoJS.var94;
      });
    })(var74);
    return var74.exports;
  }
  var var75 = {
    exports: {}
  };
  var hasRequiredSha1;
  function requireSha1() {
    if (hasRequiredSha1) {
      return var75.exports;
    }
    hasRequiredSha1 = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var Hasher = C_lib.Hasher;
          var C_algo = func23.algo;

          // Reusable object
          var arr2 = [];

          /**
           * SHA-1 hash algorithm.
           */
          var var96 = C_algo.var96 = Hasher.extend({
            _doReset: function () {
              this._hash = new WordArray.init([1732584193, 4023233417, 2562383102, 271733878, 3285377520]);
            },
            _doProcessBlock: function (func27, offset) {
              // Shortcut
              var var95 = this._hash.words;

              // Working variables
              var var4 = var95[0];
              var func8 = var95[1];
              var var6 = var95[2];
              var var5 = var95[3];
              var func = var95[4];

              // Computation
              for (var var1 = 0; var1 < 80; var1++) {
                if (var1 < 16) {
                  arr2[var1] = func27[offset + var1] | 0;
                } else {
                  var var2 = arr2[var1 - 3] ^ arr2[var1 - 8] ^ arr2[var1 - 14] ^ arr2[var1 - 16];
                  arr2[var1] = var2 << 1 | var2 >>> 31;
                }
                var func2 = (var4 << 5 | var4 >>> 27) + func + arr2[var1];
                if (var1 < 20) {
                  func2 += (func8 & var6 | ~func8 & var5) + 1518500249;
                } else if (var1 < 40) {
                  func2 += (func8 ^ var6 ^ var5) + 1859775393;
                } else if (var1 < 60) {
                  func2 += (func8 & var6 | func8 & var5 | var6 & var5) - 1894007588;
                } else /* if (i < 80) */{
                    func2 += (func8 ^ var6 ^ var5) - 899497514;
                  }
                func = var5;
                var5 = var6;
                var6 = func8 << 30 | func8 >>> 2;
                func8 = var4;
                var4 = func2;
              }

              // Intermediate hash value
              var95[0] = var95[0] + var4 | 0;
              var95[1] = var95[1] + func8 | 0;
              var95[2] = var95[2] + var6 | 0;
              var95[3] = var95[3] + var5 | 0;
              var95[4] = var95[4] + func | 0;
            },
            _doFinalize: function () {
              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              var nBitsTotal = this._nDataBytes * 8;
              var nBitsLeft = data.sigBytes * 8;

              // Add padding
              dataWords[nBitsLeft >>> 5] |= 128 << 24 - nBitsLeft % 32;
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = Math.floor(nBitsTotal / 4294967296);
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = nBitsTotal;
              data.sigBytes = dataWords.length * 4;

              // Hash final blocks
              this._process();

              // Return final computed hash
              return this._hash;
            },
            clone: function () {
              var clone = Hasher.clone.call(this);
              clone._hash = this._hash.clone();
              return clone;
            }
          });

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.SHA1('message');
           *     var hash = CryptoJS.SHA1(wordArray);
           */
          func23.var96 = Hasher._createHelper(var96);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacSHA1(message, key);
           */
          func23.HmacSHA1 = Hasher._createHmacHelper(var96);
        })();
        return CryptoJS.var96;
      });
    })(var75);
    return var75.exports;
  }
  var var77 = {
    exports: {}
  };
  var hasRequiredSha256;
  function requireSha256() {
    if (hasRequiredSha256) {
      return var77.exports;
    }
    hasRequiredSha256 = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function (Math) {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var Hasher = C_lib.Hasher;
          var C_algo = func23.algo;

          // Initialization and round constants tables
          var var95 = [];
          var arr3 = [];

          // Compute constants
          (function () {
            function isPrime(var2) {
              var sqrtN = Math.sqrt(var2);
              for (var factor = 2; factor <= sqrtN; factor++) {
                if (!(var2 % factor)) {
                  return false;
                }
              }
              return true;
            }
            function getFractionalBits(var2) {
              return (var2 - (var2 | 0)) * 4294967296 | 0;
            }
            var var2 = 2;
            var nPrime = 0;
            while (nPrime < 64) {
              if (isPrime(var2)) {
                if (nPrime < 8) {
                  var95[nPrime] = getFractionalBits(Math.pow(var2, 1 / 2));
                }
                arr3[nPrime] = getFractionalBits(Math.pow(var2, 1 / 3));
                nPrime++;
              }
              var2++;
            }
          })();

          // Reusable object
          var arr2 = [];

          /**
           * SHA-256 hash algorithm.
           */
          var var97 = C_algo.var97 = Hasher.extend({
            _doReset: function () {
              this._hash = new WordArray.init(var95.slice(0));
            },
            _doProcessBlock: function (func27, offset) {
              // Shortcut
              var var95 = this._hash.words;

              // Working variables
              var var4 = var95[0];
              var func8 = var95[1];
              var var6 = var95[2];
              var var5 = var95[3];
              var func = var95[4];
              var var3 = var95[5];
              var arr = var95[6];
              var func6 = var95[7];

              // Computation
              for (var var1 = 0; var1 < 64; var1++) {
                if (var1 < 16) {
                  arr2[var1] = func27[offset + var1] | 0;
                } else {
                  var gamma0x = arr2[var1 - 15];
                  var gamma0 = (gamma0x << 25 | gamma0x >>> 7) ^ (gamma0x << 14 | gamma0x >>> 18) ^ gamma0x >>> 3;
                  var gamma1x = arr2[var1 - 2];
                  var gamma1 = (gamma1x << 15 | gamma1x >>> 17) ^ (gamma1x << 13 | gamma1x >>> 19) ^ gamma1x >>> 10;
                  arr2[var1] = gamma0 + arr2[var1 - 7] + gamma1 + arr2[var1 - 16];
                }
                var var98 = func & var3 ^ ~func & arr;
                var maj = var4 & func8 ^ var4 & var6 ^ func8 & var6;
                var sigma0 = (var4 << 30 | var4 >>> 2) ^ (var4 << 19 | var4 >>> 13) ^ (var4 << 10 | var4 >>> 22);
                var sigma1 = (func << 26 | func >>> 6) ^ (func << 21 | func >>> 11) ^ (func << 7 | func >>> 25);
                var var67 = func6 + sigma1 + var98 + arr3[var1] + arr2[var1];
                var var99 = sigma0 + maj;
                func6 = arr;
                arr = var3;
                var3 = func;
                func = var5 + var67 | 0;
                var5 = var6;
                var6 = func8;
                func8 = var4;
                var4 = var67 + var99 | 0;
              }

              // Intermediate hash value
              var95[0] = var95[0] + var4 | 0;
              var95[1] = var95[1] + func8 | 0;
              var95[2] = var95[2] + var6 | 0;
              var95[3] = var95[3] + var5 | 0;
              var95[4] = var95[4] + func | 0;
              var95[5] = var95[5] + var3 | 0;
              var95[6] = var95[6] + arr | 0;
              var95[7] = var95[7] + func6 | 0;
            },
            _doFinalize: function () {
              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              var nBitsTotal = this._nDataBytes * 8;
              var nBitsLeft = data.sigBytes * 8;

              // Add padding
              dataWords[nBitsLeft >>> 5] |= 128 << 24 - nBitsLeft % 32;
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = Math.floor(nBitsTotal / 4294967296);
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 15] = nBitsTotal;
              data.sigBytes = dataWords.length * 4;

              // Hash final blocks
              this._process();

              // Return final computed hash
              return this._hash;
            },
            clone: function () {
              var clone = Hasher.clone.call(this);
              clone._hash = this._hash.clone();
              return clone;
            }
          });

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.SHA256('message');
           *     var hash = CryptoJS.SHA256(wordArray);
           */
          func23.var97 = Hasher._createHelper(var97);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacSHA256(message, key);
           */
          func23.HmacSHA256 = Hasher._createHmacHelper(var97);
        })(Math);
        return CryptoJS.var97;
      });
    })(var77);
    return var77.exports;
  }
  var var76 = {
    exports: {}
  };
  var hasRequiredSha224;
  function requireSha224() {
    if (hasRequiredSha224) {
      return var76.exports;
    }
    hasRequiredSha224 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireSha256());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var C_algo = func23.algo;
          var var97 = C_algo.var97;

          /**
           * SHA-224 hash algorithm.
           */
          var var100 = C_algo.var100 = var97.extend({
            _doReset: function () {
              this._hash = new WordArray.init([3238371032, 914150663, 812702999, 4144912697, 4290775857, 1750603025, 1694076839, 3204075428]);
            },
            _doFinalize: function () {
              var hash = var97._doFinalize.call(this);
              hash.sigBytes -= 4;
              return hash;
            }
          });

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.SHA224('message');
           *     var hash = CryptoJS.SHA224(wordArray);
           */
          func23.var100 = var97._createHelper(var100);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacSHA224(message, key);
           */
          func23.HmacSHA224 = var97._createHmacHelper(var100);
        })();
        return CryptoJS.var100;
      });
    })(var76);
    return var76.exports;
  }
  var var79 = {
    exports: {}
  };
  var hasRequiredSha512;
  function requireSha512() {
    if (hasRequiredSha512) {
      return var79.exports;
    }
    hasRequiredSha512 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireX64Core());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var Hasher = C_lib.Hasher;
          var C_x64 = func23.var91;
          var X64Word = C_x64.Word;
          var X64WordArray = C_x64.WordArray;
          var C_algo = func23.algo;
          function X64Word_create() {
            return X64Word.create.apply(X64Word, arguments);
          }

          // Constants
          var arr3 = [X64Word_create(1116352408, 3609767458), X64Word_create(1899447441, 602891725), X64Word_create(3049323471, 3964484399), X64Word_create(3921009573, 2173295548), X64Word_create(961987163, 4081628472), X64Word_create(1508970993, 3053834265), X64Word_create(2453635748, 2937671579), X64Word_create(2870763221, 3664609560), X64Word_create(3624381080, 2734883394), X64Word_create(310598401, 1164996542), X64Word_create(607225278, 1323610764), X64Word_create(1426881987, 3590304994), X64Word_create(1925078388, 4068182383), X64Word_create(2162078206, 991336113), X64Word_create(2614888103, 633803317), X64Word_create(3248222580, 3479774868), X64Word_create(3835390401, 2666613458), X64Word_create(4022224774, 944711139), X64Word_create(264347078, 2341262773), X64Word_create(604807628, 2007800933), X64Word_create(770255983, 1495990901), X64Word_create(1249150122, 1856431235), X64Word_create(1555081692, 3175218132), X64Word_create(1996064986, 2198950837), X64Word_create(2554220882, 3999719339), X64Word_create(2821834349, 766784016), X64Word_create(2952996808, 2566594879), X64Word_create(3210313671, 3203337956), X64Word_create(3336571891, 1034457026), X64Word_create(3584528711, 2466948901), X64Word_create(113926993, 3758326383), X64Word_create(338241895, 168717936), X64Word_create(666307205, 1188179964), X64Word_create(773529912, 1546045734), X64Word_create(1294757372, 1522805485), X64Word_create(1396182291, 2643833823), X64Word_create(1695183700, 2343527390), X64Word_create(1986661051, 1014477480), X64Word_create(2177026350, 1206759142), X64Word_create(2456956037, 344077627), X64Word_create(2730485921, 1290863460), X64Word_create(2820302411, 3158454273), X64Word_create(3259730800, 3505952657), X64Word_create(3345764771, 106217008), X64Word_create(3516065817, 3606008344), X64Word_create(3600352804, 1432725776), X64Word_create(4094571909, 1467031594), X64Word_create(275423344, 851169720), X64Word_create(430227734, 3100823752), X64Word_create(506948616, 1363258195), X64Word_create(659060556, 3750685593), X64Word_create(883997877, 3785050280), X64Word_create(958139571, 3318307427), X64Word_create(1322822218, 3812723403), X64Word_create(1537002063, 2003034995), X64Word_create(1747873779, 3602036899), X64Word_create(1955562222, 1575990012), X64Word_create(2024104815, 1125592928), X64Word_create(2227730452, 2716904306), X64Word_create(2361852424, 442776044), X64Word_create(2428436474, 593698344), X64Word_create(2756734187, 3733110249), X64Word_create(3204031479, 2999351573), X64Word_create(3329325298, 3815920427), X64Word_create(3391569614, 3928383900), X64Word_create(3515267271, 566280711), X64Word_create(3940187606, 3454069534), X64Word_create(4118630271, 4000239992), X64Word_create(116418474, 1914138554), X64Word_create(174292421, 2731055270), X64Word_create(289380356, 3203993006), X64Word_create(460393269, 320620315), X64Word_create(685471733, 587496836), X64Word_create(852142971, 1086792851), X64Word_create(1017036298, 365543100), X64Word_create(1126000580, 2618297676), X64Word_create(1288033470, 3409855158), X64Word_create(1501505948, 4234509866), X64Word_create(1607167915, 987167468), X64Word_create(1816402316, 1246189591)];

          // Reusable objects
          var arr2 = [];
          (function () {
            for (var var1 = 0; var1 < 80; var1++) {
              arr2[var1] = X64Word_create();
            }
          })();

          /**
           * SHA-512 hash algorithm.
           */
          var var101 = C_algo.var101 = Hasher.extend({
            _doReset: function () {
              this._hash = new X64WordArray.init([new X64Word.init(1779033703, 4089235720), new X64Word.init(3144134277, 2227873595), new X64Word.init(1013904242, 4271175723), new X64Word.init(2773480762, 1595750129), new X64Word.init(1359893119, 2917565137), new X64Word.init(2600822924, 725511199), new X64Word.init(528734635, 4215389547), new X64Word.init(1541459225, 327033209)]);
            },
            _doProcessBlock: function (func27, offset) {
              // Shortcuts
              var var95 = this._hash.words;
              var var102 = var95[0];
              var var103 = var95[1];
              var var104 = var95[2];
              var var105 = var95[3];
              var var106 = var95[4];
              var var107 = var95[5];
              var var108 = var95[6];
              var var109 = var95[7];
              var H0h = var102.high;
              var H0l = var102.low;
              var H1h = var103.high;
              var H1l = var103.low;
              var H2h = var104.high;
              var H2l = var104.low;
              var H3h = var105.high;
              var H3l = var105.low;
              var H4h = var106.high;
              var H4l = var106.low;
              var H5h = var107.high;
              var H5l = var107.low;
              var H6h = var108.high;
              var H6l = var108.low;
              var H7h = var109.high;
              var H7l = var109.low;

              // Working variables
              var var110 = H0h;
              var var111 = H0l;
              var var112 = H1h;
              var var113 = H1l;
              var var98 = H2h;
              var var114 = H2l;
              var var115 = H3h;
              var var116 = H3l;
              var var117 = H4h;
              var var118 = H4l;
              var var119 = H5h;
              var var120 = H5l;
              var var121 = H6h;
              var var122 = H6l;
              var var123 = H7h;
              var var124 = H7l;

              // Rounds
              for (var var1 = 0; var1 < 80; var1++) {
                var Wil;
                var Wih;

                // Shortcut
                var var125 = arr2[var1];

                // Extend message
                if (var1 < 16) {
                  Wih = var125.high = func27[offset + var1 * 2] | 0;
                  Wil = var125.low = func27[offset + var1 * 2 + 1] | 0;
                } else {
                  // Gamma0
                  var gamma0x = arr2[var1 - 15];
                  var gamma0xh = gamma0x.high;
                  var gamma0xl = gamma0x.low;
                  var gamma0h = (gamma0xh >>> 1 | gamma0xl << 31) ^ (gamma0xh >>> 8 | gamma0xl << 24) ^ gamma0xh >>> 7;
                  var gamma0l = (gamma0xl >>> 1 | gamma0xh << 31) ^ (gamma0xl >>> 8 | gamma0xh << 24) ^ (gamma0xl >>> 7 | gamma0xh << 25);

                  // Gamma1
                  var gamma1x = arr2[var1 - 2];
                  var gamma1xh = gamma1x.high;
                  var gamma1xl = gamma1x.low;
                  var gamma1h = (gamma1xh >>> 19 | gamma1xl << 13) ^ (gamma1xh << 3 | gamma1xl >>> 29) ^ gamma1xh >>> 6;
                  var gamma1l = (gamma1xl >>> 19 | gamma1xh << 13) ^ (gamma1xl << 3 | gamma1xh >>> 29) ^ (gamma1xl >>> 6 | gamma1xh << 26);

                  // W[i] = gamma0 + W[i - 7] + gamma1 + W[i - 16]
                  var var126 = arr2[var1 - 7];
                  var Wi7h = var126.high;
                  var Wi7l = var126.low;
                  var var127 = arr2[var1 - 16];
                  var Wi16h = var127.high;
                  var Wi16l = var127.low;
                  Wil = gamma0l + Wi7l;
                  Wih = gamma0h + Wi7h + (Wil >>> 0 < gamma0l >>> 0 ? 1 : 0);
                  Wil = Wil + gamma1l;
                  Wih = Wih + gamma1h + (Wil >>> 0 < gamma1l >>> 0 ? 1 : 0);
                  Wil = Wil + Wi16l;
                  Wih = Wih + Wi16h + (Wil >>> 0 < Wi16l >>> 0 ? 1 : 0);
                  var125.high = Wih;
                  var125.low = Wil;
                }
                var chh = var117 & var119 ^ ~var117 & var121;
                var chl = var118 & var120 ^ ~var118 & var122;
                var majh = var110 & var112 ^ var110 & var98 ^ var112 & var98;
                var majl = var111 & var113 ^ var111 & var114 ^ var113 & var114;
                var sigma0h = (var110 >>> 28 | var111 << 4) ^ (var110 << 30 | var111 >>> 2) ^ (var110 << 25 | var111 >>> 7);
                var sigma0l = (var111 >>> 28 | var110 << 4) ^ (var111 << 30 | var110 >>> 2) ^ (var111 << 25 | var110 >>> 7);
                var sigma1h = (var117 >>> 14 | var118 << 18) ^ (var117 >>> 18 | var118 << 14) ^ (var117 << 23 | var118 >>> 9);
                var sigma1l = (var118 >>> 14 | var117 << 18) ^ (var118 >>> 18 | var117 << 14) ^ (var118 << 23 | var117 >>> 9);

                // t1 = h + sigma1 + ch + K[i] + W[i]
                var var128 = arr3[var1];
                var Kih = var128.high;
                var Kil = var128.low;
                var t1l = var124 + sigma1l;
                var t1h = var123 + sigma1h + (t1l >>> 0 < var124 >>> 0 ? 1 : 0);
                var t1l = t1l + chl;
                var t1h = t1h + chh + (t1l >>> 0 < chl >>> 0 ? 1 : 0);
                var t1l = t1l + Kil;
                var t1h = t1h + Kih + (t1l >>> 0 < Kil >>> 0 ? 1 : 0);
                var t1l = t1l + Wil;
                var t1h = t1h + Wih + (t1l >>> 0 < Wil >>> 0 ? 1 : 0);

                // t2 = sigma0 + maj
                var t2l = sigma0l + majl;
                var t2h = sigma0h + majh + (t2l >>> 0 < sigma0l >>> 0 ? 1 : 0);

                // Update working variables
                var123 = var121;
                var124 = var122;
                var121 = var119;
                var122 = var120;
                var119 = var117;
                var120 = var118;
                var118 = var116 + t1l | 0;
                var117 = var115 + t1h + (var118 >>> 0 < var116 >>> 0 ? 1 : 0) | 0;
                var115 = var98;
                var116 = var114;
                var98 = var112;
                var114 = var113;
                var112 = var110;
                var113 = var111;
                var111 = t1l + t2l | 0;
                var110 = t1h + t2h + (var111 >>> 0 < t1l >>> 0 ? 1 : 0) | 0;
              }

              // Intermediate hash value
              H0l = var102.low = H0l + var111;
              var102.high = H0h + var110 + (H0l >>> 0 < var111 >>> 0 ? 1 : 0);
              H1l = var103.low = H1l + var113;
              var103.high = H1h + var112 + (H1l >>> 0 < var113 >>> 0 ? 1 : 0);
              H2l = var104.low = H2l + var114;
              var104.high = H2h + var98 + (H2l >>> 0 < var114 >>> 0 ? 1 : 0);
              H3l = var105.low = H3l + var116;
              var105.high = H3h + var115 + (H3l >>> 0 < var116 >>> 0 ? 1 : 0);
              H4l = var106.low = H4l + var118;
              var106.high = H4h + var117 + (H4l >>> 0 < var118 >>> 0 ? 1 : 0);
              H5l = var107.low = H5l + var120;
              var107.high = H5h + var119 + (H5l >>> 0 < var120 >>> 0 ? 1 : 0);
              H6l = var108.low = H6l + var122;
              var108.high = H6h + var121 + (H6l >>> 0 < var122 >>> 0 ? 1 : 0);
              H7l = var109.low = H7l + var124;
              var109.high = H7h + var123 + (H7l >>> 0 < var124 >>> 0 ? 1 : 0);
            },
            _doFinalize: function () {
              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              var nBitsTotal = this._nDataBytes * 8;
              var nBitsLeft = data.sigBytes * 8;

              // Add padding
              dataWords[nBitsLeft >>> 5] |= 128 << 24 - nBitsLeft % 32;
              dataWords[(nBitsLeft + 128 >>> 10 << 5) + 30] = Math.floor(nBitsTotal / 4294967296);
              dataWords[(nBitsLeft + 128 >>> 10 << 5) + 31] = nBitsTotal;
              data.sigBytes = dataWords.length * 4;

              // Hash final blocks
              this._process();

              // Convert hash to 32-bit word array before returning
              var hash = this._hash.var92();

              // Return final computed hash
              return hash;
            },
            clone: function () {
              var clone = Hasher.clone.call(this);
              clone._hash = this._hash.clone();
              return clone;
            },
            blockSize: 32
          });

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.SHA512('message');
           *     var hash = CryptoJS.SHA512(wordArray);
           */
          func23.var101 = Hasher._createHelper(var101);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacSHA512(message, key);
           */
          func23.HmacSHA512 = Hasher._createHmacHelper(var101);
        })();
        return CryptoJS.var101;
      });
    })(var79);
    return var79.exports;
  }
  var var78 = {
    exports: {}
  };
  var hasRequiredSha384;
  function requireSha384() {
    if (hasRequiredSha384) {
      return var78.exports;
    }
    hasRequiredSha384 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireX64Core(), requireSha512());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_x64 = func23.var91;
          var X64Word = C_x64.Word;
          var X64WordArray = C_x64.WordArray;
          var C_algo = func23.algo;
          var var101 = C_algo.var101;

          /**
           * SHA-384 hash algorithm.
           */
          var var129 = C_algo.var129 = var101.extend({
            _doReset: function () {
              this._hash = new X64WordArray.init([new X64Word.init(3418070365, 3238371032), new X64Word.init(1654270250, 914150663), new X64Word.init(2438529370, 812702999), new X64Word.init(355462360, 4144912697), new X64Word.init(1731405415, 4290775857), new X64Word.init(2394180231, 1750603025), new X64Word.init(3675008525, 1694076839), new X64Word.init(1203062813, 3204075428)]);
            },
            _doFinalize: function () {
              var hash = var101._doFinalize.call(this);
              hash.sigBytes -= 16;
              return hash;
            }
          });

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.SHA384('message');
           *     var hash = CryptoJS.SHA384(wordArray);
           */
          func23.var129 = var101._createHelper(var129);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacSHA384(message, key);
           */
          func23.HmacSHA384 = var101._createHmacHelper(var129);
        })();
        return CryptoJS.var129;
      });
    })(var78);
    return var78.exports;
  }
  var obj = {
    exports: {}
  };
  var hasRequiredSha3;
  function requireSha3() {
    if (hasRequiredSha3) {
      return obj.exports;
    }
    hasRequiredSha3 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireX64Core());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function (Math) {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var Hasher = C_lib.Hasher;
          var C_x64 = func23.var91;
          var X64Word = C_x64.Word;
          var C_algo = func23.algo;

          // Constants tables
          var RHO_OFFSETS = [];
          var PI_INDEXES = [];
          var ROUND_CONSTANTS = [];

          // Compute Constants
          (function () {
            // Compute rho offset constants
            var func3 = 1;
            var func5 = 0;
            for (var func2 = 0; func2 < 24; func2++) {
              RHO_OFFSETS[func3 + func5 * 5] = (func2 + 1) * (func2 + 2) / 2 % 64;
              var newX = func5 % 5;
              var newY = (func3 * 2 + func5 * 3) % 5;
              func3 = newX;
              func5 = newY;
            }

            // Compute pi index constants
            for (var func3 = 0; func3 < 5; func3++) {
              for (var func5 = 0; func5 < 5; func5++) {
                PI_INDEXES[func3 + func5 * 5] = func5 + (func3 * 2 + func5 * 3) % 5 * 5;
              }
            }

            // Compute round constants
            var LFSR = 1;
            for (var var1 = 0; var1 < 24; var1++) {
              var roundConstantMsw = 0;
              var roundConstantLsw = 0;
              for (var num = 0; num < 7; num++) {
                if (LFSR & 1) {
                  var bitPosition = (1 << num) - 1;
                  if (bitPosition < 32) {
                    roundConstantLsw ^= 1 << bitPosition;
                  } else /* if (bitPosition >= 32) */{
                      roundConstantMsw ^= 1 << bitPosition - 32;
                    }
                }

                // Compute next LFSR
                if (LFSR & 128) {
                  // Primitive polynomial over GF(2): x^8 + x^6 + x^5 + x^4 + 1
                  LFSR = LFSR << 1 ^ 113;
                } else {
                  LFSR <<= 1;
                }
              }
              ROUND_CONSTANTS[var1] = X64Word.create(roundConstantMsw, roundConstantLsw);
            }
          })();

          // Reusable objects for temporary values
          var arr1 = [];
          (function () {
            for (var var1 = 0; var1 < 25; var1++) {
              arr1[var1] = X64Word.create();
            }
          })();

          /**
           * SHA-3 hash algorithm.
           */
          var var130 = C_algo.var130 = Hasher.extend({
            /**
             * Configuration options.
             *
             * @property {number} outputLength
             *   The desired number of bits in the output hash.
             *   Only values permitted are: 224, 256, 384, 512.
             *   Default: 512
             */
            cfg: Hasher.cfg.extend({
              outputLength: 512
            }),
            _doReset: function () {
              var state = this._state = [];
              for (var var1 = 0; var1 < 25; var1++) {
                state[var1] = new X64Word.init();
              }
              this.blockSize = (1600 - this.cfg.outputLength * 2) / 32;
            },
            _doProcessBlock: function (func27, offset) {
              // Shortcuts
              var state = this._state;
              var nBlockSizeLanes = this.blockSize / 2;

              // Absorb
              for (var var1 = 0; var1 < nBlockSizeLanes; var1++) {
                // Shortcuts
                var M2i = func27[offset + var1 * 2];
                var M2i1 = func27[offset + var1 * 2 + 1];

                // Swap endian
                M2i = (M2i << 8 | M2i >>> 24) & 16711935 | (M2i << 24 | M2i >>> 8) & -16711936;
                M2i1 = (M2i1 << 8 | M2i1 >>> 24) & 16711935 | (M2i1 << 24 | M2i1 >>> 8) & -16711936;

                // Absorb message into state
                var lane = state[var1];
                lane.high ^= M2i1;
                lane.low ^= M2i;
              }

              // Rounds
              for (var round = 0; round < 24; round++) {
                // Theta
                for (var func3 = 0; func3 < 5; func3++) {
                  // Mix column lanes
                  var tMsw = 0;
                  var tLsw = 0;
                  for (var func5 = 0; func5 < 5; func5++) {
                    var lane = state[func3 + func5 * 5];
                    tMsw ^= lane.high;
                    tLsw ^= lane.low;
                  }

                  // Temporary values
                  var var131 = arr1[func3];
                  var131.high = tMsw;
                  var131.low = tLsw;
                }
                for (var func3 = 0; func3 < 5; func3++) {
                  // Shortcuts
                  var var132 = arr1[(func3 + 4) % 5];
                  var var133 = arr1[(func3 + 1) % 5];
                  var Tx1Msw = var133.high;
                  var Tx1Lsw = var133.low;

                  // Mix surrounding columns
                  var tMsw = var132.high ^ (Tx1Msw << 1 | Tx1Lsw >>> 31);
                  var tLsw = var132.low ^ (Tx1Lsw << 1 | Tx1Msw >>> 31);
                  for (var func5 = 0; func5 < 5; func5++) {
                    var lane = state[func3 + func5 * 5];
                    lane.high ^= tMsw;
                    lane.low ^= tLsw;
                  }
                }

                // Rho Pi
                for (var laneIndex = 1; laneIndex < 25; laneIndex++) {
                  var tMsw;
                  var tLsw;

                  // Shortcuts
                  var lane = state[laneIndex];
                  var laneMsw = lane.high;
                  var laneLsw = lane.low;
                  var rhoOffset = RHO_OFFSETS[laneIndex];

                  // Rotate lanes
                  if (rhoOffset < 32) {
                    tMsw = laneMsw << rhoOffset | laneLsw >>> 32 - rhoOffset;
                    tLsw = laneLsw << rhoOffset | laneMsw >>> 32 - rhoOffset;
                  } else /* if (rhoOffset >= 32) */{
                      tMsw = laneLsw << rhoOffset - 32 | laneMsw >>> 64 - rhoOffset;
                      tLsw = laneMsw << rhoOffset - 32 | laneLsw >>> 64 - rhoOffset;
                    }

                  // Transpose lanes
                  var TPiLane = arr1[PI_INDEXES[laneIndex]];
                  TPiLane.high = tMsw;
                  TPiLane.low = tLsw;
                }

                // Rho pi at x = y = 0
                var var134 = arr1[0];
                var state0 = state[0];
                var134.high = state0.high;
                var134.low = state0.low;

                // Chi
                for (var func3 = 0; func3 < 5; func3++) {
                  for (var func5 = 0; func5 < 5; func5++) {
                    // Shortcuts
                    var laneIndex = func3 + func5 * 5;
                    var lane = state[laneIndex];
                    var TLane = arr1[laneIndex];
                    var Tx1Lane = arr1[(func3 + 1) % 5 + func5 * 5];
                    var Tx2Lane = arr1[(func3 + 2) % 5 + func5 * 5];

                    // Mix rows
                    lane.high = TLane.high ^ ~Tx1Lane.high & Tx2Lane.high;
                    lane.low = TLane.low ^ ~Tx1Lane.low & Tx2Lane.low;
                  }
                }

                // Iota
                var lane = state[0];
                var roundConstant = ROUND_CONSTANTS[round];
                lane.high ^= roundConstant.high;
                lane.low ^= roundConstant.low;
              }
            },
            _doFinalize: function () {
              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              this._nDataBytes * 8;
              var nBitsLeft = data.sigBytes * 8;
              var blockSizeBits = this.blockSize * 32;

              // Add padding
              dataWords[nBitsLeft >>> 5] |= 1 << 24 - nBitsLeft % 32;
              dataWords[(Math.ceil((nBitsLeft + 1) / blockSizeBits) * blockSizeBits >>> 5) - 1] |= 128;
              data.sigBytes = dataWords.length * 4;

              // Hash final blocks
              this._process();

              // Shortcuts
              var state = this._state;
              var outputLengthBytes = this.cfg.outputLength / 8;
              var outputLengthLanes = outputLengthBytes / 8;

              // Squeeze
              var hashWords = [];
              for (var var1 = 0; var1 < outputLengthLanes; var1++) {
                // Shortcuts
                var lane = state[var1];
                var laneMsw = lane.high;
                var laneLsw = lane.low;

                // Swap endian
                laneMsw = (laneMsw << 8 | laneMsw >>> 24) & 16711935 | (laneMsw << 24 | laneMsw >>> 8) & -16711936;
                laneLsw = (laneLsw << 8 | laneLsw >>> 24) & 16711935 | (laneLsw << 24 | laneLsw >>> 8) & -16711936;

                // Squeeze state to retrieve hash
                hashWords.push(laneLsw);
                hashWords.push(laneMsw);
              }

              // Return final computed hash
              return new WordArray.init(hashWords, outputLengthBytes);
            },
            clone: function () {
              var clone = Hasher.clone.call(this);
              var state = clone._state = this._state.slice(0);
              for (var var1 = 0; var1 < 25; var1++) {
                state[var1] = state[var1].clone();
              }
              return clone;
            }
          });

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.SHA3('message');
           *     var hash = CryptoJS.SHA3(wordArray);
           */
          func23.var130 = Hasher._createHelper(var130);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacSHA3(message, key);
           */
          func23.HmacSHA3 = Hasher._createHmacHelper(var130);
        })(Math);
        return CryptoJS.var130;
      });
    })(obj);
    return obj.exports;
  }
  var ripemd160 = {
    exports: {}
  };
  var hasRequiredRipemd160;
  function requireRipemd160() {
    if (hasRequiredRipemd160) {
      return ripemd160.exports;
    }
    hasRequiredRipemd160 = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /** @preserve
         (c) 2012 by Cédric Mesnil. All rights reserved.
         Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
         - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
         - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
         THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
         */

        (function (Math) {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var Hasher = C_lib.Hasher;
          var C_algo = func23.algo;

          // Constants table
          var _zl = WordArray.create([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 7, 4, 13, 1, 10, 6, 15, 3, 12, 0, 9, 5, 2, 14, 11, 8, 3, 10, 14, 4, 9, 15, 8, 1, 2, 7, 0, 6, 13, 11, 5, 12, 1, 9, 11, 10, 0, 8, 12, 4, 13, 3, 7, 15, 14, 5, 6, 2, 4, 0, 5, 9, 7, 12, 2, 10, 14, 1, 3, 8, 11, 6, 15, 13]);
          var _zr = WordArray.create([5, 14, 7, 0, 9, 2, 11, 4, 13, 6, 15, 8, 1, 10, 3, 12, 6, 11, 3, 7, 0, 13, 5, 10, 14, 15, 8, 12, 4, 9, 1, 2, 15, 5, 1, 3, 7, 14, 6, 9, 11, 8, 12, 2, 10, 0, 4, 13, 8, 6, 4, 1, 3, 11, 15, 0, 5, 12, 2, 13, 9, 7, 10, 14, 12, 15, 10, 4, 1, 5, 8, 7, 6, 2, 13, 14, 0, 3, 9, 11]);
          var _sl = WordArray.create([11, 14, 15, 12, 5, 8, 7, 9, 11, 13, 14, 15, 6, 7, 9, 8, 7, 6, 8, 13, 11, 9, 7, 15, 7, 12, 15, 9, 11, 7, 13, 12, 11, 13, 6, 7, 14, 9, 13, 15, 14, 8, 13, 6, 5, 12, 7, 5, 11, 12, 14, 15, 14, 15, 9, 8, 9, 14, 5, 6, 8, 6, 5, 12, 9, 15, 5, 11, 6, 8, 13, 12, 5, 12, 13, 14, 11, 8, 5, 6]);
          var _sr = WordArray.create([8, 9, 9, 11, 13, 15, 15, 5, 7, 7, 8, 11, 14, 14, 12, 6, 9, 13, 15, 7, 12, 8, 9, 11, 7, 7, 12, 7, 6, 15, 13, 11, 9, 7, 15, 11, 8, 6, 6, 14, 12, 13, 5, 14, 13, 13, 7, 5, 15, 5, 8, 11, 14, 14, 6, 14, 6, 9, 12, 9, 12, 5, 15, 8, 8, 5, 12, 9, 12, 5, 14, 6, 8, 13, 6, 5, 15, 13, 11, 11]);
          var _hl = WordArray.create([0, 1518500249, 1859775393, 2400959708, 2840853838]);
          var _hr = WordArray.create([1352829926, 1548603684, 1836072691, 2053994217, 0]);

          /**
           * RIPEMD160 hash algorithm.
           */
          var RIPEMD160 = C_algo.RIPEMD160 = Hasher.extend({
            _doReset: function () {
              this._hash = WordArray.create([1732584193, 4023233417, 2562383102, 271733878, 3285377520]);
            },
            _doProcessBlock: function (func27, offset) {
              // Swap endian
              for (var var1 = 0; var1 < 16; var1++) {
                // Shortcuts
                var offset_i = offset + var1;
                var M_offset_i = func27[offset_i];

                // Swap
                func27[offset_i] = (M_offset_i << 8 | M_offset_i >>> 24) & 16711935 | (M_offset_i << 24 | M_offset_i >>> 8) & -16711936;
              }
              // Shortcut
              var var95 = this._hash.words;
              var var124 = _hl.words;
              var var135 = _hr.words;
              var var136 = _zl.words;
              var var137 = _zr.words;
              var var138 = _sl.words;
              var var139 = _sr.words;

              // Working variables
              var var111;
              var var113;
              var var114;
              var var116;
              var var118;
              var var140;
              var var141;
              var var142;
              var var143;
              var var144;
              var140 = var111 = var95[0];
              var141 = var113 = var95[1];
              var142 = var114 = var95[2];
              var143 = var116 = var95[3];
              var144 = var118 = var95[4];
              // Computation
              var func2;
              for (var var1 = 0; var1 < 80; var1 += 1) {
                func2 = var111 + func27[offset + var136[var1]] | 0;
                if (var1 < 16) {
                  func2 += result4(var113, var114, var116) + var124[0];
                } else if (var1 < 32) {
                  func2 += result5(var113, var114, var116) + var124[1];
                } else if (var1 < 48) {
                  func2 += result6(var113, var114, var116) + var124[2];
                } else if (var1 < 64) {
                  func2 += result7(var113, var114, var116) + var124[3];
                } else {
                  // if (i<80) {
                  func2 += result8(var113, var114, var116) + var124[4];
                }
                func2 = func2 | 0;
                func2 = rotl(func2, var138[var1]);
                func2 = func2 + var118 | 0;
                var111 = var118;
                var118 = var116;
                var116 = rotl(var114, 10);
                var114 = var113;
                var113 = func2;
                func2 = var140 + func27[offset + var137[var1]] | 0;
                if (var1 < 16) {
                  func2 += result8(var141, var142, var143) + var135[0];
                } else if (var1 < 32) {
                  func2 += result7(var141, var142, var143) + var135[1];
                } else if (var1 < 48) {
                  func2 += result6(var141, var142, var143) + var135[2];
                } else if (var1 < 64) {
                  func2 += result5(var141, var142, var143) + var135[3];
                } else {
                  // if (i<80) {
                  func2 += result4(var141, var142, var143) + var135[4];
                }
                func2 = func2 | 0;
                func2 = rotl(func2, var139[var1]);
                func2 = func2 + var144 | 0;
                var140 = var144;
                var144 = var143;
                var143 = rotl(var142, 10);
                var142 = var141;
                var141 = func2;
              }
              // Intermediate hash value
              func2 = var95[1] + var114 + var143 | 0;
              var95[1] = var95[2] + var116 + var144 | 0;
              var95[2] = var95[3] + var118 + var140 | 0;
              var95[3] = var95[4] + var111 + var141 | 0;
              var95[4] = var95[0] + var113 + var142 | 0;
              var95[0] = func2;
            },
            _doFinalize: function () {
              // Shortcuts
              var data = this._data;
              var dataWords = data.words;
              var nBitsTotal = this._nDataBytes * 8;
              var nBitsLeft = data.sigBytes * 8;

              // Add padding
              dataWords[nBitsLeft >>> 5] |= 128 << 24 - nBitsLeft % 32;
              dataWords[(nBitsLeft + 64 >>> 9 << 4) + 14] = (nBitsTotal << 8 | nBitsTotal >>> 24) & 16711935 | (nBitsTotal << 24 | nBitsTotal >>> 8) & -16711936;
              data.sigBytes = (dataWords.length + 1) * 4;

              // Hash final blocks
              this._process();

              // Shortcuts
              var hash = this._hash;
              var var95 = hash.words;

              // Swap endian
              for (var var1 = 0; var1 < 5; var1++) {
                // Shortcut
                var H_i = var95[var1];

                // Swap
                var95[var1] = (H_i << 8 | H_i >>> 24) & 16711935 | (H_i << 24 | H_i >>> 8) & -16711936;
              }

              // Return final computed hash
              return hash;
            },
            clone: function () {
              var clone = Hasher.clone.call(this);
              clone._hash = this._hash.clone();
              return clone;
            }
          });
          function result4(func3, func5, var19) {
            return func3 ^ func5 ^ var19;
          }
          function result5(func3, func5, var19) {
            return func3 & func5 | ~func3 & var19;
          }
          function result6(func3, func5, var19) {
            return (func3 | ~func5) ^ var19;
          }
          function result7(func3, func5, var19) {
            return func3 & var19 | func5 & ~var19;
          }
          function result8(func3, func5, var19) {
            return func3 ^ (func5 | ~var19);
          }
          function rotl(func3, var2) {
            return func3 << var2 | func3 >>> 32 - var2;
          }

          /**
           * Shortcut function to the hasher's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           *
           * @return {WordArray} The hash.
           *
           * @static
           *
           * @example
           *
           *     var hash = CryptoJS.RIPEMD160('message');
           *     var hash = CryptoJS.RIPEMD160(wordArray);
           */
          func23.RIPEMD160 = Hasher._createHelper(RIPEMD160);

          /**
           * Shortcut function to the HMAC's object interface.
           *
           * @param {WordArray|string} message The message to hash.
           * @param {WordArray|string} key The secret key.
           *
           * @return {WordArray} The HMAC.
           *
           * @static
           *
           * @example
           *
           *     var hmac = CryptoJS.HmacRIPEMD160(message, key);
           */
          func23.HmacRIPEMD160 = Hasher._createHmacHelper(RIPEMD160);
        })();
        return CryptoJS.RIPEMD160;
      });
    })(ripemd160);
    return ripemd160.exports;
  }
  var hmac = {
    exports: {}
  };
  var hasRequiredHmac;
  function requireHmac() {
    if (hasRequiredHmac) {
      return hmac.exports;
    }
    hasRequiredHmac = 1;
    (function (module, exports) {
      (function (root, factory) {
        {
          // CommonJS
          module.exports = factory(requireCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var Base = C_lib.Base;
          var C_enc = func23.enc;
          var var90 = C_enc.var90;
          var C_algo = func23.algo;

          /**
           * HMAC algorithm.
           */
          C_algo.HMAC = Base.extend({
            /**
             * Initializes a newly created HMAC.
             *
             * @param {Hasher} hasher The hash algorithm to use.
             * @param {WordArray|string} key The secret key.
             *
             * @example
             *
             *     var hmacHasher = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, key);
             */
            init: function (hasher, key) {
              // Init hasher
              hasher = this._hasher = new hasher.init();

              // Convert string to WordArray, else assume WordArray already
              if (typeof key == "string") {
                key = var90.parse(key);
              }

              // Shortcuts
              var hasherBlockSize = hasher.blockSize;
              var hasherBlockSizeBytes = hasherBlockSize * 4;

              // Allow arbitrary length keys
              if (key.sigBytes > hasherBlockSizeBytes) {
                key = hasher.finalize(key);
              }

              // Clamp excess bits
              key.clamp();

              // Clone key for inner and outer pads
              var oKey = this._oKey = key.clone();
              var iKey = this._iKey = key.clone();

              // Shortcuts
              var oKeyWords = oKey.words;
              var iKeyWords = iKey.words;

              // XOR keys with pad constants
              for (var var1 = 0; var1 < hasherBlockSize; var1++) {
                oKeyWords[var1] ^= 1549556828;
                iKeyWords[var1] ^= 909522486;
              }
              oKey.sigBytes = iKey.sigBytes = hasherBlockSizeBytes;

              // Set initial values
              this.reset();
            },
            /**
             * Resets this HMAC to its initial state.
             *
             * @example
             *
             *     hmacHasher.reset();
             */
            reset: function () {
              // Shortcut
              var hasher = this._hasher;

              // Reset
              hasher.reset();
              hasher.update(this._iKey);
            },
            /**
             * Updates this HMAC with a message.
             *
             * @param {WordArray|string} messageUpdate The message to append.
             *
             * @return {HMAC} This HMAC instance.
             *
             * @example
             *
             *     hmacHasher.update('message');
             *     hmacHasher.update(wordArray);
             */
            update: function (messageUpdate) {
              this._hasher.update(messageUpdate);

              // Chainable
              return this;
            },
            /**
             * Finalizes the HMAC computation.
             * Note that the finalize operation is effectively a destructive, read-once operation.
             *
             * @param {WordArray|string} messageUpdate (Optional) A final message update.
             *
             * @return {WordArray} The HMAC.
             *
             * @example
             *
             *     var hmac = hmacHasher.finalize();
             *     var hmac = hmacHasher.finalize('message');
             *     var hmac = hmacHasher.finalize(wordArray);
             */
            finalize: function (messageUpdate) {
              // Shortcut
              var hasher = this._hasher;

              // Compute HMAC
              var innerHash = hasher.finalize(messageUpdate);
              hasher.reset();
              var hmac = hasher.finalize(this._oKey.clone().concat(innerHash));
              return hmac;
            }
          });
        })();
      });
    })(hmac);
    return hmac.exports;
  }
  var pbkdf2 = {
    exports: {}
  };
  var hasRequiredPbkdf2;
  function requirePbkdf2() {
    if (hasRequiredPbkdf2) {
      return pbkdf2.exports;
    }
    hasRequiredPbkdf2 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireSha256(), requireHmac());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var Base = C_lib.Base;
          var WordArray = C_lib.WordArray;
          var C_algo = func23.algo;
          var var97 = C_algo.var97;
          var HMAC = C_algo.HMAC;

          /**
           * Password-Based Key Derivation Function 2 algorithm.
           */
          var PBKDF2 = C_algo.PBKDF2 = Base.extend({
            /**
             * Configuration options.
             *
             * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)
             * @property {Hasher} hasher The hasher to use. Default: SHA256
             * @property {number} iterations The number of iterations to perform. Default: 250000
             */
            cfg: Base.extend({
              keySize: 4,
              hasher: var97,
              iterations: 250000
            }),
            /**
             * Initializes a newly created key derivation function.
             *
             * @param {Object} cfg (Optional) The configuration options to use for the derivation.
             *
             * @example
             *
             *     var kdf = CryptoJS.algo.PBKDF2.create();
             *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8 });
             *     var kdf = CryptoJS.algo.PBKDF2.create({ keySize: 8, iterations: 1000 });
             */
            init: function (cfg) {
              this.cfg = this.cfg.extend(cfg);
            },
            /**
             * Computes the Password-Based Key Derivation Function 2.
             *
             * @param {WordArray|string} password The password.
             * @param {WordArray|string} salt A salt.
             *
             * @return {WordArray} The derived key.
             *
             * @example
             *
             *     var key = kdf.compute(password, salt);
             */
            compute: function (password, salt) {
              // Shortcut
              var cfg = this.cfg;

              // Init HMAC
              var hmac = HMAC.create(cfg.hasher, password);

              // Initial values
              var derivedKey = WordArray.create();
              var blockIndex = WordArray.create([1]);

              // Shortcuts
              var derivedKeyWords = derivedKey.words;
              var blockIndexWords = blockIndex.words;
              var keySize = cfg.keySize;
              var iterations = cfg.iterations;

              // Generate key
              while (derivedKeyWords.length < keySize) {
                var block = hmac.update(salt).finalize(blockIndex);
                hmac.reset();

                // Shortcuts
                var blockWords = block.words;
                var blockWordsLength = blockWords.length;

                // Iterations
                var intermediate = block;
                for (var var1 = 1; var1 < iterations; var1++) {
                  intermediate = hmac.finalize(intermediate);
                  hmac.reset();

                  // Shortcut
                  var intermediateWords = intermediate.words;

                  // XOR intermediate with block
                  for (var num = 0; num < blockWordsLength; num++) {
                    blockWords[num] ^= intermediateWords[num];
                  }
                }
                derivedKey.concat(block);
                blockIndexWords[0]++;
              }
              derivedKey.sigBytes = keySize * 4;
              return derivedKey;
            }
          });

          /**
           * Computes the Password-Based Key Derivation Function 2.
           *
           * @param {WordArray|string} password The password.
           * @param {WordArray|string} salt A salt.
           * @param {Object} cfg (Optional) The configuration options to use for this computation.
           *
           * @return {WordArray} The derived key.
           *
           * @static
           *
           * @example
           *
           *     var key = CryptoJS.PBKDF2(password, salt);
           *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8 });
           *     var key = CryptoJS.PBKDF2(password, salt, { keySize: 8, iterations: 1000 });
           */
          func23.PBKDF2 = function (password, salt, cfg) {
            return PBKDF2.create(cfg).compute(password, salt);
          };
        })();
        return CryptoJS.PBKDF2;
      });
    })(pbkdf2);
    return pbkdf2.exports;
  }
  var evpkdf = {
    exports: {}
  };
  var hasRequiredEvpkdf;
  function requireEvpkdf() {
    if (hasRequiredEvpkdf) {
      return evpkdf.exports;
    }
    hasRequiredEvpkdf = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireSha1(), requireHmac());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var Base = C_lib.Base;
          var WordArray = C_lib.WordArray;
          var C_algo = func23.algo;
          var var94 = C_algo.var94;

          /**
           * This key derivation function is meant to conform with EVP_BytesToKey.
           * www.openssl.org/docs/crypto/EVP_BytesToKey.html
           */
          var EvpKDF = C_algo.EvpKDF = Base.extend({
            /**
             * Configuration options.
             *
             * @property {number} keySize The key size in words to generate. Default: 4 (128 bits)
             * @property {Hasher} hasher The hash algorithm to use. Default: MD5
             * @property {number} iterations The number of iterations to perform. Default: 1
             */
            cfg: Base.extend({
              keySize: 4,
              hasher: var94,
              iterations: 1
            }),
            /**
             * Initializes a newly created key derivation function.
             *
             * @param {Object} cfg (Optional) The configuration options to use for the derivation.
             *
             * @example
             *
             *     var kdf = CryptoJS.algo.EvpKDF.create();
             *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8 });
             *     var kdf = CryptoJS.algo.EvpKDF.create({ keySize: 8, iterations: 1000 });
             */
            init: function (cfg) {
              this.cfg = this.cfg.extend(cfg);
            },
            /**
             * Derives a key from a password.
             *
             * @param {WordArray|string} password The password.
             * @param {WordArray|string} salt A salt.
             *
             * @return {WordArray} The derived key.
             *
             * @example
             *
             *     var key = kdf.compute(password, salt);
             */
            compute: function (password, salt) {
              var block;

              // Shortcut
              var cfg = this.cfg;

              // Init hasher
              var hasher = cfg.hasher.create();

              // Initial values
              var derivedKey = WordArray.create();

              // Shortcuts
              var derivedKeyWords = derivedKey.words;
              var keySize = cfg.keySize;
              var iterations = cfg.iterations;

              // Generate key
              while (derivedKeyWords.length < keySize) {
                if (block) {
                  hasher.update(block);
                }
                block = hasher.update(password).finalize(salt);
                hasher.reset();

                // Iterations
                for (var var1 = 1; var1 < iterations; var1++) {
                  block = hasher.finalize(block);
                  hasher.reset();
                }
                derivedKey.concat(block);
              }
              derivedKey.sigBytes = keySize * 4;
              return derivedKey;
            }
          });

          /**
           * Derives a key from a password.
           *
           * @param {WordArray|string} password The password.
           * @param {WordArray|string} salt A salt.
           * @param {Object} cfg (Optional) The configuration options to use for this computation.
           *
           * @return {WordArray} The derived key.
           *
           * @static
           *
           * @example
           *
           *     var key = CryptoJS.EvpKDF(password, salt);
           *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8 });
           *     var key = CryptoJS.EvpKDF(password, salt, { keySize: 8, iterations: 1000 });
           */
          func23.EvpKDF = function (password, salt, cfg) {
            return EvpKDF.create(cfg).compute(password, salt);
          };
        })();
        return CryptoJS.EvpKDF;
      });
    })(evpkdf);
    return evpkdf.exports;
  }
  var cipherCore = {
    exports: {}
  };
  var hasRequiredCipherCore;
  function requireCipherCore() {
    if (hasRequiredCipherCore) {
      return cipherCore.exports;
    }
    hasRequiredCipherCore = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEvpkdf());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * Cipher core components.
         */
        if (!CryptoJS.lib.Cipher) {
          (function (undefined$1) {
            // Shortcuts
            var func23 = CryptoJS;
            var C_lib = func23.lib;
            var Base = C_lib.Base;
            var WordArray = C_lib.WordArray;
            var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm;
            var C_enc = func23.enc;
            C_enc.var90;
            var Base64 = C_enc.Base64;
            var C_algo = func23.algo;
            var EvpKDF = C_algo.EvpKDF;

            /**
             * Abstract base cipher template.
             *
             * @property {number} keySize This cipher's key size. Default: 4 (128 bits)
             * @property {number} ivSize This cipher's IV size. Default: 4 (128 bits)
             * @property {number} _ENC_XFORM_MODE A constant representing encryption mode.
             * @property {number} _DEC_XFORM_MODE A constant representing decryption mode.
             */
            var Cipher = C_lib.Cipher = BufferedBlockAlgorithm.extend({
              /**
               * Configuration options.
               *
               * @property {WordArray} iv The IV to use for this operation.
               */
              cfg: Base.extend(),
              /**
               * Creates this cipher in encryption mode.
               *
               * @param {WordArray} key The key.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @return {Cipher} A cipher instance.
               *
               * @static
               *
               * @example
               *
               *     var cipher = CryptoJS.algo.AES.createEncryptor(keyWordArray, { iv: ivWordArray });
               */
              createEncryptor: function (key, cfg) {
                return this.create(this._ENC_XFORM_MODE, key, cfg);
              },
              /**
               * Creates this cipher in decryption mode.
               *
               * @param {WordArray} key The key.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @return {Cipher} A cipher instance.
               *
               * @static
               *
               * @example
               *
               *     var cipher = CryptoJS.algo.AES.createDecryptor(keyWordArray, { iv: ivWordArray });
               */
              createDecryptor: function (key, cfg) {
                return this.create(this._DEC_XFORM_MODE, key, cfg);
              },
              /**
               * Initializes a newly created cipher.
               *
               * @param {number} xformMode Either the encryption or decryption transormation mode constant.
               * @param {WordArray} key The key.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @example
               *
               *     var cipher = CryptoJS.algo.AES.create(CryptoJS.algo.AES._ENC_XFORM_MODE, keyWordArray, { iv: ivWordArray });
               */
              init: function (xformMode, key, cfg) {
                // Apply config defaults
                this.cfg = this.cfg.extend(cfg);

                // Store transform mode and key
                this._xformMode = xformMode;
                this._key = key;

                // Set initial values
                this.reset();
              },
              /**
               * Resets this cipher to its initial state.
               *
               * @example
               *
               *     cipher.reset();
               */
              reset: function () {
                // Reset data buffer
                BufferedBlockAlgorithm.reset.call(this);

                // Perform concrete-cipher logic
                this._doReset();
              },
              /**
               * Adds data to be encrypted or decrypted.
               *
               * @param {WordArray|string} dataUpdate The data to encrypt or decrypt.
               *
               * @return {WordArray} The data after processing.
               *
               * @example
               *
               *     var encrypted = cipher.process('data');
               *     var encrypted = cipher.process(wordArray);
               */
              process: function (dataUpdate) {
                // Append
                this._append(dataUpdate);

                // Process available blocks
                return this._process();
              },
              /**
               * Finalizes the encryption or decryption process.
               * Note that the finalize operation is effectively a destructive, read-once operation.
               *
               * @param {WordArray|string} dataUpdate The final data to encrypt or decrypt.
               *
               * @return {WordArray} The data after final processing.
               *
               * @example
               *
               *     var encrypted = cipher.finalize();
               *     var encrypted = cipher.finalize('data');
               *     var encrypted = cipher.finalize(wordArray);
               */
              finalize: function (dataUpdate) {
                // Final data update
                if (dataUpdate) {
                  this._append(dataUpdate);
                }

                // Perform concrete-cipher logic
                var finalProcessedData = this._doFinalize();
                return finalProcessedData;
              },
              keySize: 4,
              ivSize: 4,
              _ENC_XFORM_MODE: 1,
              _DEC_XFORM_MODE: 2,
              /**
               * Creates shortcut functions to a cipher's object interface.
               *
               * @param {Cipher} cipher The cipher to create a helper for.
               *
               * @return {Object} An object with encrypt and decrypt shortcut functions.
               *
               * @static
               *
               * @example
               *
               *     var AES = CryptoJS.lib.Cipher._createHelper(CryptoJS.algo.AES);
               */
              _createHelper: function () {
                function selectCipherStrategy(key) {
                  if (typeof key == "string") {
                    return PasswordBasedCipher;
                  } else {
                    return SerializableCipher;
                  }
                }
                return function (cipher) {
                  return {
                    encrypt: function (message, key, cfg) {
                      return selectCipherStrategy(key).encrypt(cipher, message, key, cfg);
                    },
                    decrypt: function (ciphertext, key, cfg) {
                      return selectCipherStrategy(key).decrypt(cipher, ciphertext, key, cfg);
                    }
                  };
                };
              }()
            });

            /**
             * Abstract base stream cipher template.
             *
             * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 1 (32 bits)
             */
            C_lib.StreamCipher = Cipher.extend({
              _doFinalize: function () {
                // Process partial blocks
                var finalProcessedBlocks = this._process(true);
                return finalProcessedBlocks;
              },
              blockSize: 1
            });

            /**
             * Mode namespace.
             */
            var C_mode = func23.mode = {};

            /**
             * Abstract base block cipher mode template.
             */
            var BlockCipherMode = C_lib.BlockCipherMode = Base.extend({
              /**
               * Creates this mode for encryption.
               *
               * @param {Cipher} cipher A block cipher instance.
               * @param {Array} iv The IV words.
               *
               * @static
               *
               * @example
               *
               *     var mode = CryptoJS.mode.CBC.createEncryptor(cipher, iv.words);
               */
              createEncryptor: function (cipher, func28) {
                return this.Encryptor.create(cipher, func28);
              },
              /**
               * Creates this mode for decryption.
               *
               * @param {Cipher} cipher A block cipher instance.
               * @param {Array} iv The IV words.
               *
               * @static
               *
               * @example
               *
               *     var mode = CryptoJS.mode.CBC.createDecryptor(cipher, iv.words);
               */
              createDecryptor: function (cipher, func28) {
                return this.Decryptor.create(cipher, func28);
              },
              /**
               * Initializes a newly created mode.
               *
               * @param {Cipher} cipher A block cipher instance.
               * @param {Array} iv The IV words.
               *
               * @example
               *
               *     var mode = CryptoJS.mode.CBC.Encryptor.create(cipher, iv.words);
               */
              init: function (cipher, func28) {
                this._cipher = cipher;
                this._iv = func28;
              }
            });

            /**
             * Cipher Block Chaining mode.
             */
            var CBC = C_mode.CBC = function () {
              /**
               * Abstract base CBC mode.
               */
              var CBC = BlockCipherMode.extend();

              /**
               * CBC encryptor.
               */
              CBC.Encryptor = CBC.extend({
                /**
                 * Processes the data block at offset.
                 *
                 * @param {Array} words The data words to operate on.
                 * @param {number} offset The offset where the block starts.
                 *
                 * @example
                 *
                 *     mode.processBlock(data.words, offset);
                 */
                processBlock: function (words, offset) {
                  // Shortcuts
                  var cipher = this._cipher;
                  var blockSize = cipher.blockSize;

                  // XOR and encrypt
                  xorBlock.call(this, words, offset, blockSize);
                  cipher.encryptBlock(words, offset);

                  // Remember this block to use with next block
                  this._prevBlock = words.slice(offset, offset + blockSize);
                }
              });

              /**
               * CBC decryptor.
               */
              CBC.Decryptor = CBC.extend({
                /**
                 * Processes the data block at offset.
                 *
                 * @param {Array} words The data words to operate on.
                 * @param {number} offset The offset where the block starts.
                 *
                 * @example
                 *
                 *     mode.processBlock(data.words, offset);
                 */
                processBlock: function (words, offset) {
                  // Shortcuts
                  var cipher = this._cipher;
                  var blockSize = cipher.blockSize;

                  // Remember this block to use with next block
                  var thisBlock = words.slice(offset, offset + blockSize);

                  // Decrypt and XOR
                  cipher.decryptBlock(words, offset);
                  xorBlock.call(this, words, offset, blockSize);

                  // This block becomes the previous block
                  this._prevBlock = thisBlock;
                }
              });
              function xorBlock(words, offset, blockSize) {
                var block;

                // Shortcut
                var func28 = this._iv;

                // Choose mixing block
                if (func28) {
                  block = func28;

                  // Remove IV for subsequent blocks
                  this._iv = undefined$1;
                } else {
                  block = this._prevBlock;
                }

                // XOR blocks
                for (var var1 = 0; var1 < blockSize; var1++) {
                  words[offset + var1] ^= block[var1];
                }
              }
              return CBC;
            }();

            /**
             * Padding namespace.
             */
            var C_pad = func23.pad = {};

            /**
             * PKCS #5/7 padding strategy.
             */
            var Pkcs7 = C_pad.Pkcs7 = {
              /**
               * Pads data using the algorithm defined in PKCS #5/7.
               *
               * @param {WordArray} data The data to pad.
               * @param {number} blockSize The multiple that the data should be padded to.
               *
               * @static
               *
               * @example
               *
               *     CryptoJS.pad.Pkcs7.pad(wordArray, 4);
               */
              pad: function (data, blockSize) {
                // Shortcut
                var blockSizeBytes = blockSize * 4;

                // Count padding bytes
                var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;

                // Create padding word
                var paddingWord = nPaddingBytes << 24 | nPaddingBytes << 16 | nPaddingBytes << 8 | nPaddingBytes;

                // Create padding
                var paddingWords = [];
                for (var var1 = 0; var1 < nPaddingBytes; var1 += 4) {
                  paddingWords.push(paddingWord);
                }
                var padding = WordArray.create(paddingWords, nPaddingBytes);

                // Add padding
                data.concat(padding);
              },
              /**
               * Unpads data that had been padded using the algorithm defined in PKCS #5/7.
               *
               * @param {WordArray} data The data to unpad.
               *
               * @static
               *
               * @example
               *
               *     CryptoJS.pad.Pkcs7.unpad(wordArray);
               */
              unpad: function (data) {
                // Get number of padding bytes from last byte
                var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 255;

                // Remove padding
                data.sigBytes -= nPaddingBytes;
              }
            };

            /**
             * Abstract base block cipher template.
             *
             * @property {number} blockSize The number of 32-bit words this cipher operates on. Default: 4 (128 bits)
             */
            C_lib.BlockCipher = Cipher.extend({
              /**
               * Configuration options.
               *
               * @property {Mode} mode The block mode to use. Default: CBC
               * @property {Padding} padding The padding strategy to use. Default: Pkcs7
               */
              cfg: Cipher.cfg.extend({
                mode: CBC,
                padding: Pkcs7
              }),
              reset: function () {
                var modeCreator;

                // Reset cipher
                Cipher.reset.call(this);

                // Shortcuts
                var cfg = this.cfg;
                var func28 = cfg.func28;
                var mode = cfg.mode;

                // Reset block mode
                if (this._xformMode == this._ENC_XFORM_MODE) {
                  modeCreator = mode.createEncryptor;
                } else /* if (this._xformMode == this._DEC_XFORM_MODE) */{
                    modeCreator = mode.createDecryptor;
                    // Keep at least one block in the buffer for unpadding
                    this._minBufferSize = 1;
                  }
                if (this._mode && this._mode.__creator == modeCreator) {
                  this._mode.init(this, func28 && func28.words);
                } else {
                  this._mode = modeCreator.call(mode, this, func28 && func28.words);
                  this._mode.__creator = modeCreator;
                }
              },
              _doProcessBlock: function (words, offset) {
                this._mode.processBlock(words, offset);
              },
              _doFinalize: function () {
                var finalProcessedBlocks;

                // Shortcut
                var padding = this.cfg.padding;

                // Finalize
                if (this._xformMode == this._ENC_XFORM_MODE) {
                  // Pad data
                  padding.pad(this._data, this.blockSize);

                  // Process final blocks
                  finalProcessedBlocks = this._process(true);
                } else /* if (this._xformMode == this._DEC_XFORM_MODE) */{
                    // Process final blocks
                    finalProcessedBlocks = this._process(true);

                    // Unpad data
                    padding.unpad(finalProcessedBlocks);
                  }
                return finalProcessedBlocks;
              },
              blockSize: 4
            });

            /**
             * A collection of cipher parameters.
             *
             * @property {WordArray} ciphertext The raw ciphertext.
             * @property {WordArray} key The key to this ciphertext.
             * @property {WordArray} iv The IV used in the ciphering operation.
             * @property {WordArray} salt The salt used with a key derivation function.
             * @property {Cipher} algorithm The cipher algorithm.
             * @property {Mode} mode The block mode used in the ciphering operation.
             * @property {Padding} padding The padding scheme used in the ciphering operation.
             * @property {number} blockSize The block size of the cipher.
             * @property {Format} formatter The default formatting strategy to convert this cipher params object to a string.
             */
            var CipherParams = C_lib.CipherParams = Base.extend({
              /**
               * Initializes a newly created cipher params object.
               *
               * @param {Object} cipherParams An object with any of the possible cipher parameters.
               *
               * @example
               *
               *     var cipherParams = CryptoJS.lib.CipherParams.create({
               *         ciphertext: ciphertextWordArray,
               *         key: keyWordArray,
               *         iv: ivWordArray,
               *         salt: saltWordArray,
               *         algorithm: CryptoJS.algo.AES,
               *         mode: CryptoJS.mode.CBC,
               *         padding: CryptoJS.pad.PKCS7,
               *         blockSize: 4,
               *         formatter: CryptoJS.format.OpenSSL
               *     });
               */
              init: function (cipherParams) {
                this.mixIn(cipherParams);
              },
              /**
               * Converts this cipher params object to a string.
               *
               * @param {Format} formatter (Optional) The formatting strategy to use.
               *
               * @return {string} The stringified cipher params.
               *
               * @throws Error If neither the formatter nor the default formatter is set.
               *
               * @example
               *
               *     var string = cipherParams + '';
               *     var string = cipherParams.toString();
               *     var string = cipherParams.toString(CryptoJS.format.OpenSSL);
               */
              toString: function (formatter) {
                return (formatter || this.formatter).stringify(this);
              }
            });

            /**
             * Format namespace.
             */
            var C_format = func23.format = {};

            /**
             * OpenSSL formatting strategy.
             */
            var OpenSSLFormatter = C_format.OpenSSL = {
              /**
               * Converts a cipher params object to an OpenSSL-compatible string.
               *
               * @param {CipherParams} cipherParams The cipher params object.
               *
               * @return {string} The OpenSSL-compatible string.
               *
               * @static
               *
               * @example
               *
               *     var openSSLString = CryptoJS.format.OpenSSL.stringify(cipherParams);
               */
              stringify: function (cipherParams) {
                var wordArray;

                // Shortcuts
                var ciphertext = cipherParams.ciphertext;
                var salt = cipherParams.salt;

                // Format
                if (salt) {
                  wordArray = WordArray.create([1398893684, 1701076831]).concat(salt).concat(ciphertext);
                } else {
                  wordArray = ciphertext;
                }
                return wordArray.toString(Base64);
              },
              /**
               * Converts an OpenSSL-compatible string to a cipher params object.
               *
               * @param {string} openSSLStr The OpenSSL-compatible string.
               *
               * @return {CipherParams} The cipher params object.
               *
               * @static
               *
               * @example
               *
               *     var cipherParams = CryptoJS.format.OpenSSL.parse(openSSLString);
               */
              parse: function (openSSLStr) {
                var salt;

                // Parse base64
                var ciphertext = Base64.parse(openSSLStr);

                // Shortcut
                var ciphertextWords = ciphertext.words;

                // Test for salt
                if (ciphertextWords[0] == 1398893684 && ciphertextWords[1] == 1701076831) {
                  // Extract salt
                  salt = WordArray.create(ciphertextWords.slice(2, 4));

                  // Remove salt from ciphertext
                  ciphertextWords.splice(0, 4);
                  ciphertext.sigBytes -= 16;
                }
                return CipherParams.create({
                  ciphertext: ciphertext,
                  salt: salt
                });
              }
            };

            /**
             * A cipher wrapper that returns ciphertext as a serializable cipher params object.
             */
            var SerializableCipher = C_lib.SerializableCipher = Base.extend({
              /**
               * Configuration options.
               *
               * @property {Formatter} format The formatting strategy to convert cipher param objects to and from a string. Default: OpenSSL
               */
              cfg: Base.extend({
                format: OpenSSLFormatter
              }),
              /**
               * Encrypts a message.
               *
               * @param {Cipher} cipher The cipher algorithm to use.
               * @param {WordArray|string} message The message to encrypt.
               * @param {WordArray} key The key.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @return {CipherParams} A cipher params object.
               *
               * @static
               *
               * @example
               *
               *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key);
               *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv });
               *     var ciphertextParams = CryptoJS.lib.SerializableCipher.encrypt(CryptoJS.algo.AES, message, key, { iv: iv, format: CryptoJS.format.OpenSSL });
               */
              encrypt: function (cipher, message, key, cfg) {
                // Apply config defaults
                cfg = this.cfg.extend(cfg);

                // Encrypt
                var encryptor = cipher.createEncryptor(key, cfg);
                var ciphertext = encryptor.finalize(message);

                // Shortcut
                var cipherCfg = encryptor.cfg;

                // Create and return serializable cipher params
                return CipherParams.create({
                  ciphertext: ciphertext,
                  key: key,
                  func28: cipherCfg.func28,
                  algorithm: cipher,
                  mode: cipherCfg.mode,
                  padding: cipherCfg.padding,
                  blockSize: cipher.blockSize,
                  formatter: cfg.format
                });
              },
              /**
               * Decrypts serialized ciphertext.
               *
               * @param {Cipher} cipher The cipher algorithm to use.
               * @param {CipherParams|string} ciphertext The ciphertext to decrypt.
               * @param {WordArray} key The key.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @return {WordArray} The plaintext.
               *
               * @static
               *
               * @example
               *
               *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, key, { iv: iv, format: CryptoJS.format.OpenSSL });
               *     var plaintext = CryptoJS.lib.SerializableCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, key, { iv: iv, format: CryptoJS.format.OpenSSL });
               */
              decrypt: function (cipher, ciphertext, key, cfg) {
                // Apply config defaults
                cfg = this.cfg.extend(cfg);

                // Convert string to CipherParams
                ciphertext = this._parse(ciphertext, cfg.format);

                // Decrypt
                var plaintext = cipher.createDecryptor(key, cfg).finalize(ciphertext.ciphertext);
                return plaintext;
              },
              /**
               * Converts serialized ciphertext to CipherParams,
               * else assumed CipherParams already and returns ciphertext unchanged.
               *
               * @param {CipherParams|string} ciphertext The ciphertext.
               * @param {Formatter} format The formatting strategy to use to parse serialized ciphertext.
               *
               * @return {CipherParams} The unserialized ciphertext.
               *
               * @static
               *
               * @example
               *
               *     var ciphertextParams = CryptoJS.lib.SerializableCipher._parse(ciphertextStringOrParams, format);
               */
              _parse: function (ciphertext, format) {
                if (typeof ciphertext == "string") {
                  return format.parse(ciphertext, this);
                } else {
                  return ciphertext;
                }
              }
            });

            /**
             * Key derivation function namespace.
             */
            var C_kdf = func23.kdf = {};

            /**
             * OpenSSL key derivation function.
             */
            var OpenSSLKdf = C_kdf.OpenSSL = {
              /**
               * Derives a key and IV from a password.
               *
               * @param {string} password The password to derive from.
               * @param {number} keySize The size in words of the key to generate.
               * @param {number} ivSize The size in words of the IV to generate.
               * @param {WordArray|string} salt (Optional) A 64-bit salt to use. If omitted, a salt will be generated randomly.
               *
               * @return {CipherParams} A cipher params object with the key, IV, and salt.
               *
               * @static
               *
               * @example
               *
               *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32);
               *     var derivedParams = CryptoJS.kdf.OpenSSL.execute('Password', 256/32, 128/32, 'saltsalt');
               */
              execute: function (password, keySize, ivSize, salt, hasher) {
                // Generate random salt
                if (!salt) {
                  salt = WordArray.random(8);
                }

                // Derive key and IV
                if (!hasher) {
                  var key = EvpKDF.create({
                    keySize: keySize + ivSize
                  }).compute(password, salt);
                } else {
                  var key = EvpKDF.create({
                    keySize: keySize + ivSize,
                    hasher: hasher
                  }).compute(password, salt);
                }

                // Separate key and IV
                var func28 = WordArray.create(key.words.slice(keySize), ivSize * 4);
                key.sigBytes = keySize * 4;

                // Return params
                return CipherParams.create({
                  key: key,
                  func28: func28,
                  salt: salt
                });
              }
            };

            /**
             * A serializable cipher wrapper that derives the key from a password,
             * and returns ciphertext as a serializable cipher params object.
             */
            var PasswordBasedCipher = C_lib.PasswordBasedCipher = SerializableCipher.extend({
              /**
               * Configuration options.
               *
               * @property {KDF} kdf The key derivation function to use to generate a key and IV from a password. Default: OpenSSL
               */
              cfg: SerializableCipher.cfg.extend({
                kdf: OpenSSLKdf
              }),
              /**
               * Encrypts a message using a password.
               *
               * @param {Cipher} cipher The cipher algorithm to use.
               * @param {WordArray|string} message The message to encrypt.
               * @param {string} password The password.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @return {CipherParams} A cipher params object.
               *
               * @static
               *
               * @example
               *
               *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password');
               *     var ciphertextParams = CryptoJS.lib.PasswordBasedCipher.encrypt(CryptoJS.algo.AES, message, 'password', { format: CryptoJS.format.OpenSSL });
               */
              encrypt: function (cipher, message, password, cfg) {
                // Apply config defaults
                cfg = this.cfg.extend(cfg);

                // Derive key and other params
                var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, cfg.salt, cfg.hasher);

                // Add IV to config
                cfg.func28 = derivedParams.func28;

                // Encrypt
                var ciphertext = SerializableCipher.encrypt.call(this, cipher, message, derivedParams.key, cfg);

                // Mix in derived params
                ciphertext.mixIn(derivedParams);
                return ciphertext;
              },
              /**
               * Decrypts serialized ciphertext using a password.
               *
               * @param {Cipher} cipher The cipher algorithm to use.
               * @param {CipherParams|string} ciphertext The ciphertext to decrypt.
               * @param {string} password The password.
               * @param {Object} cfg (Optional) The configuration options to use for this operation.
               *
               * @return {WordArray} The plaintext.
               *
               * @static
               *
               * @example
               *
               *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, formattedCiphertext, 'password', { format: CryptoJS.format.OpenSSL });
               *     var plaintext = CryptoJS.lib.PasswordBasedCipher.decrypt(CryptoJS.algo.AES, ciphertextParams, 'password', { format: CryptoJS.format.OpenSSL });
               */
              decrypt: function (cipher, ciphertext, password, cfg) {
                // Apply config defaults
                cfg = this.cfg.extend(cfg);

                // Convert string to CipherParams
                ciphertext = this._parse(ciphertext, cfg.format);

                // Derive key and other params
                var derivedParams = cfg.kdf.execute(password, cipher.keySize, cipher.ivSize, ciphertext.salt, cfg.hasher);

                // Add IV to config
                cfg.func28 = derivedParams.func28;

                // Decrypt
                var plaintext = SerializableCipher.decrypt.call(this, cipher, ciphertext, derivedParams.key, cfg);
                return plaintext;
              }
            });
          })();
        }
      });
    })(cipherCore);
    return cipherCore.exports;
  }
  var modeCfb = {
    exports: {}
  };
  var hasRequiredModeCfb;
  function requireModeCfb() {
    if (hasRequiredModeCfb) {
      return modeCfb.exports;
    }
    hasRequiredModeCfb = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * Cipher Feedback block mode.
         */
        CryptoJS.mode.CFB = function () {
          var CFB = CryptoJS.lib.BlockCipherMode.extend();
          CFB.Encryptor = CFB.extend({
            processBlock: function (words, offset) {
              // Shortcuts
              var cipher = this._cipher;
              var blockSize = cipher.blockSize;
              generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);

              // Remember this block to use with next block
              this._prevBlock = words.slice(offset, offset + blockSize);
            }
          });
          CFB.Decryptor = CFB.extend({
            processBlock: function (words, offset) {
              // Shortcuts
              var cipher = this._cipher;
              var blockSize = cipher.blockSize;

              // Remember this block to use with next block
              var thisBlock = words.slice(offset, offset + blockSize);
              generateKeystreamAndEncrypt.call(this, words, offset, blockSize, cipher);

              // This block becomes the previous block
              this._prevBlock = thisBlock;
            }
          });
          function generateKeystreamAndEncrypt(words, offset, blockSize, cipher) {
            var keystream;

            // Shortcut
            var func28 = this._iv;

            // Generate keystream
            if (func28) {
              keystream = func28.slice(0);

              // Remove IV for subsequent blocks
              this._iv = undefined;
            } else {
              keystream = this._prevBlock;
            }
            cipher.encryptBlock(keystream, 0);

            // Encrypt
            for (var var1 = 0; var1 < blockSize; var1++) {
              words[offset + var1] ^= keystream[var1];
            }
          }
          return CFB;
        }();
        return CryptoJS.mode.CFB;
      });
    })(modeCfb);
    return modeCfb.exports;
  }
  var modeCtr = {
    exports: {}
  };
  var hasRequiredModeCtr;
  function requireModeCtr() {
    if (hasRequiredModeCtr) {
      return modeCtr.exports;
    }
    hasRequiredModeCtr = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * Counter block mode.
         */
        CryptoJS.mode.CTR = function () {
          var CTR = CryptoJS.lib.BlockCipherMode.extend();
          var Encryptor = CTR.Encryptor = CTR.extend({
            processBlock: function (words, offset) {
              // Shortcuts
              var cipher = this._cipher;
              var blockSize = cipher.blockSize;
              var func28 = this._iv;
              var counter = this._counter;

              // Generate keystream
              if (func28) {
                counter = this._counter = func28.slice(0);

                // Remove IV for subsequent blocks
                this._iv = undefined;
              }
              var keystream = counter.slice(0);
              cipher.encryptBlock(keystream, 0);

              // Increment counter
              counter[blockSize - 1] = counter[blockSize - 1] + 1 | 0;

              // Encrypt
              for (var var1 = 0; var1 < blockSize; var1++) {
                words[offset + var1] ^= keystream[var1];
              }
            }
          });
          CTR.Decryptor = Encryptor;
          return CTR;
        }();
        return CryptoJS.mode.CTR;
      });
    })(modeCtr);
    return modeCtr.exports;
  }
  var modeCtrGladman = {
    exports: {}
  };
  var hasRequiredModeCtrGladman;
  function requireModeCtrGladman() {
    if (hasRequiredModeCtrGladman) {
      return modeCtrGladman.exports;
    }
    hasRequiredModeCtrGladman = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /** @preserve
         * Counter block mode compatible with  Dr Brian Gladman fileenc.c
         * derived from CryptoJS.mode.CTR
         * <NAME_EMAIL>
         */
        CryptoJS.mode.CTRGladman = function () {
          var CTRGladman = CryptoJS.lib.BlockCipherMode.extend();
          function incWord(word) {
            if ((word >> 24 & 255) === 255) {
              //overflow
              var var145 = word >> 16 & 255;
              var var146 = word >> 8 & 255;
              var var147 = word & 255;
              if (var145 === 255)
                // overflow b1
                {
                  var145 = 0;
                  if (var146 === 255) {
                    var146 = 0;
                    if (var147 === 255) {
                      var147 = 0;
                    } else {
                      ++var147;
                    }
                  } else {
                    ++var146;
                  }
                } else {
                ++var145;
              }
              word = 0;
              word += var145 << 16;
              word += var146 << 8;
              word += var147;
            } else {
              word += 16777216;
            }
            return word;
          }
          function incCounter(counter) {
            if ((counter[0] = incWord(counter[0])) === 0) {
              // encr_data in fileenc.c from  Dr Brian Gladman's counts only with DWORD j < 8
              counter[1] = incWord(counter[1]);
            }
            return counter;
          }
          var Encryptor = CTRGladman.Encryptor = CTRGladman.extend({
            processBlock: function (words, offset) {
              // Shortcuts
              var cipher = this._cipher;
              var blockSize = cipher.blockSize;
              var func28 = this._iv;
              var counter = this._counter;

              // Generate keystream
              if (func28) {
                counter = this._counter = func28.slice(0);

                // Remove IV for subsequent blocks
                this._iv = undefined;
              }
              incCounter(counter);
              var keystream = counter.slice(0);
              cipher.encryptBlock(keystream, 0);

              // Encrypt
              for (var var1 = 0; var1 < blockSize; var1++) {
                words[offset + var1] ^= keystream[var1];
              }
            }
          });
          CTRGladman.Decryptor = Encryptor;
          return CTRGladman;
        }();
        return CryptoJS.mode.CTRGladman;
      });
    })(modeCtrGladman);
    return modeCtrGladman.exports;
  }
  var modeOfb = {
    exports: {}
  };
  var hasRequiredModeOfb;
  function requireModeOfb() {
    if (hasRequiredModeOfb) {
      return modeOfb.exports;
    }
    hasRequiredModeOfb = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * Output Feedback block mode.
         */
        CryptoJS.mode.OFB = function () {
          var OFB = CryptoJS.lib.BlockCipherMode.extend();
          var Encryptor = OFB.Encryptor = OFB.extend({
            processBlock: function (words, offset) {
              // Shortcuts
              var cipher = this._cipher;
              var blockSize = cipher.blockSize;
              var func28 = this._iv;
              var keystream = this._keystream;

              // Generate keystream
              if (func28) {
                keystream = this._keystream = func28.slice(0);

                // Remove IV for subsequent blocks
                this._iv = undefined;
              }
              cipher.encryptBlock(keystream, 0);

              // Encrypt
              for (var var1 = 0; var1 < blockSize; var1++) {
                words[offset + var1] ^= keystream[var1];
              }
            }
          });
          OFB.Decryptor = Encryptor;
          return OFB;
        }();
        return CryptoJS.mode.OFB;
      });
    })(modeOfb);
    return modeOfb.exports;
  }
  var modeEcb = {
    exports: {}
  };
  var hasRequiredModeEcb;
  function requireModeEcb() {
    if (hasRequiredModeEcb) {
      return modeEcb.exports;
    }
    hasRequiredModeEcb = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * Electronic Codebook block mode.
         */
        CryptoJS.mode.ECB = function () {
          var ECB = CryptoJS.lib.BlockCipherMode.extend();
          ECB.Encryptor = ECB.extend({
            processBlock: function (words, offset) {
              this._cipher.encryptBlock(words, offset);
            }
          });
          ECB.Decryptor = ECB.extend({
            processBlock: function (words, offset) {
              this._cipher.decryptBlock(words, offset);
            }
          });
          return ECB;
        }();
        return CryptoJS.mode.ECB;
      });
    })(modeEcb);
    return modeEcb.exports;
  }
  var padAnsix923 = {
    exports: {}
  };
  var hasRequiredPadAnsix923;
  function requirePadAnsix923() {
    if (hasRequiredPadAnsix923) {
      return padAnsix923.exports;
    }
    hasRequiredPadAnsix923 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * ANSI X.923 padding strategy.
         */
        CryptoJS.pad.AnsiX923 = {
          pad: function (data, blockSize) {
            // Shortcuts
            var dataSigBytes = data.sigBytes;
            var blockSizeBytes = blockSize * 4;

            // Count padding bytes
            var nPaddingBytes = blockSizeBytes - dataSigBytes % blockSizeBytes;

            // Compute last byte position
            var lastBytePos = dataSigBytes + nPaddingBytes - 1;

            // Pad
            data.clamp();
            data.words[lastBytePos >>> 2] |= nPaddingBytes << 24 - lastBytePos % 4 * 8;
            data.sigBytes += nPaddingBytes;
          },
          unpad: function (data) {
            // Get number of padding bytes from last byte
            var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 255;

            // Remove padding
            data.sigBytes -= nPaddingBytes;
          }
        };
        return CryptoJS.pad.Ansix923;
      });
    })(padAnsix923);
    return padAnsix923.exports;
  }
  var padIso10126 = {
    exports: {}
  };
  var hasRequiredPadIso10126;
  function requirePadIso10126() {
    if (hasRequiredPadIso10126) {
      return padIso10126.exports;
    }
    hasRequiredPadIso10126 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * ISO 10126 padding strategy.
         */
        CryptoJS.pad.var148 = {
          pad: function (data, blockSize) {
            // Shortcut
            var blockSizeBytes = blockSize * 4;

            // Count padding bytes
            var nPaddingBytes = blockSizeBytes - data.sigBytes % blockSizeBytes;

            // Pad
            data.concat(CryptoJS.lib.WordArray.random(nPaddingBytes - 1)).concat(CryptoJS.lib.WordArray.create([nPaddingBytes << 24], 1));
          },
          unpad: function (data) {
            // Get number of padding bytes from last byte
            var nPaddingBytes = data.words[data.sigBytes - 1 >>> 2] & 255;

            // Remove padding
            data.sigBytes -= nPaddingBytes;
          }
        };
        return CryptoJS.pad.var148;
      });
    })(padIso10126);
    return padIso10126.exports;
  }
  var padIso97971 = {
    exports: {}
  };
  var hasRequiredPadIso97971;
  function requirePadIso97971() {
    if (hasRequiredPadIso97971) {
      return padIso97971.exports;
    }
    hasRequiredPadIso97971 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * ISO/IEC 9797-1 Padding Method 2.
         */
        CryptoJS.pad.var149 = {
          pad: function (data, blockSize) {
            // Add 0x80 byte
            data.concat(CryptoJS.lib.WordArray.create([2147483648], 1));

            // Zero pad the rest
            CryptoJS.pad.ZeroPadding.pad(data, blockSize);
          },
          unpad: function (data) {
            // Remove zero padding
            CryptoJS.pad.ZeroPadding.unpad(data);

            // Remove one more byte -- the 0x80 byte
            data.sigBytes--;
          }
        };
        return CryptoJS.pad.var149;
      });
    })(padIso97971);
    return padIso97971.exports;
  }
  var padZeropadding = {
    exports: {}
  };
  var hasRequiredPadZeropadding;
  function requirePadZeropadding() {
    if (hasRequiredPadZeropadding) {
      return padZeropadding.exports;
    }
    hasRequiredPadZeropadding = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * Zero padding strategy.
         */
        CryptoJS.pad.ZeroPadding = {
          pad: function (data, blockSize) {
            // Shortcut
            var blockSizeBytes = blockSize * 4;

            // Pad
            data.clamp();
            data.sigBytes += blockSizeBytes - (data.sigBytes % blockSizeBytes || blockSizeBytes);
          },
          unpad: function (data) {
            // Shortcut
            var dataWords = data.words;

            // Unpad
            var var1 = data.sigBytes - 1;
            for (var var1 = data.sigBytes - 1; var1 >= 0; var1--) {
              if (dataWords[var1 >>> 2] >>> 24 - var1 % 4 * 8 & 255) {
                data.sigBytes = var1 + 1;
                break;
              }
            }
          }
        };
        return CryptoJS.pad.ZeroPadding;
      });
    })(padZeropadding);
    return padZeropadding.exports;
  }
  var padNopadding = {
    exports: {}
  };
  var hasRequiredPadNopadding;
  function requirePadNopadding() {
    if (hasRequiredPadNopadding) {
      return padNopadding.exports;
    }
    hasRequiredPadNopadding = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        /**
         * A noop padding strategy.
         */
        CryptoJS.pad.NoPadding = {
          pad: function () {},
          unpad: function () {}
        };
        return CryptoJS.pad.NoPadding;
      });
    })(padNopadding);
    return padNopadding.exports;
  }
  var formatHex = {
    exports: {}
  };
  var hasRequiredFormatHex;
  function requireFormatHex() {
    if (hasRequiredFormatHex) {
      return formatHex.exports;
    }
    hasRequiredFormatHex = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function (undefined$1) {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var CipherParams = C_lib.CipherParams;
          var C_enc = func23.enc;
          var Hex = C_enc.Hex;
          var C_format = func23.format;
          C_format.Hex = {
            /**
             * Converts the ciphertext of a cipher params object to a hexadecimally encoded string.
             *
             * @param {CipherParams} cipherParams The cipher params object.
             *
             * @return {string} The hexadecimally encoded string.
             *
             * @static
             *
             * @example
             *
             *     var hexString = CryptoJS.format.Hex.stringify(cipherParams);
             */
            stringify: function (cipherParams) {
              return cipherParams.ciphertext.toString(Hex);
            },
            /**
             * Converts a hexadecimally encoded ciphertext string to a cipher params object.
             *
             * @param {string} input The hexadecimally encoded string.
             *
             * @return {CipherParams} The cipher params object.
             *
             * @static
             *
             * @example
             *
             *     var cipherParams = CryptoJS.format.Hex.parse(hexString);
             */
            parse: function (input) {
              var ciphertext = Hex.parse(input);
              return CipherParams.create({
                ciphertext: ciphertext
              });
            }
          };
        })();
        return CryptoJS.format.Hex;
      });
    })(formatHex);
    return formatHex.exports;
  }
  var aes = {
    exports: {}
  };
  var hasRequiredAes;
  function requireAes() {
    if (hasRequiredAes) {
      return aes.exports;
    }
    hasRequiredAes = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var BlockCipher = C_lib.BlockCipher;
          var C_algo = func23.algo;

          // Lookup tables
          var SBOX = [];
          var INV_SBOX = [];
          var SUB_MIX_0 = [];
          var SUB_MIX_1 = [];
          var SUB_MIX_2 = [];
          var SUB_MIX_3 = [];
          var INV_SUB_MIX_0 = [];
          var INV_SUB_MIX_1 = [];
          var INV_SUB_MIX_2 = [];
          var INV_SUB_MIX_3 = [];

          // Compute lookup tables
          (function () {
            // Compute double table
            var var5 = [];
            for (var var1 = 0; var1 < 256; var1++) {
              if (var1 < 128) {
                var5[var1] = var1 << 1;
              } else {
                var5[var1] = var1 << 1 ^ 283;
              }
            }

            // Walk GF(2^8)
            var func3 = 0;
            var num2 = 0;
            for (var var1 = 0; var1 < 256; var1++) {
              // Compute sbox
              var var150 = num2 ^ num2 << 1 ^ num2 << 2 ^ num2 << 3 ^ num2 << 4;
              var150 = var150 >>> 8 ^ var150 & 255 ^ 99;
              SBOX[func3] = var150;
              INV_SBOX[var150] = func3;

              // Compute multiplication
              var var151 = var5[func3];
              var var152 = var5[var151];
              var var153 = var5[var152];

              // Compute sub bytes, mix columns tables
              var func2 = var5[var150] * 257 ^ var150 * 16843008;
              SUB_MIX_0[func3] = func2 << 24 | func2 >>> 8;
              SUB_MIX_1[func3] = func2 << 16 | func2 >>> 16;
              SUB_MIX_2[func3] = func2 << 8 | func2 >>> 24;
              SUB_MIX_3[func3] = func2;

              // Compute inv sub bytes, inv mix columns tables
              var func2 = var153 * 16843009 ^ var152 * 65537 ^ var151 * 257 ^ func3 * 16843008;
              INV_SUB_MIX_0[var150] = func2 << 24 | func2 >>> 8;
              INV_SUB_MIX_1[var150] = func2 << 16 | func2 >>> 16;
              INV_SUB_MIX_2[var150] = func2 << 8 | func2 >>> 24;
              INV_SUB_MIX_3[var150] = func2;

              // Compute next counter
              if (!func3) {
                func3 = num2 = 1;
              } else {
                func3 = var151 ^ var5[var5[var5[var153 ^ var151]]];
                num2 ^= var5[var5[num2]];
              }
            }
          })();

          // Precomputed Rcon lookup
          var RCON = [0, 1, 2, 4, 8, 16, 32, 64, 128, 27, 54];

          /**
           * AES block cipher algorithm.
           */
          var AES = C_algo.AES = BlockCipher.extend({
            _doReset: function () {
              var func2;

              // Skip reset of nRounds has been set before and key did not change
              if (this._nRounds && this._keyPriorReset === this._key) {
                return;
              }

              // Shortcuts
              var key = this._keyPriorReset = this._key;
              var keyWords = key.words;
              var keySize = key.sigBytes / 4;

              // Compute number of rounds
              var nRounds = this._nRounds = keySize + 6;

              // Compute number of key schedule rows
              var ksRows = (nRounds + 1) * 4;

              // Compute key schedule
              var keySchedule = this._keySchedule = [];
              for (var ksRow = 0; ksRow < ksRows; ksRow++) {
                if (ksRow < keySize) {
                  keySchedule[ksRow] = keyWords[ksRow];
                } else {
                  func2 = keySchedule[ksRow - 1];
                  if (!(ksRow % keySize)) {
                    // Rot word
                    func2 = func2 << 8 | func2 >>> 24;

                    // Sub word
                    func2 = SBOX[func2 >>> 24] << 24 | SBOX[func2 >>> 16 & 255] << 16 | SBOX[func2 >>> 8 & 255] << 8 | SBOX[func2 & 255];

                    // Mix Rcon
                    func2 ^= RCON[ksRow / keySize | 0] << 24;
                  } else if (keySize > 6 && ksRow % keySize == 4) {
                    // Sub word
                    func2 = SBOX[func2 >>> 24] << 24 | SBOX[func2 >>> 16 & 255] << 16 | SBOX[func2 >>> 8 & 255] << 8 | SBOX[func2 & 255];
                  }
                  keySchedule[ksRow] = keySchedule[ksRow - keySize] ^ func2;
                }
              }

              // Compute inv key schedule
              var invKeySchedule = this._invKeySchedule = [];
              for (var invKsRow = 0; invKsRow < ksRows; invKsRow++) {
                var ksRow = ksRows - invKsRow;
                if (invKsRow % 4) {
                  var func2 = keySchedule[ksRow];
                } else {
                  var func2 = keySchedule[ksRow - 4];
                }
                if (invKsRow < 4 || ksRow <= 4) {
                  invKeySchedule[invKsRow] = func2;
                } else {
                  invKeySchedule[invKsRow] = INV_SUB_MIX_0[SBOX[func2 >>> 24]] ^ INV_SUB_MIX_1[SBOX[func2 >>> 16 & 255]] ^ INV_SUB_MIX_2[SBOX[func2 >>> 8 & 255]] ^ INV_SUB_MIX_3[SBOX[func2 & 255]];
                }
              }
            },
            encryptBlock: function (func27, offset) {
              this._doCryptBlock(func27, offset, this._keySchedule, SUB_MIX_0, SUB_MIX_1, SUB_MIX_2, SUB_MIX_3, SBOX);
            },
            decryptBlock: function (func27, offset) {
              // Swap 2nd and 4th rows
              var func2 = func27[offset + 1];
              func27[offset + 1] = func27[offset + 3];
              func27[offset + 3] = func2;
              this._doCryptBlock(func27, offset, this._invKeySchedule, INV_SUB_MIX_0, INV_SUB_MIX_1, INV_SUB_MIX_2, INV_SUB_MIX_3, INV_SBOX);

              // Inv swap 2nd and 4th rows
              var func2 = func27[offset + 1];
              func27[offset + 1] = func27[offset + 3];
              func27[offset + 3] = func2;
            },
            _doCryptBlock: function (func27, offset, keySchedule, SUB_MIX_0, SUB_MIX_1, SUB_MIX_2, SUB_MIX_3, SBOX) {
              // Shortcut
              var nRounds = this._nRounds;

              // Get input, add round key
              var var154 = func27[offset] ^ keySchedule[0];
              var var155 = func27[offset + 1] ^ keySchedule[1];
              var var156 = func27[offset + 2] ^ keySchedule[2];
              var var157 = func27[offset + 3] ^ keySchedule[3];

              // Key schedule row counter
              var ksRow = 4;

              // Rounds
              for (var round = 1; round < nRounds; round++) {
                // Shift rows, sub bytes, mix columns, add round key
                var var158 = SUB_MIX_0[var154 >>> 24] ^ SUB_MIX_1[var155 >>> 16 & 255] ^ SUB_MIX_2[var156 >>> 8 & 255] ^ SUB_MIX_3[var157 & 255] ^ keySchedule[ksRow++];
                var var67 = SUB_MIX_0[var155 >>> 24] ^ SUB_MIX_1[var156 >>> 16 & 255] ^ SUB_MIX_2[var157 >>> 8 & 255] ^ SUB_MIX_3[var154 & 255] ^ keySchedule[ksRow++];
                var var99 = SUB_MIX_0[var156 >>> 24] ^ SUB_MIX_1[var157 >>> 16 & 255] ^ SUB_MIX_2[var154 >>> 8 & 255] ^ SUB_MIX_3[var155 & 255] ^ keySchedule[ksRow++];
                var var159 = SUB_MIX_0[var157 >>> 24] ^ SUB_MIX_1[var154 >>> 16 & 255] ^ SUB_MIX_2[var155 >>> 8 & 255] ^ SUB_MIX_3[var156 & 255] ^ keySchedule[ksRow++];

                // Update state
                var154 = var158;
                var155 = var67;
                var156 = var99;
                var157 = var159;
              }

              // Shift rows, sub bytes, add round key
              var var158 = (SBOX[var154 >>> 24] << 24 | SBOX[var155 >>> 16 & 255] << 16 | SBOX[var156 >>> 8 & 255] << 8 | SBOX[var157 & 255]) ^ keySchedule[ksRow++];
              var var67 = (SBOX[var155 >>> 24] << 24 | SBOX[var156 >>> 16 & 255] << 16 | SBOX[var157 >>> 8 & 255] << 8 | SBOX[var154 & 255]) ^ keySchedule[ksRow++];
              var var99 = (SBOX[var156 >>> 24] << 24 | SBOX[var157 >>> 16 & 255] << 16 | SBOX[var154 >>> 8 & 255] << 8 | SBOX[var155 & 255]) ^ keySchedule[ksRow++];
              var var159 = (SBOX[var157 >>> 24] << 24 | SBOX[var154 >>> 16 & 255] << 16 | SBOX[var155 >>> 8 & 255] << 8 | SBOX[var156 & 255]) ^ keySchedule[ksRow++];

              // Set output
              func27[offset] = var158;
              func27[offset + 1] = var67;
              func27[offset + 2] = var99;
              func27[offset + 3] = var159;
            },
            keySize: 8
          });

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.AES.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.AES.decrypt(ciphertext, key, cfg);
           */
          func23.AES = BlockCipher._createHelper(AES);
        })();
        return CryptoJS.AES;
      });
    })(aes);
    return aes.exports;
  }
  var tripledes = {
    exports: {}
  };
  var hasRequiredTripledes;
  function requireTripledes() {
    if (hasRequiredTripledes) {
      return tripledes.exports;
    }
    hasRequiredTripledes = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var WordArray = C_lib.WordArray;
          var BlockCipher = C_lib.BlockCipher;
          var C_algo = func23.algo;

          // Permuted Choice 1 constants
          var arr4 = [57, 49, 41, 33, 25, 17, 9, 1, 58, 50, 42, 34, 26, 18, 10, 2, 59, 51, 43, 35, 27, 19, 11, 3, 60, 52, 44, 36, 63, 55, 47, 39, 31, 23, 15, 7, 62, 54, 46, 38, 30, 22, 14, 6, 61, 53, 45, 37, 29, 21, 13, 5, 28, 20, 12, 4];

          // Permuted Choice 2 constants
          var arr5 = [14, 17, 11, 24, 1, 5, 3, 28, 15, 6, 21, 10, 23, 19, 12, 4, 26, 8, 16, 7, 27, 20, 13, 2, 41, 52, 31, 37, 47, 55, 30, 40, 51, 45, 33, 48, 44, 49, 39, 56, 34, 53, 46, 42, 50, 36, 29, 32];

          // Cumulative bit shift constants
          var BIT_SHIFTS = [1, 2, 4, 6, 8, 10, 12, 14, 15, 17, 19, 21, 23, 25, 27, 28];

          // SBOXes and round permutation constants
          var SBOX_P = [{
            0: 8421888,
            268435456: 32768,
            536870912: 8421378,
            805306368: 2,
            1073741824: 512,
            1342177280: 8421890,
            1610612736: 8389122,
            1879048192: 8388608,
            2147483648: 514,
            2415919104: 8389120,
            2684354560: 33280,
            2952790016: 8421376,
            3221225472: 32770,
            3489660928: 8388610,
            3758096384: 0,
            4026531840: 33282,
            134217728: 0,
            402653184: 8421890,
            671088640: 33282,
            939524096: 32768,
            1207959552: 8421888,
            1476395008: 512,
            1744830464: 8421378,
            2013265920: 2,
            2281701376: 8389120,
            2550136832: 33280,
            2818572288: 8421376,
            3087007744: 8389122,
            3355443200: 8388610,
            3623878656: 32770,
            3892314112: 514,
            4160749568: 8388608,
            1: 32768,
            268435457: 2,
            536870913: 8421888,
            805306369: 8388608,
            1073741825: 8421378,
            1342177281: 33280,
            1610612737: 512,
            1879048193: 8389122,
            2147483649: 8421890,
            2415919105: 8421376,
            2684354561: 8388610,
            2952790017: 33282,
            3221225473: 514,
            3489660929: 8389120,
            3758096385: 32770,
            4026531841: 0,
            134217729: 8421890,
            402653185: 8421376,
            671088641: 8388608,
            939524097: 512,
            1207959553: 32768,
            1476395009: 8388610,
            1744830465: 2,
            2013265921: 33282,
            2281701377: 32770,
            2550136833: 8389122,
            2818572289: 514,
            3087007745: 8421888,
            3355443201: 8389120,
            3623878657: 0,
            3892314113: 33280,
            4160749569: 8421378
          }, {
            0: 1074282512,
            16777216: 16384,
            33554432: 524288,
            50331648: 1074266128,
            67108864: 1073741840,
            83886080: 1074282496,
            100663296: 1073758208,
            117440512: 16,
            134217728: 540672,
            150994944: 1073758224,
            167772160: 1073741824,
            184549376: 540688,
            201326592: 524304,
            218103808: 0,
            234881024: 16400,
            251658240: 1074266112,
            8388608: 1073758208,
            25165824: 540688,
            41943040: 16,
            58720256: 1073758224,
            75497472: 1074282512,
            92274688: 1073741824,
            109051904: 524288,
            125829120: 1074266128,
            142606336: 524304,
            159383552: 0,
            176160768: 16384,
            192937984: 1074266112,
            209715200: 1073741840,
            226492416: 540672,
            243269632: 1074282496,
            260046848: 16400,
            268435456: 0,
            285212672: 1074266128,
            301989888: 1073758224,
            318767104: 1074282496,
            335544320: 1074266112,
            352321536: 16,
            369098752: 540688,
            385875968: 16384,
            402653184: 16400,
            419430400: 524288,
            436207616: 524304,
            452984832: 1073741840,
            469762048: 540672,
            486539264: 1073758208,
            503316480: 1073741824,
            520093696: 1074282512,
            276824064: 540688,
            293601280: 524288,
            310378496: 1074266112,
            327155712: 16384,
            343932928: 1073758208,
            360710144: 1074282512,
            377487360: 16,
            394264576: 1073741824,
            411041792: 1074282496,
            427819008: 1073741840,
            444596224: 1073758224,
            461373440: 524304,
            478150656: 0,
            494927872: 16400,
            511705088: 1074266128,
            528482304: 540672
          }, {
            0: 260,
            1048576: 0,
            2097152: 67109120,
            3145728: 65796,
            4194304: 65540,
            5242880: 67108868,
            6291456: 67174660,
            7340032: 67174400,
            8388608: 67108864,
            9437184: 67174656,
            10485760: 65792,
            11534336: 67174404,
            12582912: 67109124,
            13631488: 65536,
            14680064: 4,
            15728640: 256,
            524288: 67174656,
            1572864: 67174404,
            2621440: 0,
            3670016: 67109120,
            4718592: 67108868,
            5767168: 65536,
            6815744: 65540,
            7864320: 260,
            8912896: 4,
            9961472: 256,
            11010048: 67174400,
            12058624: 65796,
            13107200: 65792,
            14155776: 67109124,
            15204352: 67174660,
            16252928: 67108864,
            16777216: 67174656,
            17825792: 65540,
            18874368: 65536,
            19922944: 67109120,
            20971520: 256,
            22020096: 67174660,
            23068672: 67108868,
            24117248: 0,
            25165824: 67109124,
            26214400: 67108864,
            27262976: 4,
            28311552: 65792,
            29360128: 67174400,
            30408704: 260,
            31457280: 65796,
            32505856: 67174404,
            17301504: 67108864,
            18350080: 260,
            19398656: 67174656,
            20447232: 0,
            21495808: 65540,
            22544384: 67109120,
            23592960: 256,
            24641536: 67174404,
            25690112: 65536,
            26738688: 67174660,
            27787264: 65796,
            28835840: 67108868,
            29884416: 67109124,
            30932992: 67174400,
            31981568: 4,
            33030144: 65792
          }, {
            0: 2151682048,
            65536: 2147487808,
            131072: 4198464,
            196608: 2151677952,
            262144: 0,
            327680: 4198400,
            393216: 2147483712,
            458752: 4194368,
            524288: 2147483648,
            589824: 4194304,
            655360: 64,
            720896: 2147487744,
            786432: 2151678016,
            851968: 4160,
            917504: 4096,
            983040: 2151682112,
            32768: 2147487808,
            98304: 64,
            163840: 2151678016,
            229376: 2147487744,
            294912: 4198400,
            360448: 2151682112,
            425984: 0,
            491520: 2151677952,
            557056: 4096,
            622592: 2151682048,
            688128: 4194304,
            753664: 4160,
            819200: 2147483648,
            884736: 4194368,
            950272: 4198464,
            1015808: 2147483712,
            1048576: 4194368,
            1114112: 4198400,
            1179648: 2147483712,
            1245184: 0,
            1310720: 4160,
            1376256: 2151678016,
            1441792: 2151682048,
            1507328: 2147487808,
            1572864: 2151682112,
            1638400: 2147483648,
            1703936: 2151677952,
            1769472: 4198464,
            1835008: 2147487744,
            1900544: 4194304,
            1966080: 64,
            2031616: 4096,
            1081344: 2151677952,
            1146880: 2151682112,
            1212416: 0,
            1277952: 4198400,
            1343488: 4194368,
            1409024: 2147483648,
            1474560: 2147487808,
            1540096: 64,
            1605632: 2147483712,
            1671168: 4096,
            1736704: 2147487744,
            1802240: 2151678016,
            1867776: 4160,
            1933312: 2151682048,
            1998848: 4194304,
            2064384: 4198464
          }, {
            0: 128,
            4096: 17039360,
            8192: 262144,
            12288: 536870912,
            16384: 537133184,
            20480: 16777344,
            24576: 553648256,
            28672: 262272,
            32768: 16777216,
            36864: 537133056,
            40960: 536871040,
            45056: 553910400,
            49152: 553910272,
            53248: 0,
            57344: 17039488,
            61440: 553648128,
            2048: 17039488,
            6144: 553648256,
            10240: 128,
            14336: 17039360,
            18432: 262144,
            22528: 537133184,
            26624: 553910272,
            30720: 536870912,
            34816: 537133056,
            38912: 0,
            43008: 553910400,
            47104: 16777344,
            51200: 536871040,
            55296: 553648128,
            59392: 16777216,
            63488: 262272,
            65536: 262144,
            69632: 128,
            73728: 536870912,
            77824: 553648256,
            81920: 16777344,
            86016: 553910272,
            90112: 537133184,
            94208: 16777216,
            98304: 553910400,
            102400: 553648128,
            106496: 17039360,
            110592: 537133056,
            114688: 262272,
            118784: 536871040,
            122880: 0,
            126976: 17039488,
            67584: 553648256,
            71680: 16777216,
            75776: 17039360,
            79872: 537133184,
            83968: 536870912,
            88064: 17039488,
            92160: 128,
            96256: 553910272,
            100352: 262272,
            104448: 553910400,
            108544: 0,
            112640: 553648128,
            116736: 16777344,
            120832: 262144,
            124928: 537133056,
            129024: 536871040
          }, {
            0: 268435464,
            256: 8192,
            512: 270532608,
            768: 270540808,
            1024: 268443648,
            1280: 2097152,
            1536: 2097160,
            1792: 268435456,
            2048: 0,
            2304: 268443656,
            2560: 2105344,
            2816: 8,
            3072: 270532616,
            3328: 2105352,
            3584: 8200,
            3840: 270540800,
            128: 270532608,
            384: 270540808,
            640: 8,
            896: 2097152,
            1152: 2105352,
            1408: 268435464,
            1664: 268443648,
            1920: 8200,
            2176: 2097160,
            2432: 8192,
            2688: 268443656,
            2944: 270532616,
            3200: 0,
            3456: 270540800,
            3712: 2105344,
            3968: 268435456,
            4096: 268443648,
            4352: 270532616,
            4608: 270540808,
            4864: 8200,
            5120: 2097152,
            5376: 268435456,
            5632: 268435464,
            5888: 2105344,
            6144: 2105352,
            6400: 0,
            6656: 8,
            6912: 270532608,
            7168: 8192,
            7424: 268443656,
            7680: 270540800,
            7936: 2097160,
            4224: 8,
            4480: 2105344,
            4736: 2097152,
            4992: 268435464,
            5248: 268443648,
            5504: 8200,
            5760: 270540808,
            6016: 270532608,
            6272: 270540800,
            6528: 270532616,
            6784: 8192,
            7040: 2105352,
            7296: 2097160,
            7552: 0,
            7808: 268435456,
            8064: 268443656
          }, {
            0: 1048576,
            16: 33555457,
            32: 1024,
            48: 1049601,
            64: 34604033,
            80: 0,
            96: 1,
            112: 34603009,
            128: 33555456,
            144: 1048577,
            160: 33554433,
            176: 34604032,
            192: 34603008,
            208: 1025,
            224: 1049600,
            240: 33554432,
            8: 34603009,
            24: 0,
            40: 33555457,
            56: 34604032,
            72: 1048576,
            88: 33554433,
            104: 33554432,
            120: 1025,
            136: 1049601,
            152: 33555456,
            168: 34603008,
            184: 1048577,
            200: 1024,
            216: 34604033,
            232: 1,
            248: 1049600,
            256: 33554432,
            272: 1048576,
            288: 33555457,
            304: 34603009,
            320: 1048577,
            336: 33555456,
            352: 34604032,
            368: 1049601,
            384: 1025,
            400: 34604033,
            416: 1049600,
            432: 1,
            448: 0,
            464: 34603008,
            480: 33554433,
            496: 1024,
            264: 1049600,
            280: 33555457,
            296: 34603009,
            312: 1,
            328: 33554432,
            344: 1048576,
            360: 1025,
            376: 34604032,
            392: 33554433,
            408: 34603008,
            424: 0,
            440: 34604033,
            456: 1049601,
            472: 1024,
            488: 33555456,
            504: 1048577
          }, {
            0: 134219808,
            1: 131072,
            2: 134217728,
            3: 32,
            4: 131104,
            5: 134350880,
            6: 134350848,
            7: 2048,
            8: 134348800,
            9: 134219776,
            10: 133120,
            11: 134348832,
            12: 2080,
            13: 0,
            14: 134217760,
            15: 133152,
            2147483648: 2048,
            2147483649: 134350880,
            2147483650: 134219808,
            2147483651: 134217728,
            2147483652: 134348800,
            2147483653: 133120,
            2147483654: 133152,
            2147483655: 32,
            2147483656: 134217760,
            2147483657: 2080,
            2147483658: 131104,
            2147483659: 134350848,
            2147483660: 0,
            2147483661: 134348832,
            2147483662: 134219776,
            2147483663: 131072,
            16: 133152,
            17: 134350848,
            18: 32,
            19: 2048,
            20: 134219776,
            21: 134217760,
            22: 134348832,
            23: 131072,
            24: 0,
            25: 131104,
            26: 134348800,
            27: 134219808,
            28: 134350880,
            29: 133120,
            30: 2080,
            31: 134217728,
            2147483664: 131072,
            2147483665: 2048,
            2147483666: 134348832,
            2147483667: 133152,
            2147483668: 32,
            2147483669: 134348800,
            2147483670: 134217728,
            2147483671: 134219808,
            2147483672: 134350880,
            2147483673: 134217760,
            2147483674: 134219776,
            2147483675: 0,
            2147483676: 133120,
            2147483677: 2080,
            2147483678: 131104,
            2147483679: 134350848
          }];

          // Masks that select the SBOX input
          var SBOX_MASK = [4160749569, 528482304, 33030144, 2064384, 129024, 8064, 504, 2147483679];

          /**
           * DES block cipher algorithm.
           */
          var DES = C_algo.DES = BlockCipher.extend({
            _doReset: function () {
              // Shortcuts
              var key = this._key;
              var keyWords = key.words;

              // Select 56 bits according to PC1
              var keyBits = [];
              for (var var1 = 0; var1 < 56; var1++) {
                var keyBitPos = arr4[var1] - 1;
                keyBits[var1] = keyWords[keyBitPos >>> 5] >>> 31 - keyBitPos % 32 & 1;
              }

              // Assemble 16 subkeys
              var subKeys = this._subKeys = [];
              for (var nSubKey = 0; nSubKey < 16; nSubKey++) {
                // Create subkey
                var subKey = subKeys[nSubKey] = [];

                // Shortcut
                var bitShift = BIT_SHIFTS[nSubKey];

                // Select 48 bits according to PC2
                for (var var1 = 0; var1 < 24; var1++) {
                  // Select from the left 28 key bits
                  subKey[var1 / 6 | 0] |= keyBits[(arr5[var1] - 1 + bitShift) % 28] << 31 - var1 % 6;

                  // Select from the right 28 key bits
                  subKey[4 + (var1 / 6 | 0)] |= keyBits[28 + (arr5[var1 + 24] - 1 + bitShift) % 28] << 31 - var1 % 6;
                }

                // Since each subkey is applied to an expanded 32-bit input,
                // the subkey can be broken into 8 values scaled to 32-bits,
                // which allows the key to be used without expansion
                subKey[0] = subKey[0] << 1 | subKey[0] >>> 31;
                for (var var1 = 1; var1 < 7; var1++) {
                  subKey[var1] = subKey[var1] >>> (var1 - 1) * 4 + 3;
                }
                subKey[7] = subKey[7] << 5 | subKey[7] >>> 27;
              }

              // Compute inverse subkeys
              var invSubKeys = this._invSubKeys = [];
              for (var var1 = 0; var1 < 16; var1++) {
                invSubKeys[var1] = subKeys[15 - var1];
              }
            },
            encryptBlock: function (func27, offset) {
              this._doCryptBlock(func27, offset, this._subKeys);
            },
            decryptBlock: function (func27, offset) {
              this._doCryptBlock(func27, offset, this._invSubKeys);
            },
            _doCryptBlock: function (func27, offset, subKeys) {
              // Get input
              this._lBlock = func27[offset];
              this._rBlock = func27[offset + 1];

              // Initial permutation
              exchangeLR.call(this, 4, 252645135);
              exchangeLR.call(this, 16, 65535);
              exchangeRL.call(this, 2, 858993459);
              exchangeRL.call(this, 8, 16711935);
              exchangeLR.call(this, 1, 1431655765);

              // Rounds
              for (var round = 0; round < 16; round++) {
                // Shortcuts
                var subKey = subKeys[round];
                var lBlock = this._lBlock;
                var rBlock = this._rBlock;

                // Feistel function
                var var3 = 0;
                for (var var1 = 0; var1 < 8; var1++) {
                  var3 |= SBOX_P[var1][((rBlock ^ subKey[var1]) & SBOX_MASK[var1]) >>> 0];
                }
                this._lBlock = rBlock;
                this._rBlock = lBlock ^ var3;
              }

              // Undo swap from last round
              var func2 = this._lBlock;
              this._lBlock = this._rBlock;
              this._rBlock = func2;

              // Final permutation
              exchangeLR.call(this, 1, 1431655765);
              exchangeRL.call(this, 8, 16711935);
              exchangeRL.call(this, 2, 858993459);
              exchangeLR.call(this, 16, 65535);
              exchangeLR.call(this, 4, 252645135);

              // Set output
              func27[offset] = this._lBlock;
              func27[offset + 1] = this._rBlock;
            },
            keySize: 2,
            ivSize: 2,
            blockSize: 2
          });

          // Swap bits across the left and right words
          function exchangeLR(offset, mask) {
            var func2 = (this._lBlock >>> offset ^ this._rBlock) & mask;
            this._rBlock ^= func2;
            this._lBlock ^= func2 << offset;
          }
          function exchangeRL(offset, mask) {
            var func2 = (this._rBlock >>> offset ^ this._lBlock) & mask;
            this._lBlock ^= func2;
            this._rBlock ^= func2 << offset;
          }

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.DES.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.DES.decrypt(ciphertext, key, cfg);
           */
          func23.DES = BlockCipher._createHelper(DES);

          /**
           * Triple-DES block cipher algorithm.
           */
          var TripleDES = C_algo.TripleDES = BlockCipher.extend({
            _doReset: function () {
              // Shortcuts
              var key = this._key;
              var keyWords = key.words;
              // Make sure the key length is valid (64, 128 or >= 192 bit)
              if (keyWords.length !== 2 && keyWords.length !== 4 && keyWords.length < 6) {
                throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");
              }

              // Extend the key according to the keying options defined in 3DES standard
              var var160 = keyWords.slice(0, 2);
              var var161 = keyWords.length < 4 ? keyWords.slice(0, 2) : keyWords.slice(2, 4);
              var var162 = keyWords.length < 6 ? keyWords.slice(0, 2) : keyWords.slice(4, 6);

              // Create DES instances
              this._des1 = DES.createEncryptor(WordArray.create(var160));
              this._des2 = DES.createEncryptor(WordArray.create(var161));
              this._des3 = DES.createEncryptor(WordArray.create(var162));
            },
            encryptBlock: function (func27, offset) {
              this._des1.encryptBlock(func27, offset);
              this._des2.decryptBlock(func27, offset);
              this._des3.encryptBlock(func27, offset);
            },
            decryptBlock: function (func27, offset) {
              this._des3.decryptBlock(func27, offset);
              this._des2.encryptBlock(func27, offset);
              this._des1.decryptBlock(func27, offset);
            },
            keySize: 6,
            ivSize: 2,
            blockSize: 2
          });

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.TripleDES.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.TripleDES.decrypt(ciphertext, key, cfg);
           */
          func23.TripleDES = BlockCipher._createHelper(TripleDES);
        })();
        return CryptoJS.TripleDES;
      });
    })(tripledes);
    return tripledes.exports;
  }
  var obj1 = {
    exports: {}
  };
  var hasRequiredRc4;
  function requireRc4() {
    if (hasRequiredRc4) {
      return obj1.exports;
    }
    hasRequiredRc4 = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var StreamCipher = C_lib.StreamCipher;
          var C_algo = func23.algo;

          /**
           * RC4 stream cipher algorithm.
           */
          var var163 = C_algo.var163 = StreamCipher.extend({
            _doReset: function () {
              // Shortcuts
              var key = this._key;
              var keyWords = key.words;
              var keySigBytes = key.sigBytes;

              // Init sbox
              var var61 = this.var164 = [];
              for (var var1 = 0; var1 < 256; var1++) {
                var61[var1] = var1;
              }

              // Key setup
              for (var var1 = 0, num = 0; var1 < 256; var1++) {
                var keyByteIndex = var1 % keySigBytes;
                var keyByte = keyWords[keyByteIndex >>> 2] >>> 24 - keyByteIndex % 4 * 8 & 255;
                num = (num + var61[var1] + keyByte) % 256;

                // Swap
                var func2 = var61[var1];
                var61[var1] = var61[num];
                var61[num] = func2;
              }

              // Counters
              this.var165 = this.var166 = 0;
            },
            _doProcessBlock: function (func27, offset) {
              func27[offset] ^= generateKeystreamWord.call(this);
            },
            keySize: 8,
            ivSize: 0
          });
          function generateKeystreamWord() {
            // Shortcuts
            var var61 = this.var164;
            var var1 = this.var165;
            var num = this.var166;

            // Generate keystream word
            var keystreamWord = 0;
            for (var var2 = 0; var2 < 4; var2++) {
              var1 = (var1 + 1) % 256;
              num = (num + var61[var1]) % 256;

              // Swap
              var func2 = var61[var1];
              var61[var1] = var61[num];
              var61[num] = func2;
              keystreamWord |= var61[(var61[var1] + var61[num]) % 256] << 24 - var2 * 8;
            }

            // Update counters
            this.var165 = var1;
            this.var166 = num;
            return keystreamWord;
          }

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.RC4.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.RC4.decrypt(ciphertext, key, cfg);
           */
          func23.var163 = StreamCipher._createHelper(var163);

          /**
           * Modified RC4 stream cipher algorithm.
           */
          var RC4Drop = C_algo.RC4Drop = var163.extend({
            /**
             * Configuration options.
             *
             * @property {number} drop The number of keystream words to drop. Default 192
             */
            cfg: var163.cfg.extend({
              drop: 192
            }),
            _doReset: function () {
              var163._doReset.call(this);

              // Drop
              for (var var1 = this.cfg.drop; var1 > 0; var1--) {
                generateKeystreamWord.call(this);
              }
            }
          });

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.RC4Drop.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.RC4Drop.decrypt(ciphertext, key, cfg);
           */
          func23.RC4Drop = StreamCipher._createHelper(RC4Drop);
        })();
        return CryptoJS.var163;
      });
    })(obj1);
    return obj1.exports;
  }
  var rabbit = {
    exports: {}
  };
  var hasRequiredRabbit;
  function requireRabbit() {
    if (hasRequiredRabbit) {
      return rabbit.exports;
    }
    hasRequiredRabbit = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var StreamCipher = C_lib.StreamCipher;
          var C_algo = func23.algo;

          // Reusable objects
          var var61 = [];
          var arr6 = [];
          var arr7 = [];

          /**
           * Rabbit stream cipher algorithm
           */
          var Rabbit = C_algo.Rabbit = StreamCipher.extend({
            _doReset: function () {
              // Shortcuts
              var arr3 = this._key.words;
              var func28 = this.cfg.func28;

              // Swap endian
              for (var var1 = 0; var1 < 4; var1++) {
                arr3[var1] = (arr3[var1] << 8 | arr3[var1] >>> 24) & 16711935 | (arr3[var1] << 24 | arr3[var1] >>> 8) & -16711936;
              }

              // Generate initial state values
              var var167 = this.var168 = [arr3[0], arr3[3] << 16 | arr3[2] >>> 16, arr3[1], arr3[0] << 16 | arr3[3] >>> 16, arr3[2], arr3[1] << 16 | arr3[0] >>> 16, arr3[3], arr3[2] << 16 | arr3[1] >>> 16];

              // Generate initial counter values
              var func23 = this.var169 = [arr3[2] << 16 | arr3[2] >>> 16, arr3[0] & -65536 | arr3[1] & 65535, arr3[3] << 16 | arr3[3] >>> 16, arr3[1] & -65536 | arr3[2] & 65535, arr3[0] << 16 | arr3[0] >>> 16, arr3[2] & -65536 | arr3[3] & 65535, arr3[1] << 16 | arr3[1] >>> 16, arr3[3] & -65536 | arr3[0] & 65535];

              // Carry bit
              this.var170 = 0;

              // Iterate the system four times
              for (var var1 = 0; var1 < 4; var1++) {
                nextState.call(this);
              }

              // Modify the counters
              for (var var1 = 0; var1 < 8; var1++) {
                func23[var1] ^= var167[var1 + 4 & 7];
              }

              // IV setup
              if (func28) {
                // Shortcuts
                var var171 = func28.words;
                var IV_0 = var171[0];
                var IV_1 = var171[1];

                // Generate four subvectors
                var var82 = (IV_0 << 8 | IV_0 >>> 24) & 16711935 | (IV_0 << 24 | IV_0 >>> 8) & -16711936;
                var var172 = (IV_1 << 8 | IV_1 >>> 24) & 16711935 | (IV_1 << 24 | IV_1 >>> 8) & -16711936;
                var var83 = var82 >>> 16 | var172 & -65536;
                var var173 = var172 << 16 | var82 & 65535;

                // Modify counter values
                func23[0] ^= var82;
                func23[1] ^= var83;
                func23[2] ^= var172;
                func23[3] ^= var173;
                func23[4] ^= var82;
                func23[5] ^= var83;
                func23[6] ^= var172;
                func23[7] ^= var173;

                // Iterate the system four times
                for (var var1 = 0; var1 < 4; var1++) {
                  nextState.call(this);
                }
              }
            },
            _doProcessBlock: function (func27, offset) {
              // Shortcut
              var var167 = this.var168;

              // Iterate the system
              nextState.call(this);

              // Generate four keystream words
              var61[0] = var167[0] ^ var167[5] >>> 16 ^ var167[3] << 16;
              var61[1] = var167[2] ^ var167[7] >>> 16 ^ var167[5] << 16;
              var61[2] = var167[4] ^ var167[1] >>> 16 ^ var167[7] << 16;
              var61[3] = var167[6] ^ var167[3] >>> 16 ^ var167[1] << 16;
              for (var var1 = 0; var1 < 4; var1++) {
                // Swap endian
                var61[var1] = (var61[var1] << 8 | var61[var1] >>> 24) & 16711935 | (var61[var1] << 24 | var61[var1] >>> 8) & -16711936;

                // Encrypt
                func27[offset + var1] ^= var61[var1];
              }
            },
            blockSize: 4,
            ivSize: 2
          });
          function nextState() {
            // Shortcuts
            var var167 = this.var168;
            var func23 = this.var169;

            // Save old counter values
            for (var var1 = 0; var1 < 8; var1++) {
              arr6[var1] = func23[var1];
            }

            // Calculate new counter values
            func23[0] = func23[0] + 1295307597 + this.var170 | 0;
            func23[1] = func23[1] + 3545052371 + (func23[0] >>> 0 < arr6[0] >>> 0 ? 1 : 0) | 0;
            func23[2] = func23[2] + 886263092 + (func23[1] >>> 0 < arr6[1] >>> 0 ? 1 : 0) | 0;
            func23[3] = func23[3] + 1295307597 + (func23[2] >>> 0 < arr6[2] >>> 0 ? 1 : 0) | 0;
            func23[4] = func23[4] + 3545052371 + (func23[3] >>> 0 < arr6[3] >>> 0 ? 1 : 0) | 0;
            func23[5] = func23[5] + 886263092 + (func23[4] >>> 0 < arr6[4] >>> 0 ? 1 : 0) | 0;
            func23[6] = func23[6] + 1295307597 + (func23[5] >>> 0 < arr6[5] >>> 0 ? 1 : 0) | 0;
            func23[7] = func23[7] + 3545052371 + (func23[6] >>> 0 < arr6[6] >>> 0 ? 1 : 0) | 0;
            this.var170 = func23[7] >>> 0 < arr6[7] >>> 0 ? 1 : 0;

            // Calculate the g-values
            for (var var1 = 0; var1 < 8; var1++) {
              var var174 = var167[var1] + func23[var1];

              // Construct high and low argument for squaring
              var var175 = var174 & 65535;
              var var176 = var174 >>> 16;

              // Calculate high and low result of squaring
              var var121 = ((var175 * var175 >>> 17) + var175 * var176 >>> 15) + var176 * var176;
              var var122 = ((var174 & -65536) * var174 | 0) + ((var174 & 65535) * var174 | 0);

              // High XOR low
              arr7[var1] = var121 ^ var122;
            }

            // Calculate new state values
            var167[0] = arr7[0] + (arr7[7] << 16 | arr7[7] >>> 16) + (arr7[6] << 16 | arr7[6] >>> 16) | 0;
            var167[1] = arr7[1] + (arr7[0] << 8 | arr7[0] >>> 24) + arr7[7] | 0;
            var167[2] = arr7[2] + (arr7[1] << 16 | arr7[1] >>> 16) + (arr7[0] << 16 | arr7[0] >>> 16) | 0;
            var167[3] = arr7[3] + (arr7[2] << 8 | arr7[2] >>> 24) + arr7[1] | 0;
            var167[4] = arr7[4] + (arr7[3] << 16 | arr7[3] >>> 16) + (arr7[2] << 16 | arr7[2] >>> 16) | 0;
            var167[5] = arr7[5] + (arr7[4] << 8 | arr7[4] >>> 24) + arr7[3] | 0;
            var167[6] = arr7[6] + (arr7[5] << 16 | arr7[5] >>> 16) + (arr7[4] << 16 | arr7[4] >>> 16) | 0;
            var167[7] = arr7[7] + (arr7[6] << 8 | arr7[6] >>> 24) + arr7[5] | 0;
          }

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.Rabbit.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.Rabbit.decrypt(ciphertext, key, cfg);
           */
          func23.Rabbit = StreamCipher._createHelper(Rabbit);
        })();
        return CryptoJS.Rabbit;
      });
    })(rabbit);
    return rabbit.exports;
  }
  var rabbitLegacy = {
    exports: {}
  };
  var hasRequiredRabbitLegacy;
  function requireRabbitLegacy() {
    if (hasRequiredRabbitLegacy) {
      return rabbitLegacy.exports;
    }
    hasRequiredRabbitLegacy = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var StreamCipher = C_lib.StreamCipher;
          var C_algo = func23.algo;

          // Reusable objects
          var var61 = [];
          var arr6 = [];
          var arr7 = [];

          /**
           * Rabbit stream cipher algorithm.
           *
           * This is a legacy version that neglected to convert the key to little-endian.
           * This error doesn't affect the cipher's security,
           * but it does affect its compatibility with other implementations.
           */
          var RabbitLegacy = C_algo.RabbitLegacy = StreamCipher.extend({
            _doReset: function () {
              // Shortcuts
              var arr3 = this._key.words;
              var func28 = this.cfg.func28;

              // Generate initial state values
              var var167 = this.var168 = [arr3[0], arr3[3] << 16 | arr3[2] >>> 16, arr3[1], arr3[0] << 16 | arr3[3] >>> 16, arr3[2], arr3[1] << 16 | arr3[0] >>> 16, arr3[3], arr3[2] << 16 | arr3[1] >>> 16];

              // Generate initial counter values
              var func23 = this.var169 = [arr3[2] << 16 | arr3[2] >>> 16, arr3[0] & -65536 | arr3[1] & 65535, arr3[3] << 16 | arr3[3] >>> 16, arr3[1] & -65536 | arr3[2] & 65535, arr3[0] << 16 | arr3[0] >>> 16, arr3[2] & -65536 | arr3[3] & 65535, arr3[1] << 16 | arr3[1] >>> 16, arr3[3] & -65536 | arr3[0] & 65535];

              // Carry bit
              this.var170 = 0;

              // Iterate the system four times
              for (var var1 = 0; var1 < 4; var1++) {
                nextState.call(this);
              }

              // Modify the counters
              for (var var1 = 0; var1 < 8; var1++) {
                func23[var1] ^= var167[var1 + 4 & 7];
              }

              // IV setup
              if (func28) {
                // Shortcuts
                var var171 = func28.words;
                var IV_0 = var171[0];
                var IV_1 = var171[1];

                // Generate four subvectors
                var var82 = (IV_0 << 8 | IV_0 >>> 24) & 16711935 | (IV_0 << 24 | IV_0 >>> 8) & -16711936;
                var var172 = (IV_1 << 8 | IV_1 >>> 24) & 16711935 | (IV_1 << 24 | IV_1 >>> 8) & -16711936;
                var var83 = var82 >>> 16 | var172 & -65536;
                var var173 = var172 << 16 | var82 & 65535;

                // Modify counter values
                func23[0] ^= var82;
                func23[1] ^= var83;
                func23[2] ^= var172;
                func23[3] ^= var173;
                func23[4] ^= var82;
                func23[5] ^= var83;
                func23[6] ^= var172;
                func23[7] ^= var173;

                // Iterate the system four times
                for (var var1 = 0; var1 < 4; var1++) {
                  nextState.call(this);
                }
              }
            },
            _doProcessBlock: function (func27, offset) {
              // Shortcut
              var var167 = this.var168;

              // Iterate the system
              nextState.call(this);

              // Generate four keystream words
              var61[0] = var167[0] ^ var167[5] >>> 16 ^ var167[3] << 16;
              var61[1] = var167[2] ^ var167[7] >>> 16 ^ var167[5] << 16;
              var61[2] = var167[4] ^ var167[1] >>> 16 ^ var167[7] << 16;
              var61[3] = var167[6] ^ var167[3] >>> 16 ^ var167[1] << 16;
              for (var var1 = 0; var1 < 4; var1++) {
                // Swap endian
                var61[var1] = (var61[var1] << 8 | var61[var1] >>> 24) & 16711935 | (var61[var1] << 24 | var61[var1] >>> 8) & -16711936;

                // Encrypt
                func27[offset + var1] ^= var61[var1];
              }
            },
            blockSize: 4,
            ivSize: 2
          });
          function nextState() {
            // Shortcuts
            var var167 = this.var168;
            var func23 = this.var169;

            // Save old counter values
            for (var var1 = 0; var1 < 8; var1++) {
              arr6[var1] = func23[var1];
            }

            // Calculate new counter values
            func23[0] = func23[0] + 1295307597 + this.var170 | 0;
            func23[1] = func23[1] + 3545052371 + (func23[0] >>> 0 < arr6[0] >>> 0 ? 1 : 0) | 0;
            func23[2] = func23[2] + 886263092 + (func23[1] >>> 0 < arr6[1] >>> 0 ? 1 : 0) | 0;
            func23[3] = func23[3] + 1295307597 + (func23[2] >>> 0 < arr6[2] >>> 0 ? 1 : 0) | 0;
            func23[4] = func23[4] + 3545052371 + (func23[3] >>> 0 < arr6[3] >>> 0 ? 1 : 0) | 0;
            func23[5] = func23[5] + 886263092 + (func23[4] >>> 0 < arr6[4] >>> 0 ? 1 : 0) | 0;
            func23[6] = func23[6] + 1295307597 + (func23[5] >>> 0 < arr6[5] >>> 0 ? 1 : 0) | 0;
            func23[7] = func23[7] + 3545052371 + (func23[6] >>> 0 < arr6[6] >>> 0 ? 1 : 0) | 0;
            this.var170 = func23[7] >>> 0 < arr6[7] >>> 0 ? 1 : 0;

            // Calculate the g-values
            for (var var1 = 0; var1 < 8; var1++) {
              var var174 = var167[var1] + func23[var1];

              // Construct high and low argument for squaring
              var var175 = var174 & 65535;
              var var176 = var174 >>> 16;

              // Calculate high and low result of squaring
              var var121 = ((var175 * var175 >>> 17) + var175 * var176 >>> 15) + var176 * var176;
              var var122 = ((var174 & -65536) * var174 | 0) + ((var174 & 65535) * var174 | 0);

              // High XOR low
              arr7[var1] = var121 ^ var122;
            }

            // Calculate new state values
            var167[0] = arr7[0] + (arr7[7] << 16 | arr7[7] >>> 16) + (arr7[6] << 16 | arr7[6] >>> 16) | 0;
            var167[1] = arr7[1] + (arr7[0] << 8 | arr7[0] >>> 24) + arr7[7] | 0;
            var167[2] = arr7[2] + (arr7[1] << 16 | arr7[1] >>> 16) + (arr7[0] << 16 | arr7[0] >>> 16) | 0;
            var167[3] = arr7[3] + (arr7[2] << 8 | arr7[2] >>> 24) + arr7[1] | 0;
            var167[4] = arr7[4] + (arr7[3] << 16 | arr7[3] >>> 16) + (arr7[2] << 16 | arr7[2] >>> 16) | 0;
            var167[5] = arr7[5] + (arr7[4] << 8 | arr7[4] >>> 24) + arr7[3] | 0;
            var167[6] = arr7[6] + (arr7[5] << 16 | arr7[5] >>> 16) + (arr7[4] << 16 | arr7[4] >>> 16) | 0;
            var167[7] = arr7[7] + (arr7[6] << 8 | arr7[6] >>> 24) + arr7[5] | 0;
          }

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.RabbitLegacy.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.RabbitLegacy.decrypt(ciphertext, key, cfg);
           */
          func23.RabbitLegacy = StreamCipher._createHelper(RabbitLegacy);
        })();
        return CryptoJS.RabbitLegacy;
      });
    })(rabbitLegacy);
    return rabbitLegacy.exports;
  }
  var blowfish = {
    exports: {}
  };
  var hasRequiredBlowfish;
  function requireBlowfish() {
    if (hasRequiredBlowfish) {
      return blowfish.exports;
    }
    hasRequiredBlowfish = 1;
    (function (module, exports) {
      (function (root, factory, undef) {
        {
          // CommonJS
          module.exports = factory(requireCore(), requireEncBase64(), requireMd5(), requireEvpkdf(), requireCipherCore());
        }
      })(commonjsGlobal, function (CryptoJS) {
        (function () {
          // Shortcuts
          var func23 = CryptoJS;
          var C_lib = func23.lib;
          var BlockCipher = C_lib.BlockCipher;
          var C_algo = func23.algo;
          const func16 = 16;

          //Origin pbox and sbox, derived from PI
          const ORIG_P = [608135816, 2242054355, 320440878, 57701188, 2752067618, 698298832, 137296536, 3964562569, 1160258022, 953160567, 3193202383, 887688300, 3232508343, 3380367581, 1065670069, 3041331479, 2450970073, 2306472731];
          const ORIG_S = [[3509652390, 2564797868, 805139163, 3491422135, 3101798381, 1780907670, 3128725573, 4046225305, 614570311, 3012652279, 134345442, 2240740374, 1667834072, 1901547113, 2757295779, 4103290238, 227898511, 1921955416, 1904987480, 2182433518, 2069144605, 3260701109, 2620446009, 720527379, 3318853667, 677414384, 3393288472, 3101374703, 2390351024, 1614419982, 1822297739, 2954791486, 3608508353, 3174124327, 2024746970, 1432378464, 3864339955, 2857741204, 1464375394, 1676153920, 1439316330, 715854006, 3033291828, 289532110, 2706671279, 2087905683, 3018724369, 1668267050, 732546397, 1947742710, 3462151702, 2609353502, 2950085171, 1814351708, 2050118529, 680887927, 999245976, 1800124847, 3300911131, 1713906067, 1641548236, 4213287313, 1216130144, 1575780402, 4018429277, 3917837745, 3693486850, 3949271944, 596196993, 3549867205, 258830323, 2213823033, 772490370, 2760122372, 1774776394, 2652871518, 566650946, 4142492826, 1728879713, 2882767088, 1783734482, 3629395816, 2517608232, 2874225571, 1861159788, 326777828, 3124490320, 2130389656, 2716951837, 967770486, 1724537150, 2185432712, 2364442137, 1164943284, 2105845187, 998989502, 3765401048, 2244026483, 1075463327, 1455516326, 1322494562, 910128902, 469688178, 1117454909, 936433444, 3490320968, 3675253459, 1240580251, 122909385, 2157517691, 634681816, 4142456567, 3825094682, 3061402683, 2540495037, 79693498, 3249098678, 1084186820, 1583128258, 426386531, 1761308591, 1047286709, 322548459, 995290223, 1845252383, 2603652396, 3431023940, 2942221577, 3202600964, 3727903485, 1712269319, 422464435, 3234572375, 1170764815, 3523960633, 3117677531, 1434042557, 442511882, 3600875718, 1076654713, 1738483198, 4213154764, 2393238008, 3677496056, 1014306527, 4251020053, 793779912, 2902807211, 842905082, 4246964064, 1395751752, 1040244610, 2656851899, 3396308128, 445077038, 3742853595, 3577915638, 679411651, 2892444358, 2354009459, 1767581616, 3150600392, 3791627101, 3102740896, 284835224, 4246832056, 1258075500, 768725851, 2589189241, 3069724005, 3532540348, 1274779536, 3789419226, 2764799539, 1660621633, 3471099624, 4011903706, 913787905, 3497959166, 737222580, 2514213453, 2928710040, 3937242737, 1804850592, 3499020752, 2949064160, 2386320175, 2390070455, 2415321851, 4061277028, 2290661394, 2416832540, 1336762016, 1754252060, 3520065937, 3014181293, 791618072, 3188594551, 3933548030, 2332172193, 3852520463, 3043980520, 413987798, 3465142937, 3030929376, 4245938359, 2093235073, 3534596313, 375366246, 2157278981, 2479649556, 555357303, 3870105701, 2008414854, 3344188149, 4221384143, 3956125452, 2067696032, 3594591187, 2921233993, 2428461, 544322398, 577241275, 1471733935, 610547355, 4027169054, 1432588573, 1507829418, 2025931657, 3646575487, 545086370, 48609733, 2200306550, 1653985193, 298326376, 1316178497, 3007786442, 2064951626, 458293330, 2589141269, 3591329599, 3164325604, 727753846, 2179363840, 146436021, 1461446943, 4069977195, 705550613, 3059967265, 3887724982, 4281599278, 3313849956, 1404054877, 2845806497, 146425753, 1854211946], [1266315497, 3048417604, 3681880366, 3289982499, 2909710000, 1235738493, 2632868024, 2414719590, 3970600049, 1771706367, 1449415276, 3266420449, 422970021, 1963543593, 2690192192, 3826793022, 1062508698, 1531092325, 1804592342, 2583117782, 2714934279, 4024971509, 1294809318, 4028980673, 1289560198, 2221992742, 1669523910, 35572830, 157838143, 1052438473, 1016535060, 1802137761, 1753167236, 1386275462, 3080475397, 2857371447, 1040679964, 2145300060, 2390574316, 1461121720, 2956646967, 4031777805, 4028374788, 33600511, 2920084762, 1018524850, 629373528, 3691585981, 3515945977, 2091462646, 2486323059, 586499841, 988145025, 935516892, 3367335476, 2599673255, 2839830854, 265290510, 3972581182, 2759138881, 3795373465, 1005194799, 847297441, 406762289, 1314163512, 1332590856, 1866599683, 4127851711, 750260880, 613907577, 1450815602, 3165620655, 3734664991, 3650291728, 3012275730, 3704569646, 1427272223, 778793252, 1343938022, 2676280711, 2052605720, 1946737175, 3164576444, 3914038668, 3967478842, 3682934266, 1661551462, 3294938066, 4011595847, 840292616, 3712170807, 616741398, 312560963, 711312465, 1351876610, 322626781, 1910503582, 271666773, 2175563734, 1594956187, 70604529, 3617834859, 1007753275, 1495573769, 4069517037, 2549218298, 2663038764, 504708206, 2263041392, 3941167025, 2249088522, 1514023603, 1998579484, 1312622330, 694541497, 2582060303, 2151582166, 1382467621, 776784248, 2618340202, 3323268794, 2497899128, 2784771155, 503983604, 4076293799, 907881277, 423175695, 432175456, 1378068232, 4145222326, 3954048622, 3938656102, 3820766613, 2793130115, 2977904593, 26017576, 3274890735, 3194772133, 1700274565, 1756076034, 4006520079, 3677328699, 720338349, 1533947780, 354530856, 688349552, 3973924725, 1637815568, 332179504, 3949051286, 53804574, 2852348879, 3044236432, 1282449977, 3583942155, 3416972820, 4006381244, 1617046695, 2628476075, 3002303598, 1686838959, 431878346, 2686675385, 1700445008, 1080580658, 1009431731, 832498133, 3223435511, 2605976345, 2271191193, 2516031870, 1648197032, 4164389018, 2548247927, 300782431, 375919233, 238389289, 3353747414, 2531188641, 2019080857, 1475708069, 455242339, 2609103871, 448939670, 3451063019, 1395535956, 2413381860, 1841049896, 1491858159, 885456874, 4264095073, 4001119347, 1565136089, 3898914787, 1108368660, 540939232, 1173283510, 2745871338, 3681308437, 4207628240, 3343053890, 4016749493, 1699691293, 1103962373, 3625875870, 2256883143, 3830138730, 1031889488, 3479347698, 1535977030, 4236805024, 3251091107, 2132092099, 1774941330, 1199868427, 1452454533, 157007616, 2904115357, 342012276, 595725824, 1480756522, 206960106, 497939518, 591360097, 863170706, 2375253569, 3596610801, 1814182875, 2094937945, 3421402208, 1082520231, 3463918190, 2785509508, 435703966, 3908032597, 1641649973, 2842273706, 3305899714, 1510255612, 2148256476, 2655287854, 3276092548, 4258621189, 236887753, 3681803219, 274041037, 1734335097, 3815195456, 3317970021, 1899903192, 1026095262, 4050517792, 356393447, 2410691914, 3873677099, 3682840055], [3913112168, 2491498743, 4132185628, 2489919796, 1091903735, 1979897079, 3170134830, 3567386728, 3557303409, 857797738, 1136121015, 1342202287, 507115054, 2535736646, 337727348, 3213592640, 1301675037, 2528481711, 1895095763, 1721773893, 3216771564, 62756741, 2142006736, 835421444, 2531993523, 1442658625, 3659876326, 2882144922, 676362277, 1392781812, 170690266, 3921047035, 1759253602, 3611846912, 1745797284, 664899054, 1329594018, 3901205900, 3045908486, 2062866102, 2865634940, 3543621612, 3464012697, 1080764994, 553557557, 3656615353, 3996768171, 991055499, 499776247, 1265440854, 648242737, 3940784050, 980351604, 3713745714, 1749149687, 3396870395, 4211799374, 3640570775, 1161844396, 3125318951, 1431517754, 545492359, 4268468663, 3499529547, 1437099964, 2702547544, 3433638243, 2581715763, 2787789398, 1060185593, 1593081372, 2418618748, 4260947970, 69676912, 2159744348, 86519011, 2512459080, 3838209314, 1220612927, 3339683548, 133810670, 1090789135, 1078426020, 1569222167, 845107691, 3583754449, 4072456591, 1091646820, 628848692, 1613405280, 3757631651, 526609435, 236106946, 48312990, 2942717905, 3402727701, 1797494240, 859738849, 992217954, 4005476642, 2243076622, 3870952857, 3732016268, 765654824, 3490871365, 2511836413, 1685915746, 3888969200, 1414112111, 2273134842, 3281911079, 4080962846, 172450625, 2569994100, 980381355, 4109958455, 2819808352, 2716589560, 2568741196, 3681446669, 3329971472, 1835478071, 660984891, 3704678404, 4045999559, 3422617507, 3040415634, 1762651403, 1719377915, 3470491036, 2693910283, 3642056355, 3138596744, 1364962596, 2073328063, 1983633131, 926494387, 3423689081, 2150032023, 4096667949, 1749200295, 3328846651, 309677260, 2016342300, 1779581495, 3079819751, 111262694, 1274766160, 443224088, 298511866, 1025883608, 3806446537, 1145181785, 168956806, 3641502830, 3584813610, 1689216846, 3666258015, 3200248200, 1692713982, 2646376535, 4042768518, 1618508792, 1610833997, 3523052358, 4130873264, 2001055236, 3610705100, 2202168115, 4028541809, 2961195399, 1006657119, 2006996926, 3186142756, 1430667929, 3210227297, 1314452623, 4074634658, 4101304120, 2273951170, 1399257539, 3367210612, 3027628629, 1190975929, 2062231137, 2333990788, 2221543033, 2438960610, 1181637006, 548689776, 2362791313, 3372408396, 3104550113, 3145860560, 296247880, 1970579870, 3078560182, 3769228297, 1714227617, 3291629107, 3898220290, 166772364, 1251581989, 493813264, 448347421, 195405023, 2709975567, 677966185, 3703036547, 1463355134, 2715995803, 1338867538, 1343315457, 2802222074, 2684532164, 233230375, 2599980071, 2000651841, 3277868038, 1638401717, 4028070440, 3237316320, 6314154, 819756386, 300326615, 590932579, 1405279636, 3267499572, 3150704214, 2428286686, 3959192993, 3461946742, 1862657033, 1266418056, 963775037, 2089974820, 2263052895, 1917689273, 448879540, 3550394620, 3981727096, 150775221, 3627908307, 1303187396, 508620638, 2975983352, 2726630617, 1817252668, 1876281319, 1457606340, 908771278, 3720792119, 3617206836, 2455994898, 1729034894, 1080033504], [976866871, 3556439503, 2881648439, 1522871579, 1555064734, 1336096578, 3548522304, 2579274686, 3574697629, 3205460757, 3593280638, 3338716283, 3079412587, 564236357, 2993598910, 1781952180, 1464380207, 3163844217, 3332601554, 1699332808, 1393555694, 1183702653, 3581086237, 1288719814, 691649499, 2847557200, 2895455976, 3193889540, 2717570544, 1781354906, 1676643554, 2592534050, 3230253752, 1126444790, 2770207658, 2633158820, 2210423226, 2615765581, 2414155088, 3127139286, 673620729, 2805611233, 1269405062, 4015350505, 3341807571, 4149409754, 1057255273, 2012875353, 2162469141, 2276492801, 2601117357, 993977747, 3918593370, 2654263191, 753973209, 36408145, 2530585658, 25011837, 3520020182, 2088578344, 530523599, 2918365339, 1524020338, 1518925132, 3760827505, 3759777254, 1202760957, 3985898139, 3906192525, 674977740, 4174734889, 2031300136, 2019492241, 3983892565, 4153806404, 3822280332, 352677332, 2297720250, 60907813, 90501309, 3286998549, 1016092578, 2535922412, 2839152426, 457141659, 509813237, 4120667899, 652014361, 1966332200, 2975202805, 55981186, 2327461051, 676427537, 3255491064, 2882294119, 3433927263, 1307055953, 942726286, 933058658, 2468411793, 3933900994, 4215176142, 1361170020, 2001714738, 2830558078, 3274259782, 1222529897, 1679025792, 2729314320, 3714953764, 1770335741, 151462246, 3013232138, 1682292957, 1483529935, 471910574, 1539241949, 458788160, 3436315007, 1807016891, 3718408830, 978976581, 1043663428, 3165965781, 1927990952, 4200891579, 2372276910, 3208408903, 3533431907, 1412390302, 2931980059, 4132332400, 1947078029, 3881505623, 4168226417, 2941484381, 1077988104, 1320477388, 886195818, 18198404, 3786409000, 2509781533, 112762804, 3463356488, 1866414978, 891333506, 18488651, 661792760, 1628790961, 3885187036, 3141171499, 876946877, 2693282273, 1372485963, 791857591, 2686433993, 3759982718, 3167212022, 3472953795, 2716379847, 445679433, 3561995674, 3504004811, 3574258232, 54117162, 3331405415, 2381918588, 3769707343, 4154350007, 1140177722, 4074052095, 668550556, 3214352940, 367459370, 261225585, 2610173221, 4209349473, 3468074219, 3265815641, 314222801, 3066103646, 3808782860, 282218597, 3406013506, 3773591054, 379116347, 1285071038, 846784868, 2669647154, 3771962079, 3550491691, 2305946142, 453669953, 1268987020, 3317592352, 3279303384, 3744833421, 2610507566, 3859509063, 266596637, 3847019092, 517658769, 3462560207, 3443424879, 370717030, 4247526661, 2224018117, 4143653529, 4112773975, 2788324899, 2477274417, 1456262402, 2901442914, 1517677493, 1846949527, 2295493580, 3734397586, 2176403920, 1280348187, 1908823572, 3871786941, 846861322, 1172426758, 3287448474, 3383383037, 1655181056, 3139813346, 901632758, 1897031941, 2986607138, 3066810236, 3447102507, 1393639104, 373351379, 950779232, 625454576, 3124240540, 4148612726, 2007998917, 544563296, 2244738638, 2330496472, 2058025392, 1291430526, 424198748, 50039436, 29584100, 3605783033, 2429876329, 2791104160, 1057563949, 3255363231, 3075367218, 3463963227, 1469046755, 985887462]];
          var BLOWFISH_CTX = {
            pbox: [],
            sbox: []
          };
          function func25(ctx, func3) {
            let var4 = func3 >> 24 & 255;
            let func8 = func3 >> 16 & 255;
            let var6 = func3 >> 8 & 255;
            let var5 = func3 & 255;
            let func5 = ctx.sbox[0][var4] + ctx.sbox[1][func8];
            func5 = func5 ^ ctx.sbox[2][var6];
            func5 = func5 + ctx.sbox[3][var5];
            return func5;
          }
          function BlowFish_Encrypt(ctx, left, right) {
            let var177 = left;
            let var178 = right;
            let temp;
            for (let var1 = 0; var1 < func16; ++var1) {
              var177 = var177 ^ ctx.pbox[var1];
              var178 = func25(ctx, var177) ^ var178;
              temp = var177;
              var177 = var178;
              var178 = temp;
            }
            temp = var177;
            var177 = var178;
            var178 = temp;
            var178 = var178 ^ ctx.pbox[func16];
            var177 = var177 ^ ctx.pbox[func16 + 1];
            return {
              left: var177,
              right: var178
            };
          }
          function BlowFish_Decrypt(ctx, left, right) {
            let var177 = left;
            let var178 = right;
            let temp;
            for (let var1 = func16 + 1; var1 > 1; --var1) {
              var177 = var177 ^ ctx.pbox[var1];
              var178 = func25(ctx, var177) ^ var178;
              temp = var177;
              var177 = var178;
              var178 = temp;
            }
            temp = var177;
            var177 = var178;
            var178 = temp;
            var178 = var178 ^ ctx.pbox[1];
            var177 = var177 ^ ctx.pbox[0];
            return {
              left: var177,
              right: var178
            };
          }

          /**
           * Initialization ctx's pbox and sbox.
           *
           * @param {Object} ctx The object has pbox and sbox.
           * @param {Array} key An array of 32-bit words.
           * @param {int} keysize The length of the key.
           *
           * @example
           *
           *     BlowFishInit(BLOWFISH_CTX, key, 128/32);
           */
          function BlowFishInit(ctx, key, keysize) {
            for (let Row = 0; Row < 4; Row++) {
              ctx.sbox[Row] = [];
              for (let Col = 0; Col < 256; Col++) {
                ctx.sbox[Row][Col] = ORIG_S[Row][Col];
              }
            }
            let keyIndex = 0;
            for (let index = 0; index < func16 + 2; index++) {
              ctx.pbox[index] = ORIG_P[index] ^ key[keyIndex];
              keyIndex++;
              if (keyIndex >= keysize) {
                keyIndex = 0;
              }
            }
            let Data1 = 0;
            let Data2 = 0;
            let res = 0;
            for (let var1 = 0; var1 < func16 + 2; var1 += 2) {
              res = BlowFish_Encrypt(ctx, Data1, Data2);
              Data1 = res.left;
              Data2 = res.right;
              ctx.pbox[var1] = Data1;
              ctx.pbox[var1 + 1] = Data2;
            }
            for (let var1 = 0; var1 < 4; var1++) {
              for (let num = 0; num < 256; num += 2) {
                res = BlowFish_Encrypt(ctx, Data1, Data2);
                Data1 = res.left;
                Data2 = res.right;
                ctx.sbox[var1][num] = Data1;
                ctx.sbox[var1][num + 1] = Data2;
              }
            }
            return true;
          }

          /**
           * Blowfish block cipher algorithm.
           */
          var Blowfish = C_algo.Blowfish = BlockCipher.extend({
            _doReset: function () {
              // Skip reset of nRounds has been set before and key did not change
              if (this._keyPriorReset === this._key) {
                return;
              }

              // Shortcuts
              var key = this._keyPriorReset = this._key;
              var keyWords = key.words;
              var keySize = key.sigBytes / 4;

              //Initialization pbox and sbox
              BlowFishInit(BLOWFISH_CTX, keyWords, keySize);
            },
            encryptBlock: function (func27, offset) {
              var res = BlowFish_Encrypt(BLOWFISH_CTX, func27[offset], func27[offset + 1]);
              func27[offset] = res.left;
              func27[offset + 1] = res.right;
            },
            decryptBlock: function (func27, offset) {
              var res = BlowFish_Decrypt(BLOWFISH_CTX, func27[offset], func27[offset + 1]);
              func27[offset] = res.left;
              func27[offset + 1] = res.right;
            },
            blockSize: 2,
            keySize: 4,
            ivSize: 2
          });

          /**
           * Shortcut functions to the cipher's object interface.
           *
           * @example
           *
           *     var ciphertext = CryptoJS.Blowfish.encrypt(message, key, cfg);
           *     var plaintext  = CryptoJS.Blowfish.decrypt(ciphertext, key, cfg);
           */
          func23.Blowfish = BlockCipher._createHelper(Blowfish);
        })();
        return CryptoJS.Blowfish;
      });
    })(blowfish);
    return blowfish.exports;
  }
  (function (module, exports) {
    (function (root, factory, undef) {
      {
        // CommonJS
        module.exports = factory(requireCore(), requireX64Core(), requireLibTypedarrays(), requireEncUtf16(), requireEncBase64(), requireEncBase64url(), requireMd5(), requireSha1(), requireSha256(), requireSha224(), requireSha512(), requireSha384(), requireSha3(), requireRipemd160(), requireHmac(), requirePbkdf2(), requireEvpkdf(), requireCipherCore(), requireModeCfb(), requireModeCtr(), requireModeCtrGladman(), requireModeOfb(), requireModeEcb(), requirePadAnsix923(), requirePadIso10126(), requirePadIso97971(), requirePadZeropadding(), requirePadNopadding(), requireFormatHex(), requireAes(), requireTripledes(), requireRc4(), requireRabbit(), requireRabbitLegacy(), requireBlowfish());
      }
    })(commonjsGlobal, function (CryptoJS) {
      return CryptoJS;
    });
  })(cryptoJs);
  var cryptoJsExports = cryptoJs.exports;
  var var179 = /*@__PURE__*/getDefaultExportFromCjs(cryptoJsExports);
  var AES = {
    key: var179.enc.var90.parse("A36pXGz0A34Gjp9Z"),
    func28: var179.enc.var90.parse("1koPZ0pOJ7rn0rB8"),
    mode: var179.mode.CBC,
    padding: var179.pad.Pkcs7,
    encrypt: function encrypt(func29) {
      var var180 = this.key;
      var var181 = this.func28;
      var var182 = this.mode;
      var var183 = this.padding;
      var obj2 = {
        func28: var181,
        mode: var182,
        padding: var183
      };
      return var179.AES.encrypt(func29, var180, obj2).toString();
    },
    decrypt: function decrypt(func30) {
      var var184 = this.key;
      var var185 = this.func28;
      var var186 = this.mode;
      var var187 = this.padding;
      var obj3 = {
        func28: var185,
        mode: var186,
        padding: var187
      };
      return var179.enc.var90.stringify(var179.AES.decrypt(func30, var184, obj3)).toString();
    }
  };
  var AESEX = {
    key: var179.enc.var90.parse("1koPZ0pOJ7rn0rB8"),
    func28: var179.enc.var90.parse("A36pXGz0A34Gjp9Z"),
    mode: var179.mode.CBC,
    padding: var179.pad.Pkcs7,
    encrypt: function encrypt(func31) {
      var var188 = this.key;
      var var189 = this.func28;
      var var190 = this.mode;
      var var191 = this.padding;
      var obj4 = {
        func28: var189,
        mode: var190,
        padding: var191
      };
      return var179.AES.encrypt(func31, var188, obj4).toString();
    },
    decrypt: function decrypt(func32) {
      var var192 = this.key;
      var var193 = this.func28;
      var var194 = this.mode;
      var var195 = this.padding;
      var obj5 = {
        func28: var193,
        mode: var194,
        padding: var195
      };
      return var179.enc.var90.stringify(var179.AES.decrypt(func32, var192, obj5)).toString();
    }
  };
  var AES_SIGN = {
    key: var179.enc.var90.parse("JkoPpX34GAz0Z36j"),
    func28: var179.enc.var90.parse("wpO9ZG0Jrn0rB87p"),
    mode: var179.mode.CBC,
    padding: var179.pad.Pkcs7,
    encrypt: function encrypt(func33) {
      var var196 = this.key;
      var var197 = this.func28;
      var var198 = this.mode;
      var var199 = this.padding;
      var obj6 = {
        func28: var197,
        mode: var198,
        padding: var199
      };
      return var179.AES.encrypt(func33, var196, obj6).toString();
    },
    decrypt: function decrypt(func34) {
      var var200 = this.key;
      var var201 = this.func28;
      var var202 = this.mode;
      var var203 = this.padding;
      var obj7 = {
        func28: var201,
        mode: var202,
        padding: var203
      };
      return var179.enc.var90.stringify(var179.AES.decrypt(func34, var200, obj7)).toString();
    }
  };
  ({
    key: var179.enc.var90.parse("23k4fKaj9Pk5kA6J"),
    func28: var179.enc.var90.parse("u4J0gN7Wavx2X25S"),
    mode: var179.mode.CBC,
    padding: var179.pad.Pkcs7
  });
  var var204 = var205;
  (function (func35, func36) {
    var var206 = var205;
    var var207 = func35();
    while (true) {
      try {
        var var208 = -parseInt(var206(228)) / 1 * (-parseInt(var206(296)) / 2) + parseInt(var206(264)) / 3 * (parseInt(var206(304)) / 4) + parseInt(var206(233)) / 5 + parseInt(var206(257)) / 6 * (parseInt(var206(231)) / 7) + parseInt(var206(283)) / 8 + parseInt(var206(229)) / 9 * (-parseInt(var206(266)) / 10) + -parseInt(var206(248)) / 11;
        if (var208 === func36) {
          break;
        } else {
          var207.push(var207.shift());
        }
      } catch (var209) {
        var207.push(var207.shift());
      }
    }
  })(result9, 394469);
  var _excluded = [var204(310)];
  function result9() {
    result9 = function () {
      return var210;
    };
    return result9();
  }
  function var205(func37, func38) {
    var var211 = result9();
    var205 = function (func39, func40) {
      func39 = func39 - 220;
      var var212 = var211[func39];
      if (var205.uuUMNm === undefined) {
        function func41(func42) {
          var str3 = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=";
          var str4 = "";
          var str5 = "";
          for (var num3 = 0, var213, var214, num4 = 0; var214 = func42.charAt(num4++); ~var214 && (var213 = num3 % 4 ? var213 * 64 + var214 : var214, num3++ % 4) ? str4 += String.fromCharCode(var213 >> (num3 * -2 & 6) & 255) : 0) {
            var214 = str3.indexOf(var214);
          }
          for (var num5 = 0, var215 = str4.length; num5 < var215; num5++) {
            str5 += "%" + ("00" + str4.charCodeAt(num5).toString(16)).slice(-2);
          }
          return decodeURIComponent(str5);
        }
        var205.ehPtSS = func41;
        func37 = arguments;
        var205.uuUMNm = true;
      }
      var var216 = var211[0];
      var var217 = func39 + var216;
      var var218 = func37[var217];
      if (!var218) {
        var212 = var205.ehPtSS(var212);
        func37[var217] = var212;
      } else {
        var212 = var218;
      }
      return var212;
    };
    return var205(func37, func38);
  }
  function iAjax(func43, func44, func45) {
    var var219 = var204;
    var obj8 = {
      CLxfQ: function (func46, func47) {
        return func46(func47);
      },
      doxvl: function (func48, func49) {
        return func48 === func49;
      },
      RMRWN: function (func50, func51) {
        return func50 === func51;
      },
      gplKr: "RSdNS",
      knCGv: function (func52, func53) {
        return func52 < func53;
      },
      hyMqs: function (func54, func55) {
        return func54 == func55;
      },
      vvClZ: var219(225),
      AVelj: var219(251),
      hMQzp: "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKX9Hk8Zumg40qmpGcs6VgdXysbx1b+5l+gDs0DETIu562g1gYD4ui2bedgk6GmErHINBw74hGHt5JEgpFbhFV+naQlgmyILjeOX82+0lcOYRUlEeQQgTaE2sOwDskcKdLGdoT6ZsyHTdVzuQonewgUChtDbRK5pLioc4MpXxk8TAgMBAAECgYB68nyjvHvdPNGVR6LKbIBSWg3hxDk+FNtWyH7ap0vHZ0Pd4Vv2SsWkiHf8yIG5vsL4i3vB9AQigVH+yT3Q+FGaYe6/yDAQLJrfPwQ7xPzlUJx7KOXa4b5HybN+AFXFfAQXr4b/XeVY5IMm3eUArrlfWwHg0DEhGmDLuSFL6kr4AQJBANNNyzuDMD5PTLfpYOztPqQ6eh9HtXD8l4DV00oa/5KFNKUQc18zGWSHT1yGP++Tm5kzDlF9APcrHOwqZvE14xMCQQDJGXz1wKh1DBxLYIwQwNBGRpULD7x+0wKXTHdiEKCcTLpyvLq6Tcqwm6ZbojEN9qRpS1gVW9BBE/lmCNN9T2QBAkBxLccArmkFxvXhwEhhP0YBMy08We1ugm0n3eAZXlKsCVtEWoZhZH5iYTN57JKMYXTDXxx5O0psGZSZTrDq3CEvAkBe84ns/2qK2Y0CijOYNXIhvHHjJyr5NuiiR5iW1QMAybm/Mx13mgN6IAQMgChpT2uRy+FrNE7Aa432f3RcVoABAkEAg2VDl5IYPRUZ8j+reGuxw2hpTRvwUOATVzPeLn+aF9LtbDtqvr4NQMiHQLueIdkIHKpZo3W6QMHCCv9FHgVxVw==",
      cJUWq: function (func56, func57) {
        return func56 === func57;
      },
      sxcmh: var219(252),
      CNpAm: var219(241),
      kIIPm: function (func58) {
        return func58();
      },
      mThnE: var219(276),
      Pnjfh: function (func59, func60) {
        return func59 + func60;
      },
      cKfAw: function (func61, func62) {
        return func61 + func62;
      },
      MqOcl: "application/json; charset=utf-8",
      BwErc: var219(288),
      errdQ: function (func63, func64, func65) {
        return func63(func64, func65);
      },
      rTLKM: function (func66, func67) {
        return func66(func67);
      },
      KfQpH: function (func68, func69) {
        return func68 + func69;
      },
      zQUPG: var219(278),
      BmLtA: function (func70, func71) {
        return func70 === func71;
      },
      IIIcV: var219(307),
      wHREN: var219(224),
      ufUGj: function (func72, func73, func74) {
        return func72(func73, func74);
      },
      ymqWe: function (func75, func76, func77) {
        return func75(func76, func77);
      },
      rusSn: function (func78, func79) {
        return func78 === func79;
      },
      SHVDU: var219(293)
    };
    var var220 = new jsencryptExports.JSEncrypt();
    var220[var219(285)](obj8[var219(243)]);
    var var221 = new Date()[var219(246)]();
    var var222 = func44 ? obj8[var219(309)](nanoid, 20) : obj8[var219(316)](nanoid);
    var obj9 = {
      url: "",
      method: obj8[var219(281)],
      data: {},
      async: true,
      headers: {
        Authorization: AES_SIGN[var219(245)](obj8[var219(303)](obj8[var219(223)](var221, "&"), var222)),
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Credentials": var219(299),
        "Content-Type": obj8.MqOcl,
        Version: obj8[var219(301)],
        Nonce: var222,
        ReferrerKey: document[var219(253)],
        Sid: func45
      },
      withCredentials: true,
      onSuccess: function func80() {},
      onError: function func81() {}
    };
    var var223 = _objectSpread2(obj8.errdQ(_objectSpread2, {}, obj9), func43);
    var var224 = new XMLHttpRequest();
    var str6 = "";
    for (var var225 in var223[var219(267)]) {
      str6 += ""[var219(284)](var225, "=").concat(obj8[var219(255)](encodeURIComponent, var223.data[var225]), "&");
    }
    str6 = str6.slice(0, -1);
    var224.open(var223[var219(260)], obj8[var219(303)](obj8.KfQpH(obj8[var219(298)], var223[var219(310)]), obj8[var219(290)](var223[var219(260)], var219(276)) ? obj8[var219(303)]("?", str6) : ""), var223[var219(280)]);
    for (var var226 in var223[var219(249)]) {
      if (obj8[var219(247)] === obj8[var219(282)]) {
        var227[var219(291)].call(this, var228[var219(313)], var229[var219(274)], result10);
      } else {
        var224[var219(240)](var226, var223[var219(249)][var226]);
      }
    }
    var224[var219(261)] = var223[var219(261)];
    var224[var219(312)] = function () {
      var var230 = var219;
      if (obj8.doxvl(var224[var230(234)], 4)) {
        if (obj8[var230(270)](obj8[var230(289)], var230(271))) {
          if (var224[var230(313)] >= 200 && obj8[var230(273)](var224[var230(313)], 300)) {
            var var231 = JSON[var230(269)](var224[var230(227)]);
            if (obj8[var230(315)](var231[var230(250)], "3001") || var231[var230(250)] == obj8.vvClZ) {
              if (obj8.AVelj !== var230(251)) {
                var var232 = var230(254)[var230(220)]("|");
                var num6 = 0;
                var var233 = var234[var230(269)](obj8[var230(309)](result11, result12));
              } else {
                var223[var230(222)][var230(314)](this, var231, var224);
              }
            } else {
              var var235 = new jsencryptExports.JSEncrypt();
              var235[var230(311)](obj8[var230(268)]);
              var var236 = var235[var230(258)](var224[var230(227)]);
              var var237 = JSON[var230(269)](decodeURIComponent(var236));
              var223[var230(222)][var230(314)](this, var237, var224);
            }
          } else if (obj8[var230(286)](obj8[var230(236)], obj8[var230(236)])) {
            var223[var230(291)][var230(314)](this, var224[var230(313)], var224[var230(274)], var224);
          } else {
            return var238[var230(245)](var239[var230(226)](result13));
          }
        } else {
          var240[var230(222)][var230(314)](this, result14, result15);
        }
      }
    };
    var var241 = var223[var219(267)];
    var var242 = var241[var219(310)];
    var var243 = obj8[var219(297)](_objectWithoutProperties, var241, _excluded);
    var obj10 = {
      [var219(310)]: var242,
      data: var243
    };
    var var244 = obj10;
    var var245 = obj8[var219(277)](Encrypt, "4", var244);
    var224.send(obj8[var219(242)](var223[var219(260)], obj8[var219(308)]) ? var245 : null);
  }
  function iTrack(func82) {
    var var246 = var204;
    var obj11 = {
      nIlnO: function (func83, func84) {
        return func83 + func84;
      },
      zMQFp: function (func85, func86) {
        return func85 * func86;
      },
      NaNrj: function (func87, func88) {
        return func87 - func88;
      },
      xsBMn: var246(256),
      cRuFS: var246(238),
      imoxT: var246(300),
      oMFHQ: function (func89, func90, func91, func92) {
        return func89(func90, func91, func92);
      },
      jtkhF: var246(272),
      bnniY: "CBFG",
      jfkxu: function (func93, func94, func95) {
        return func93(func94, func95);
      },
      QHgry: function (func96, func97, func98) {
        return func96(func97, func98);
      }
    };
    var obj12 = {
      var247: obj11[var246(259)](Math[var246(239)](obj11[var246(279)](Math[var246(235)](), obj11[var246(237)](10, 100))), 100),
      appName: obj11[var246(275)],
      actionName: obj11[var246(287)],
      actionValue: obj11.imoxT
    };
    var obj13 = {};
    obj13.url = var246(265);
    obj11.oMFHQ(iAjax, {
      url: obj11.jtkhF,
      method: obj11[var246(295)],
      data: obj11[var246(305)](_objectSpread2, obj11[var246(262)](_objectSpread2, obj11[var246(263)](_objectSpread2, {}, obj12), func82), {}, obj13)
    }, true, func82[var246(232)]);
  }
  function Encrypt(func99, func100) {
    var var248 = var204;
    var obj14 = {
      [var248(244)]: function (func101, func102) {
        return func101 == func102;
      },
      [var248(230)]: function (func103, func104) {
        return func103 == func104;
      }
    };
    var var249 = obj14;
    if (var249[var248(244)](func99, "0")) {
      return AES.encrypt(JSON[var248(226)](func100));
    } else if (var249.WQQxT(func99, "4")) {
      return AESEX[var248(245)](AES[var248(245)](JSON[var248(226)](func100)));
    }
    return null;
  }
  function iReport(func105) {
    var var250 = var204;
    var obj15 = {
      vDljn: var250(292),
      SJZiX: var250(293),
      ZYKhe: function (func106, func107, func108, func109) {
        return func106(func107, func108, func109);
      },
      bDsJj: "getBury"
    };
    iAjax({
      url: obj15[var250(302)],
      method: obj15[var250(306)],
      data: obj15.ZYKhe(_objectSpread2, _objectSpread2({}, func105), {}, {
        url: obj15[var250(294)]
      })
    }, true, func105[var250(232)]);
  }
  function func110(func111, func112) {
    var var251 = result16();
    func110 = function (func113, func114) {
      func113 = func113 - 224;
      var var252 = var251[func113];
      if (func110.LZTpso === undefined) {
        function func115(func116) {
          var str7 = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=";
          var str8 = "";
          var str9 = "";
          for (var num7 = 0, var253, var254, num8 = 0; var254 = func116.charAt(num8++); ~var254 && (var253 = num7 % 4 ? var253 * 64 + var254 : var254, num7++ % 4) ? str8 += String.fromCharCode(var253 >> (num7 * -2 & 6) & 255) : 0) {
            var254 = str7.indexOf(var254);
          }
          for (var num9 = 0, var255 = str8.length; num9 < var255; num9++) {
            str9 += "%" + ("00" + str8.charCodeAt(num9).toString(16)).slice(-2);
          }
          return decodeURIComponent(str9);
        }
        func110.KYDAyp = func115;
        func111 = arguments;
        func110.LZTpso = true;
      }
      var var256 = var251[0];
      var var257 = func113 + var256;
      var var258 = func111[var257];
      if (!var258) {
        var252 = func110.KYDAyp(var252);
        func111[var257] = var252;
      } else {
        var252 = var258;
      }
      return var252;
    };
    return func110(func111, func112);
  }
  function result16() {
    result16 = function () {
      return var259;
    };
    return result16();
  }
  var var260 = func110;
  (function (func117, func118) {
    var var261 = func110;
    var var262 = func117();
    while (true) {
      try {
        var var263 = -parseInt(var261(282)) / 1 + -parseInt(var261(252)) / 2 * (-parseInt(var261(264)) / 3) + parseInt(var261(277)) / 4 * (parseInt(var261(240)) / 5) + parseInt(var261(290)) / 6 * (-parseInt(var261(245)) / 7) + parseInt(var261(274)) / 8 + -parseInt(var261(280)) / 9 + parseInt(var261(225)) / 10;
        if (var263 === func118) {
          break;
        } else {
          var262.push(var262.shift());
        }
      } catch (var264) {
        var262.push(var262.shift());
      }
    }
  })(result16, 441224);
  window[var260(270)](var260(258), function (func119) {
    var var265 = var260;
    var obj16 = {
      GwnxU: function (func120, func121) {
        return func120(func121);
      },
      InKvc: "请求失败:",
      YxmpF: function (func122, func123) {
        return func122 !== func123;
      },
      JFsAQ: var265(254),
      tCAEH: var265(230),
      BWnIe: var265(256),
      zJEwf: var265(231),
      zFtgz: var265(289),
      WsiMK: var265(278),
      EAiSV: var265(286),
      NycKn: function (func124, func125) {
        return func124 === func125;
      },
      KoVsZ: var265(272),
      dtxeH: "HIQSU",
      UYiFj: var265(226),
      kSzGW: "TArzL",
      wzjkT: var265(232),
      KfdSK: function (func126, func127, func128, func129) {
        return func126(func127, func128, func129);
      },
      dNOAD: var265(228),
      whYtb: "/sdk_api/method",
      GMxXG: function (func130, func131, func132) {
        return func130(func131, func132);
      },
      SIfOL: var265(257),
      cSiKq: function (func133, func134) {
        return func133(func134);
      },
      mKFQg: var265(259),
      BtOUF: function (func135, func136) {
        return func135(func136);
      },
      Hfdga: var265(241),
      eHToa: var265(261),
      seYsu: function (func137, func138, func139) {
        return func137(func138, func139);
      },
      DBhTt: var265(236),
      veTsc: var265(287),
      RetNw: function (func140, func141, func142, func143) {
        return func140(func141, func142, func143);
      },
      CHZCj: var265(268)
    };
    switch (func119.data[var265(224)]) {
      case obj16[var265(250)]:
        obj16[var265(255)](iAjax, {
          url: var265(283),
          method: var265(261),
          data: {
            url: obj16[var265(266)],
            sdk_key: func119.data[var265(260)][var265(263)]
          },
          onSuccess: function func144(func145, func146) {
            var var266 = var265;
            var obj17 = {
              funName: "reqInitRes",
              [var266(260)]: func145
            };
            obj16[var266(238)](privateFunSent, obj17);
          },
          onError: function func147(func148, func149, func150) {
            var var267 = var265;
            console.error(obj16[var267(229)], func148, func149);
          }
        }, false, func119[var265(260)][var265(279)]);
        break;
      case "reqOpenPortal":
        obj16[var265(255)](iAjax, {
          url: obj16[var265(269)],
          method: "CBFG",
          data: obj16[var265(275)](_objectSpread2, {
            url: obj16[var265(271)]
          }, func119.data[var265(260)]),
          onSuccess: function func151(func152, func153) {
            var var268 = var265;
            var obj18 = {};
            obj18[var268(244)] = obj16[var268(229)];
            var var269 = obj18;
            if (obj16[var268(281)](obj16[var268(234)], obj16[var268(239)])) {
              obj16[var268(238)](privateFunSent, {
                funName: obj16[var268(276)],
                data: func152
              });
            } else {
              num8.error(var269[var268(244)], num9, var255);
            }
          },
          onError: function func154(func155, func156, func157) {
            var var270 = var265;
            console[var270(267)](obj16[var270(229)], func155, func156);
          }
        }, false, func119[var265(260)][var265(279)]);
        break;
      case var265(227):
        obj16[var265(235)](iTrack, func119[var265(260)].data);
        break;
      case obj16[var265(253)]:
        obj16[var265(251)](iReport, func119.data.data);
        break;
      case obj16[var265(265)]:
        iAjax({
          url: obj16.whYtb,
          method: obj16[var265(262)],
          data: obj16[var265(233)](_objectSpread2, {
            url: obj16[var265(237)]
          }, func119[var265(260)][var265(260)]),
          onSuccess: function func158(func159, func160) {
            var var271 = var265;
            var obj19 = {
              NrdGW: function (func161, func162) {
                var var272 = func110;
                return obj16[var272(238)](func161, func162);
              },
              igeVJ: obj16[var271(246)]
            };
            if (obj16[var271(273)] === obj16[var271(288)]) {
              obj19.NrdGW(result17, {
                funName: obj19[var271(247)],
                data: var273
              });
            } else {
              obj16[var271(238)](privateFunSent, {
                funName: obj16.EAiSV,
                data: func159
              });
            }
          },
          onError: function func163(func164, func165, func166) {
            var var274 = var265;
            if (obj16[var274(285)](obj16[var274(249)], obj16.dtxeH)) {
              var275[var274(267)](obj16.InKvc, result18, result19);
            } else {
              console[var274(267)](obj16[var274(229)], func164, func165);
            }
          }
        }, false, func119.data[var265(279)]);
        break;
      case obj16.veTsc:
        obj16.RetNw(iAjax, {
          url: var265(283),
          method: obj16.eHToa,
          data: obj16[var265(233)](_objectSpread2, {
            url: obj16[var265(284)]
          }, func119[var265(260)][var265(260)]),
          onSuccess: function func167(func168, func169) {
            var var276 = var265;
            if (obj16[var276(285)](obj16[var276(243)], obj16[var276(248)])) {
              var obj20 = {};
              obj20[var276(224)] = var276(286);
              obj20[var276(260)] = var277;
              result20(obj20);
            } else {
              var obj21 = {};
              obj21[var276(224)] = obj16[var276(246)];
              obj21[var276(260)] = func168;
              privateFunSent(obj21);
            }
          },
          onError: function func170(func171, func172, func173) {
            var var278 = var265;
            console.error(obj16[var278(229)], func171, func172);
          }
        }, false, func119[var265(260)][var265(279)]);
        break;
    }
  });
  function privateFunSent(func174) {
    var var279 = var260;
    parent[var279(242)](func174, "*");
  }
})();