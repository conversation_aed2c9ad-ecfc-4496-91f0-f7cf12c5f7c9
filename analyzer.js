#!/usr/bin/env node

const fs = require('fs');
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');

class CodeAnalyzer {
    constructor(sourceCode) {
        this.sourceCode = sourceCode;
        this.ast = null;
        this.analysis = {
            overview: {},
            functions: [],
            variables: [],
            strings: [],
            patterns: {},
            complexity: {},
            security: {}
        };
    }

    // 解析代码
    parseCode() {
        try {
            this.ast = parse(this.sourceCode, {
                sourceType: 'script',
                allowImportExportEverywhere: true,
                allowReturnOutsideFunction: true
            });
            return true;
        } catch (error) {
            console.error('解析失败:', error.message);
            return false;
        }
    }

    // 执行完整分析
    analyze() {
        console.log('🔍 开始代码分析...\n');
        
        if (!this.parseCode()) {
            return null;
        }

        this.analyzeOverview();
        this.analyzeFunctions();
        this.analyzeVariables();
        this.analyzeStrings();
        this.analyzePatterns();
        this.analyzeComplexity();
        this.analyzeSecurity();

        return this.analysis;
    }

    // 概览分析
    analyzeOverview() {
        console.log('📊 分析代码概览...');
        
        let totalNodes = 0;
        let totalLines = this.sourceCode.split('\n').length;
        
        traverse(this.ast, {
            enter: () => totalNodes++
        });

        this.analysis.overview = {
            totalLines,
            totalNodes,
            fileSize: this.sourceCode.length,
            estimatedComplexity: Math.floor(totalNodes / 100)
        };

        console.log(`  - 总行数: ${totalLines}`);
        console.log(`  - AST节点数: ${totalNodes}`);
        console.log(`  - 文件大小: ${this.sourceCode.length} 字符`);
    }

    // 函数分析
    analyzeFunctions() {
        console.log('\n🔧 分析函数结构...');
        
        traverse(this.ast, {
            FunctionDeclaration: (path) => {
                this.analyzeSingleFunction(path, 'declaration');
            },
            FunctionExpression: (path) => {
                this.analyzeSingleFunction(path, 'expression');
            },
            ArrowFunctionExpression: (path) => {
                this.analyzeSingleFunction(path, 'arrow');
            }
        });

        console.log(`  - 发现 ${this.analysis.functions.length} 个函数`);
    }

    // 分析单个函数
    analyzeSingleFunction(path, type) {
        const func = path.node;
        const name = func.id?.name || 'anonymous';
        
        let complexity = 0;
        let statements = 0;
        let loops = 0;
        let conditions = 0;
        let returns = 0;

        traverse(func, {
            Statement: () => statements++,
            IfStatement: () => { conditions++; complexity += 2; },
            WhileStatement: () => { loops++; complexity += 3; },
            ForStatement: () => { loops++; complexity += 3; },
            SwitchStatement: () => { conditions++; complexity += 2; },
            ReturnStatement: () => returns++,
            CallExpression: () => complexity += 1
        });

        const functionInfo = {
            name,
            type,
            parameters: func.params.length,
            statements,
            loops,
            conditions,
            returns,
            complexity,
            isObfuscated: this.isFunctionObfuscated(func),
            suspiciousPatterns: this.findSuspiciousPatterns(func)
        };

        this.analysis.functions.push(functionInfo);
        
        if (functionInfo.isObfuscated) {
            console.log(`    🚨 可疑函数: ${name} (复杂度: ${complexity})`);
        }
    }

    // 检测函数是否被混淆
    isFunctionObfuscated(func) {
        const name = func.id?.name || '';
        
        // 检查函数名
        if (name.length <= 2 || /^[a-zA-Z]$/.test(name)) {
            return true;
        }

        // 检查参数名
        const hasObfuscatedParams = func.params.some(param => {
            if (t.isIdentifier(param)) {
                return param.name.length <= 2;
            }
            return false;
        });

        return hasObfuscatedParams;
    }

    // 查找可疑模式
    findSuspiciousPatterns(func) {
        const patterns = [];
        
        traverse(func, {
            StringLiteral: (path) => {
                const value = path.node.value;
                if (value.length > 50 && /^[A-Za-z0-9+/]+=*$/.test(value)) {
                    patterns.push('base64_string');
                }
                if (/eval|Function|setTimeout|setInterval/.test(value)) {
                    patterns.push('dynamic_execution');
                }
            },
            CallExpression: (path) => {
                const callee = path.node.callee;
                if (t.isIdentifier(callee)) {
                    if (['eval', 'Function'].includes(callee.name)) {
                        patterns.push('eval_usage');
                    }
                }
            },
            WhileStatement: (path) => {
                if (t.isBooleanLiteral(path.node.test, { value: true })) {
                    patterns.push('infinite_loop');
                }
            }
        });

        return [...new Set(patterns)];
    }

    // 变量分析
    analyzeVariables() {
        console.log('\n📝 分析变量...');
        
        const variableMap = new Map();
        
        traverse(this.ast, {
            VariableDeclarator: (path) => {
                if (t.isIdentifier(path.node.id)) {
                    const name = path.node.id.name;
                    const init = path.node.init;
                    
                    let type = 'unknown';
                    let isObfuscated = false;
                    let value = null;

                    if (t.isStringLiteral(init)) {
                        type = 'string';
                        value = init.value;
                    } else if (t.isNumericLiteral(init)) {
                        type = 'number';
                        value = init.value;
                    } else if (t.isBooleanLiteral(init)) {
                        type = 'boolean';
                        value = init.value;
                    } else if (t.isArrayExpression(init)) {
                        type = 'array';
                        value = `[${init.elements.length} elements]`;
                    } else if (t.isObjectExpression(init)) {
                        type = 'object';
                        value = `{${init.properties.length} properties}`;
                    } else if (t.isFunctionExpression(init)) {
                        type = 'function';
                    }

                    // 检测混淆
                    if (name.length <= 2 || /^[a-zA-Z]$/.test(name) || /^_0x[a-f0-9]+$/i.test(name)) {
                        isObfuscated = true;
                    }

                    variableMap.set(name, { type, isObfuscated, value });
                }
            }
        });

        this.analysis.variables = Array.from(variableMap.entries()).map(([name, info]) => ({
            name,
            ...info
        }));

        const obfuscatedVars = this.analysis.variables.filter(v => v.isObfuscated);
        console.log(`  - 总变量数: ${this.analysis.variables.length}`);
        console.log(`  - 混淆变量: ${obfuscatedVars.length}`);
    }

    // 字符串分析
    analyzeStrings() {
        console.log('\n🔤 分析字符串...');
        
        const strings = [];
        
        traverse(this.ast, {
            StringLiteral: (path) => {
                const value = path.node.value;
                const analysis = {
                    value: value.length > 100 ? value.substring(0, 100) + '...' : value,
                    length: value.length,
                    isBase64: /^[A-Za-z0-9+/]+=*$/.test(value) && value.length > 4,
                    isHex: /^[0-9a-fA-F]+$/.test(value) && value.length > 4,
                    hasUnicode: /\\u[0-9a-fA-F]{4}/.test(value),
                    isSuspicious: value.length > 50 || /eval|function|script/i.test(value)
                };
                
                strings.push(analysis);
            }
        });

        this.analysis.strings = strings;
        
        const suspiciousStrings = strings.filter(s => s.isSuspicious);
        const base64Strings = strings.filter(s => s.isBase64);
        
        console.log(`  - 总字符串数: ${strings.length}`);
        console.log(`  - 可疑字符串: ${suspiciousStrings.length}`);
        console.log(`  - Base64字符串: ${base64Strings.length}`);
    }

    // 模式分析
    analyzePatterns() {
        console.log('\n🔍 分析混淆模式...');
        
        const patterns = {
            controlFlowFlattening: 0,
            stringObfuscation: 0,
            deadCodeInjection: 0,
            variableRenaming: 0,
            propertyMangling: 0
        };

        traverse(this.ast, {
            WhileStatement: (path) => {
                if (t.isBooleanLiteral(path.node.test, { value: true })) {
                    patterns.controlFlowFlattening++;
                }
            },
            SwitchStatement: (path) => {
                if (path.node.cases.length > 10) {
                    patterns.controlFlowFlattening++;
                }
            },
            StringLiteral: (path) => {
                if (path.node.value.length > 20 && /^[A-Za-z0-9+/]+=*$/.test(path.node.value)) {
                    patterns.stringObfuscation++;
                }
            },
            IfStatement: (path) => {
                if (t.isBooleanLiteral(path.node.test, { value: false })) {
                    patterns.deadCodeInjection++;
                }
            },
            Identifier: (path) => {
                if (path.node.name.length <= 2 && path.isReferencedIdentifier()) {
                    patterns.variableRenaming++;
                }
            },
            MemberExpression: (path) => {
                if (t.isStringLiteral(path.node.property)) {
                    patterns.propertyMangling++;
                }
            }
        });

        this.analysis.patterns = patterns;
        
        console.log(`  - 控制流扁平化: ${patterns.controlFlowFlattening}`);
        console.log(`  - 字符串混淆: ${patterns.stringObfuscation}`);
        console.log(`  - 死代码注入: ${patterns.deadCodeInjection}`);
        console.log(`  - 变量重命名: ${patterns.variableRenaming}`);
    }

    // 复杂度分析
    analyzeComplexity() {
        console.log('\n📈 分析代码复杂度...');
        
        let cyclomaticComplexity = 1; // 基础复杂度
        let nestingDepth = 0;
        let maxNesting = 0;
        
        traverse(this.ast, {
            enter: (path) => {
                if (t.isIfStatement(path.node) || 
                    t.isWhileStatement(path.node) || 
                    t.isForStatement(path.node) ||
                    t.isSwitchCase(path.node)) {
                    cyclomaticComplexity++;
                    nestingDepth++;
                    maxNesting = Math.max(maxNesting, nestingDepth);
                }
            },
            exit: (path) => {
                if (t.isIfStatement(path.node) || 
                    t.isWhileStatement(path.node) || 
                    t.isForStatement(path.node) ||
                    t.isSwitchCase(path.node)) {
                    nestingDepth--;
                }
            }
        });

        this.analysis.complexity = {
            cyclomatic: cyclomaticComplexity,
            maxNesting: maxNesting,
            maintainabilityIndex: Math.max(0, 171 - 5.2 * Math.log(cyclomaticComplexity) - 0.23 * maxNesting)
        };

        console.log(`  - 圈复杂度: ${cyclomaticComplexity}`);
        console.log(`  - 最大嵌套深度: ${maxNesting}`);
        console.log(`  - 可维护性指数: ${this.analysis.complexity.maintainabilityIndex.toFixed(2)}`);
    }

    // 安全分析
    analyzeSecurity() {
        console.log('\n🔒 分析安全风险...');
        
        const risks = [];
        
        traverse(this.ast, {
            CallExpression: (path) => {
                const callee = path.node.callee;
                
                if (t.isIdentifier(callee)) {
                    if (callee.name === 'eval') {
                        risks.push({ type: 'eval_usage', severity: 'high', location: 'eval()调用' });
                    }
                    if (callee.name === 'Function') {
                        risks.push({ type: 'function_constructor', severity: 'medium', location: 'Function()构造器' });
                    }
                }
                
                if (t.isMemberExpression(callee)) {
                    const property = callee.property;
                    if (t.isIdentifier(property)) {
                        if (['setTimeout', 'setInterval'].includes(property.name)) {
                            const firstArg = path.node.arguments[0];
                            if (t.isStringLiteral(firstArg)) {
                                risks.push({ type: 'string_execution', severity: 'medium', location: `${property.name}(字符串)` });
                            }
                        }
                    }
                }
            },
            
            StringLiteral: (path) => {
                const value = path.node.value;
                if (/document\.write|innerHTML|outerHTML/.test(value)) {
                    risks.push({ type: 'dom_manipulation', severity: 'medium', location: 'DOM操作字符串' });
                }
                if (/javascript:|data:/.test(value)) {
                    risks.push({ type: 'protocol_injection', severity: 'high', location: '协议注入' });
                }
            }
        });

        this.analysis.security = {
            risks,
            riskLevel: this.calculateRiskLevel(risks)
        };

        console.log(`  - 发现 ${risks.length} 个安全风险`);
        console.log(`  - 风险等级: ${this.analysis.security.riskLevel}`);
    }

    // 计算风险等级
    calculateRiskLevel(risks) {
        const highRisks = risks.filter(r => r.severity === 'high').length;
        const mediumRisks = risks.filter(r => r.severity === 'medium').length;
        
        if (highRisks > 0) return 'HIGH';
        if (mediumRisks > 2) return 'MEDIUM';
        if (mediumRisks > 0) return 'LOW';
        return 'MINIMAL';
    }

    // 生成报告
    generateReport() {
        console.log('\n📋 生成分析报告...');
        
        const report = {
            timestamp: new Date().toISOString(),
            analysis: this.analysis,
            recommendations: this.generateRecommendations()
        };

        return report;
    }

    // 生成建议
    generateRecommendations() {
        const recommendations = [];
        
        if (this.analysis.patterns.controlFlowFlattening > 0) {
            recommendations.push('检测到控制流扁平化，建议使用控制流去混淆工具');
        }
        
        if (this.analysis.patterns.stringObfuscation > 10) {
            recommendations.push('检测到大量字符串混淆，建议使用字符串解密工具');
        }
        
        if (this.analysis.variables.filter(v => v.isObfuscated).length > 20) {
            recommendations.push('检测到大量混淆变量，建议进行变量重命名');
        }
        
        if (this.analysis.security.riskLevel === 'HIGH') {
            recommendations.push('检测到高风险代码，建议进行安全审查');
        }
        
        if (this.analysis.complexity.cyclomatic > 50) {
            recommendations.push('代码复杂度过高，建议进行重构');
        }

        return recommendations;
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const inputFile = process.argv[2] || 'proxy.js';
    
    if (!fs.existsSync(inputFile)) {
        console.error(`❌ 输入文件不存在: ${inputFile}`);
        process.exit(1);
    }

    const sourceCode = fs.readFileSync(inputFile, 'utf8');
    const analyzer = new CodeAnalyzer(sourceCode);
    const report = analyzer.analyze();
    
    if (report) {
        const fullReport = analyzer.generateReport();
        
        // 保存详细报告
        fs.writeFileSync('analysis-report.json', JSON.stringify(fullReport, null, 2));
        console.log('\n✅ 分析完成！详细报告已保存到: analysis-report.json');
        
        // 显示建议
        console.log('\n💡 建议:');
        fullReport.recommendations.forEach(rec => {
            console.log(`  - ${rec}`);
        });
    }
}

module.exports = CodeAnalyzer;
