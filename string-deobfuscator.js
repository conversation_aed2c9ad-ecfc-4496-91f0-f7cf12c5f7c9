const traverse = require('@babel/traverse').default;
const t = require('@babel/types');

class StringDeobfuscator {
    constructor(ast) {
        this.ast = ast;
        this.stringArrays = new Map();
        this.decoderFunctions = new Map();
        this.encryptedStrings = new Set();
        this.decryptedCount = 0;
    }

    // 检测字符串混淆模式
    detectStringObfuscation() {
        console.log('\n🔍 检测字符串混淆模式...');
        
        const patterns = {
            stringArrays: 0,
            base64Strings: 0,
            hexStrings: 0,
            unicodeEscapes: 0,
            decoderFunctions: 0
        };

        traverse(this.ast, {
            VariableDeclarator: (path) => {
                if (t.isArrayExpression(path.node.init)) {
                    if (this.isStringArray(path.node.init)) {
                        patterns.stringArrays++;
                        this.analyzeStringArray(path);
                        console.log(`  📋 发现字符串数组: ${path.node.id.name}`);
                    }
                }
            },

            StringLiteral: (path) => {
                const value = path.node.value;
                
                if (this.isBase64String(value)) {
                    patterns.base64Strings++;
                    this.encryptedStrings.add(path);
                } else if (this.isHexString(value)) {
                    patterns.hexStrings++;
                    this.encryptedStrings.add(path);
                } else if (this.hasUnicodeEscapes(value)) {
                    patterns.unicodeEscapes++;
                    this.encryptedStrings.add(path);
                }
            },

            FunctionDeclaration: (path) => {
                if (this.isDecoderFunction(path)) {
                    patterns.decoderFunctions++;
                    this.analyzeDecoderFunction(path);
                    console.log(`  🔧 发现解码函数: ${path.node.id.name}`);
                }
            }
        });

        console.log(`📊 字符串混淆检测结果:`);
        console.log(`  - 字符串数组: ${patterns.stringArrays}`);
        console.log(`  - Base64字符串: ${patterns.base64Strings}`);
        console.log(`  - 十六进制字符串: ${patterns.hexStrings}`);
        console.log(`  - Unicode转义: ${patterns.unicodeEscapes}`);
        console.log(`  - 解码函数: ${patterns.decoderFunctions}`);

        return patterns;
    }

    // 检测字符串数组
    isStringArray(arrayExpr) {
        if (arrayExpr.elements.length < 3) return false;
        
        const stringElements = arrayExpr.elements.filter(el => 
            t.isStringLiteral(el) && el.value.length > 0
        );
        
        return stringElements.length / arrayExpr.elements.length > 0.7;
    }

    // 检测Base64字符串
    isBase64String(str) {
        if (str.length < 4 || str.length % 4 !== 0) return false;
        return /^[A-Za-z0-9+/]+=*$/.test(str);
    }

    // 检测十六进制字符串
    isHexString(str) {
        if (str.length < 4 || str.length % 2 !== 0) return false;
        return /^[0-9a-fA-F]+$/.test(str);
    }

    // 检测Unicode转义
    hasUnicodeEscapes(str) {
        return /\\u[0-9a-fA-F]{4}/.test(str) || /\\x[0-9a-fA-F]{2}/.test(str);
    }

    // 检测解码函数
    isDecoderFunction(funcPath) {
        const body = funcPath.node.body.body;
        if (body.length === 0) return false;

        // 检查函数是否包含字符串操作
        let hasStringOps = false;
        funcPath.traverse({
            CallExpression: (path) => {
                const callee = path.node.callee;
                if (t.isMemberExpression(callee) && t.isIdentifier(callee.property)) {
                    const methodName = callee.property.name;
                    if (['charAt', 'charCodeAt', 'fromCharCode', 'split', 'join', 'slice'].includes(methodName)) {
                        hasStringOps = true;
                    }
                }
            }
        });

        return hasStringOps;
    }

    // 分析字符串数组
    analyzeStringArray(varPath) {
        const arrayName = varPath.node.id.name;
        const elements = varPath.node.init.elements;
        
        const stringArray = elements.map(el => 
            t.isStringLiteral(el) ? el.value : null
        ).filter(val => val !== null);

        this.stringArrays.set(arrayName, stringArray);
        console.log(`    📝 字符串数组 ${arrayName} 包含 ${stringArray.length} 个字符串`);
    }

    // 分析解码函数
    analyzeDecoderFunction(funcPath) {
        const funcName = funcPath.node.id.name;
        
        // 尝试执行函数来理解其行为
        try {
            const funcCode = this.generateFunctionCode(funcPath.node);
            this.decoderFunctions.set(funcName, {
                code: funcCode,
                path: funcPath
            });
        } catch (error) {
            console.log(`    ⚠️  无法分析解码函数 ${funcName}: ${error.message}`);
        }
    }

    // 生成函数代码
    generateFunctionCode(funcNode) {
        const generate = require('@babel/generator').default;
        return generate(funcNode).code;
    }

    // 解密字符串
    decryptStrings() {
        console.log('\n🔓 开始字符串解密...');
        
        // 处理直接加密的字符串
        this.decryptDirectStrings();
        
        // 处理字符串数组引用
        this.decryptStringArrayReferences();
        
        // 处理解码函数调用
        this.decryptDecoderFunctionCalls();

        console.log(`✅ 字符串解密完成，解密了 ${this.decryptedCount} 个字符串`);
        return this.decryptedCount;
    }

    // 解密直接加密的字符串
    decryptDirectStrings() {
        for (const stringPath of this.encryptedStrings) {
            const value = stringPath.node.value;
            const decrypted = this.attemptDecryption(value);
            
            if (decrypted && decrypted !== value) {
                stringPath.node.value = decrypted;
                this.decryptedCount++;
                console.log(`  🔓 ${value.substring(0, 20)}... -> ${decrypted.substring(0, 20)}...`);
            }
        }
    }

    // 解密字符串数组引用
    decryptStringArrayReferences() {
        traverse(this.ast, {
            MemberExpression: (path) => {
                if (t.isIdentifier(path.node.object) && t.isNumericLiteral(path.node.property)) {
                    const arrayName = path.node.object.name;
                    const index = path.node.property.value;
                    
                    if (this.stringArrays.has(arrayName)) {
                        const stringArray = this.stringArrays.get(arrayName);
                        if (index >= 0 && index < stringArray.length) {
                            const originalString = stringArray[index];
                            const decryptedString = this.attemptDecryption(originalString);
                            
                            if (decryptedString !== originalString) {
                                path.replaceWith(t.stringLiteral(decryptedString));
                                this.decryptedCount++;
                                console.log(`  🔓 数组[${index}]: ${originalString.substring(0, 20)}... -> ${decryptedString.substring(0, 20)}...`);
                            }
                        }
                    }
                }
            }
        });
    }

    // 解密解码函数调用
    decryptDecoderFunctionCalls() {
        for (const [funcName, funcInfo] of this.decoderFunctions) {
            traverse(this.ast, {
                CallExpression: (path) => {
                    if (t.isIdentifier(path.node.callee, { name: funcName })) {
                        const result = this.executeDecoderFunction(funcInfo, path.node.arguments);
                        if (result) {
                            path.replaceWith(t.stringLiteral(result));
                            this.decryptedCount++;
                            console.log(`  🔓 函数调用: ${funcName}(...) -> ${result.substring(0, 30)}...`);
                        }
                    }
                }
            });
        }
    }

    // 执行解码函数
    executeDecoderFunction(funcInfo, args) {
        try {
            // 创建安全的执行环境
            const sandbox = this.createSandbox();
            
            // 准备参数
            const argValues = args.map(arg => {
                if (t.isStringLiteral(arg)) return arg.value;
                if (t.isNumericLiteral(arg)) return arg.value;
                if (t.isBooleanLiteral(arg)) return arg.value;
                return undefined;
            });

            // 执行函数
            const func = new Function('return ' + funcInfo.code)();
            const result = func.apply(sandbox, argValues);
            
            if (typeof result === 'string') {
                return result;
            }
        } catch (error) {
            // 忽略执行错误
        }
        
        return null;
    }

    // 创建安全的执行沙箱
    createSandbox() {
        return {
            // 提供常用的字符串操作方法
            String: String,
            parseInt: parseInt,
            parseFloat: parseFloat,
            decodeURIComponent: decodeURIComponent,
            unescape: unescape,
            atob: (str) => Buffer.from(str, 'base64').toString('binary'),
            btoa: (str) => Buffer.from(str, 'binary').toString('base64'),
            
            // 提供字符串数组的访问
            ...Object.fromEntries(this.stringArrays)
        };
    }

    // 尝试解密字符串
    attemptDecryption(str) {
        // Base64解码
        if (this.isBase64String(str)) {
            try {
                const decoded = Buffer.from(str, 'base64').toString('utf8');
                if (this.isPrintableString(decoded)) {
                    return decoded;
                }
            } catch (e) {}
        }

        // 十六进制解码
        if (this.isHexString(str)) {
            try {
                const decoded = Buffer.from(str, 'hex').toString('utf8');
                if (this.isPrintableString(decoded)) {
                    return decoded;
                }
            } catch (e) {}
        }

        // Unicode转义解码
        if (this.hasUnicodeEscapes(str)) {
            try {
                const decoded = str.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => 
                    String.fromCharCode(parseInt(hex, 16))
                ).replace(/\\x([0-9a-fA-F]{2})/g, (match, hex) => 
                    String.fromCharCode(parseInt(hex, 16))
                );
                return decoded;
            } catch (e) {}
        }

        // URL解码
        if (str.includes('%')) {
            try {
                const decoded = decodeURIComponent(str);
                if (decoded !== str) {
                    return decoded;
                }
            } catch (e) {}
        }

        // ROT13解码
        if (/^[A-Za-z\s]+$/.test(str)) {
            const rot13 = str.replace(/[A-Za-z]/g, char => {
                const start = char <= 'Z' ? 65 : 97;
                return String.fromCharCode(((char.charCodeAt(0) - start + 13) % 26) + start);
            });
            if (rot13 !== str && this.isPrintableString(rot13)) {
                return rot13;
            }
        }

        return str;
    }

    // 检查是否为可打印字符串
    isPrintableString(str) {
        if (str.length === 0) return false;
        return /^[\x20-\x7E\s\n\r\t]*$/.test(str);
    }

    // 清理字符串数组和解码函数
    cleanup() {
        console.log('\n🧹 清理字符串数组和解码函数...');
        
        let cleanedArrays = 0;
        let cleanedFunctions = 0;

        // 清理未使用的字符串数组
        for (const arrayName of this.stringArrays.keys()) {
            if (this.isArrayUnused(arrayName)) {
                this.removeVariableDeclaration(arrayName);
                cleanedArrays++;
            }
        }

        // 清理未使用的解码函数
        for (const funcName of this.decoderFunctions.keys()) {
            if (this.isFunctionUnused(funcName)) {
                this.removeFunctionDeclaration(funcName);
                cleanedFunctions++;
            }
        }

        console.log(`✅ 清理完成: ${cleanedArrays} 个数组, ${cleanedFunctions} 个函数`);
        return { cleanedArrays, cleanedFunctions };
    }

    // 检查数组是否未使用
    isArrayUnused(arrayName) {
        let usageCount = 0;
        
        traverse(this.ast, {
            Identifier: (path) => {
                if (path.node.name === arrayName && path.isReferencedIdentifier()) {
                    usageCount++;
                }
            }
        });
        
        return usageCount <= 1; // 只有声明处的使用
    }

    // 检查函数是否未使用
    isFunctionUnused(funcName) {
        let usageCount = 0;
        
        traverse(this.ast, {
            Identifier: (path) => {
                if (path.node.name === funcName && path.isReferencedIdentifier()) {
                    usageCount++;
                }
            }
        });
        
        return usageCount === 0;
    }

    // 移除变量声明
    removeVariableDeclaration(varName) {
        traverse(this.ast, {
            VariableDeclarator: (path) => {
                if (t.isIdentifier(path.node.id, { name: varName })) {
                    if (path.parent.declarations.length === 1) {
                        path.parentPath.remove();
                    } else {
                        path.remove();
                    }
                }
            }
        });
    }

    // 移除函数声明
    removeFunctionDeclaration(funcName) {
        traverse(this.ast, {
            FunctionDeclaration: (path) => {
                if (t.isIdentifier(path.node.id, { name: funcName })) {
                    path.remove();
                }
            }
        });
    }

    // 执行完整的字符串去混淆
    process() {
        const patterns = this.detectStringObfuscation();
        const decryptedCount = this.decryptStrings();
        const cleanup = this.cleanup();
        
        return {
            patterns,
            decryptedCount,
            cleanup,
            totalStringArrays: this.stringArrays.size,
            totalDecoderFunctions: this.decoderFunctions.size
        };
    }
}

module.exports = StringDeobfuscator;
