{"timestamp": "2025-08-15T12:38:06.572Z", "totalTests": 3, "successfulTests": 2, "failedTests": 1, "averageExecutionTime": 11.333333333333334, "results": [{"name": "简单混淆测试", "success": false, "error": "Cannot read properties of null (reading 'complexity')", "executionTime": 21}, {"name": "控制流扁平化测试", "success": true, "executionTime": 10, "originalCode": "\n            function test() {\n                var state = 0;\n                while (true) {\n                    switch (state) {\n                        case 0:\n                            console.log(\"step 1\");\n                            state = 1;\n                            break;\n                        case 1:\n                            console.log(\"step 2\");\n                            state = 2;\n                            break;\n                        case 2:\n                            return \"done\";\n                    }\n                }\n            }\n        ", "deobfuscatedCode": "function test() {\n  var state = 0;\n}", "originalAnalysis": {"overview": {"totalLines": 19, "totalNodes": 43, "fileSize": 581, "estimatedComplexity": 0}, "functions": [{"name": "test", "type": "declaration", "parameters": 0, "statements": 12, "loops": 1, "conditions": 1, "returns": 1, "complexity": 7, "isObfuscated": false, "suspiciousPatterns": ["infinite_loop"]}], "variables": [{"name": "state", "type": "number", "isObfuscated": false, "value": 0}], "strings": [{"value": "step 1", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "step 2", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "done", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}], "patterns": {"controlFlowFlattening": 1, "stringObfuscation": 0, "deadCodeInjection": 0, "variableRenaming": 0, "propertyMangling": 0}, "complexity": {"cyclomatic": 5, "maxNesting": 2, "maintainabilityIndex": 162.17092285534267}, "security": {"risks": [], "riskLevel": "MINIMAL"}}, "deobfuscatedAnalysis": {"overview": {"totalLines": 3, "totalNodes": 8, "fileSize": 36, "estimatedComplexity": 0}, "functions": [{"name": "test", "type": "declaration", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}], "variables": [{"name": "state", "type": "number", "isObfuscated": false, "value": 0}], "strings": [], "patterns": {"controlFlowFlattening": 0, "stringObfuscation": 0, "deadCodeInjection": 0, "variableRenaming": 0, "propertyMangling": 0}, "complexity": {"cyclomatic": 1, "maxNesting": 0, "maintainabilityIndex": 171}, "security": {"risks": [], "riskLevel": "MINIMAL"}}, "improvements": {"complexityReduction": 4, "maintainabilityImprovement": 8.829077144657333, "variableImprovement": 0, "securityRiskReduction": 0, "sizeChange": -545}}, {"name": "字符串数组测试", "success": true, "executionTime": 3, "originalCode": "\n            var strings = [\"hello\", \"world\", \"test\"];\n            function getString(index) {\n                return strings[index];\n            }\n            console.log(getString(0) + \" \" + getString(1));\n        ", "deobfuscatedCode": "function getString(index) {\n  return strings[index];\n}\nconsole.log(getString(0) + \" \" + getString(1));", "originalAnalysis": {"overview": {"totalLines": 7, "totalNodes": 30, "fileSize": 216, "estimatedComplexity": 0}, "functions": [{"name": "getString", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}], "variables": [{"name": "strings", "type": "array", "isObfuscated": false, "value": "[3 elements]"}], "strings": [{"value": "hello", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "world", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "test", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " ", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}], "patterns": {"controlFlowFlattening": 0, "stringObfuscation": 0, "deadCodeInjection": 0, "variableRenaming": 0, "propertyMangling": 0}, "complexity": {"cyclomatic": 1, "maxNesting": 0, "maintainabilityIndex": 171}, "security": {"risks": [], "riskLevel": "MINIMAL"}}, "deobfuscatedAnalysis": {"overview": {"totalLines": 4, "totalNodes": 23, "fileSize": 102, "estimatedComplexity": 0}, "functions": [{"name": "getString", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}], "variables": [], "strings": [{"value": " ", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}], "patterns": {"controlFlowFlattening": 0, "stringObfuscation": 0, "deadCodeInjection": 0, "variableRenaming": 0, "propertyMangling": 0}, "complexity": {"cyclomatic": 1, "maxNesting": 0, "maintainabilityIndex": 171}, "security": {"risks": [], "riskLevel": "MINIMAL"}}, "improvements": {"complexityReduction": 0, "maintainabilityImprovement": 0, "variableImprovement": 0, "securityRiskReduction": 0, "sizeChange": -114}}]}