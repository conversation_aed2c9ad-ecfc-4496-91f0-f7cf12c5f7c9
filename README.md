# JavaScript 逆向工程和去混淆工具

这是一个专业的JavaScript逆向工程工具套件，使用AST分析技术来检测和去除各种代码混淆。

## 🚀 功能特性

### 核心功能
- **AST解析**: 使用Babel解析器进行深度代码分析
- **控制流去扁平化**: 检测和重构被扁平化的控制流
- **字符串解密**: 自动解密Base64、十六进制、Unicode等编码字符串
- **变量重命名**: 将混淆的变量名替换为有意义的名称
- **死代码消除**: 移除永远不会执行的代码分支
- **安全分析**: 检测潜在的安全风险

### 支持的混淆类型
- ✅ 控制流扁平化 (while + switch模式)
- ✅ 字符串数组混淆
- ✅ Base64/十六进制字符串加密
- ✅ 变量名混淆
- ✅ 死代码注入
- ✅ Unicode转义序列
- ✅ 函数名混淆

## 📦 安装

```bash
# 安装依赖
npm install

# 或者使用yarn
yarn install
```

## 🛠️ 使用方法

### 1. 基本去混淆

```bash
# 去混淆单个文件
node deobfuscator.js input.js output.js

# 使用默认文件名
node deobfuscator.js  # 默认处理 proxy.js -> deobfuscated.js
```

### 2. 代码分析

```bash
# 分析代码结构和混淆模式
node analyzer.js proxy.js

# 生成详细的分析报告
node analyzer.js proxy.js  # 输出到 analysis-report.json
```

### 3. 运行测试

```bash
# 运行完整测试套件
node test.js

# 或使用npm脚本
npm test
```

## 📋 工具说明

### deobfuscator.js - 主去混淆器
这是核心的去混淆工具，集成了所有去混淆功能：

```javascript
const JSDeobfuscator = require('./deobfuscator');

const sourceCode = fs.readFileSync('obfuscated.js', 'utf8');
const deobfuscator = new JSDeobfuscator(sourceCode);
const cleanCode = deobfuscator.deobfuscate();
```

**主要功能:**
- AST解析和代码结构分析
- 变量名去混淆和重命名
- 字符串解密
- 死代码消除
- 代码格式化和美化

### control-flow-deobfuscator.js - 控制流去混淆器
专门处理控制流扁平化的模块：

```javascript
const ControlFlowDeobfuscator = require('./control-flow-deobfuscator');

const deobfuscator = new ControlFlowDeobfuscator(ast);
const result = deobfuscator.process();
```

**检测模式:**
- `while(true) + switch` 扁平化
- 大型switch语句
- 字符串数组控制流

### string-deobfuscator.js - 字符串去混淆器
专门处理字符串混淆的模块：

```javascript
const StringDeobfuscator = require('./string-deobfuscator');

const deobfuscator = new StringDeobfuscator(ast);
const result = deobfuscator.process();
```

**支持的编码:**
- Base64编码
- 十六进制编码
- Unicode转义序列
- URL编码
- ROT13编码

### analyzer.js - 代码分析器
提供详细的代码分析和混淆检测：

```javascript
const CodeAnalyzer = require('./analyzer');

const analyzer = new CodeAnalyzer(sourceCode);
const report = analyzer.analyze();
```

**分析维度:**
- 代码概览 (行数、节点数、文件大小)
- 函数分析 (复杂度、参数、可疑模式)
- 变量分析 (类型、混淆检测)
- 字符串分析 (编码检测、可疑内容)
- 混淆模式检测
- 安全风险评估

## 📊 输出示例

### 去混淆统计
```
📊 去混淆统计:
  原始大小: 12823 字符
  最终大小: 8945 字符
  压缩比例: 30.23%
  函数数量: 45
  变量重命名: 127
  字符串解密: 23
  死代码移除: 8
  控制流修复: 3
```

### 分析报告
```json
{
  "overview": {
    "totalLines": 342,
    "totalNodes": 1523,
    "fileSize": 12823,
    "estimatedComplexity": 15
  },
  "patterns": {
    "controlFlowFlattening": 3,
    "stringObfuscation": 23,
    "deadCodeInjection": 8,
    "variableRenaming": 127
  },
  "security": {
    "riskLevel": "MEDIUM",
    "risks": [
      {
        "type": "eval_usage",
        "severity": "high",
        "location": "eval()调用"
      }
    ]
  }
}
```

## 🔧 高级用法

### 自定义去混淆流程

```javascript
const deobfuscator = new JSDeobfuscator(sourceCode);

// 只执行特定步骤
deobfuscator.parseCode();
deobfuscator.analyzeStructure();
deobfuscator.deobfuscateVariables();

// 获取中间结果
const partialResult = deobfuscator.generateCleanCode();
```

### 批量处理

```javascript
const files = ['file1.js', 'file2.js', 'file3.js'];

files.forEach(file => {
    const code = fs.readFileSync(file, 'utf8');
    const deobfuscator = new JSDeobfuscator(code);
    const result = deobfuscator.deobfuscate();
    
    fs.writeFileSync(file.replace('.js', '_clean.js'), result);
});
```

## ⚠️ 注意事项

1. **安全性**: 工具会尝试执行某些解码函数，请确保在安全环境中运行
2. **兼容性**: 支持ES5-ES2020语法，部分ES2021+特性可能不支持
3. **性能**: 大文件(>1MB)可能需要较长处理时间
4. **准确性**: 复杂的自定义混淆可能需要手动调整

## 🐛 故障排除

### 常见问题

**Q: 解析失败 "Unexpected token"**
A: 检查代码语法，可能包含不支持的ES特性

**Q: 去混淆效果不佳**
A: 尝试先运行分析器了解混淆模式，然后针对性处理

**Q: 内存不足**
A: 对于大文件，考虑分块处理或增加Node.js内存限制

### 调试模式

```bash
# 启用详细日志
DEBUG=* node deobfuscator.js input.js

# 保存中间结果
node deobfuscator.js --save-intermediate input.js
```

## 📈 性能优化

- 使用`--max-old-space-size=4096`增加内存限制
- 对大文件进行预处理，移除注释和空白
- 使用多进程处理多个文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进工具！

## 📄 许可证

MIT License - 详见LICENSE文件

## 🔗 相关资源

- [Babel AST Explorer](https://astexplorer.net/)
- [JavaScript混淆技术研究](https://example.com)
- [代码安全分析指南](https://example.com)
