{"name": "js-deobfuscator", "version": "1.0.0", "description": "JavaScript逆向工程和去混淆工具", "main": "deobfuscator.js", "scripts": {"start": "node deobfuscator.js", "deobfuscate": "node deobfuscator.js proxy.js deobfuscated.js", "analyze": "node analyzer.js", "test": "node test.js"}, "keywords": ["javascript", "deobfuscation", "reverse-engineering", "ast", "babel", "security"], "author": "JS Reverse Engineer", "license": "MIT", "dependencies": {"@babel/core": "^7.28.3", "@babel/generator": "^7.28.3", "@babel/parser": "^7.28.3", "@babel/traverse": "^7.28.3", "@babel/types": "^7.28.2"}, "devDependencies": {"chalk": "^4.1.2", "commander": "^9.4.1", "progress": "^2.0.3"}, "engines": {"node": ">=14.0.0"}}