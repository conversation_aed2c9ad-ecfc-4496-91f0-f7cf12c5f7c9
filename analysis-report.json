{"timestamp": "2025-08-15T12:34:50.397Z", "analysis": {"overview": {"totalLines": 12823, "totalNodes": 50719, "fileSize": 401865, "estimatedComplexity": 507}, "functions": [{"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7368, "loops": 252, "conditions": 611, "returns": 648, "complexity": 4242, "isObfuscated": true, "suspiciousPatterns": ["base64_string", "infinite_loop", "dynamic_execution"]}, {"name": "_defineProperty", "type": "declaration", "parameters": 3, "statements": 7, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "ownKeys", "type": "declaration", "parameters": 2, "statements": 12, "loops": 0, "conditions": 2, "returns": 2, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_objectSpread2", "type": "declaration", "parameters": 1, "statements": 17, "loops": 1, "conditions": 2, "returns": 1, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_objectWithoutProperties", "type": "declaration", "parameters": 2, "statements": 17, "loops": 1, "conditions": 3, "returns": 2, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_objectWithoutPropertiesLoose", "type": "declaration", "parameters": 2, "statements": 14, "loops": 0, "conditions": 3, "returns": 2, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_toPrimitive", "type": "declaration", "parameters": 2, "statements": 13, "loops": 0, "conditions": 3, "returns": 3, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "declaration", "parameters": 1, "statements": 7, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "getDefaultExportFromCjs", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "getAugmentedNamespace", "type": "declaration", "parameters": 1, "statements": 24, "loops": 0, "conditions": 3, "returns": 5, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "a", "type": "expression", "parameters": 0, "statements": 5, "loops": 0, "conditions": 1, "returns": 2, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3803, "loops": 137, "conditions": 443, "returns": 352, "complexity": 2259, "isObfuscated": true, "suspiciousPatterns": ["base64_string", "infinite_loop", "dynamic_execution"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3799, "loops": 137, "conditions": 443, "returns": 352, "complexity": 2257, "isObfuscated": true, "suspiciousPatterns": ["base64_string", "infinite_loop", "dynamic_execution"]}, {"name": "int2char", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "op_and", "type": "declaration", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "op_or", "type": "declaration", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "op_xor", "type": "declaration", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "op_andnot", "type": "declaration", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "lbit", "type": "declaration", "parameters": 1, "statements": 25, "loops": 0, "conditions": 6, "returns": 2, "complexity": 12, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "cbit", "type": "declaration", "parameters": 1, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "hex2b64", "type": "declaration", "parameters": 1, "statements": 20, "loops": 2, "conditions": 2, "returns": 1, "complexity": 21, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "b64tohex", "type": "declaration", "parameters": 1, "statements": 38, "loops": 1, "conditions": 6, "returns": 1, "complexity": 25, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "extendStatics", "type": "declaration", "parameters": 2, "statements": 11, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 6, "loops": 0, "conditions": 1, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "__extends", "type": "declaration", "parameters": 2, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "__", "type": "declaration", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 45, "loops": 4, "conditions": 6, "returns": 1, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 47, "loops": 3, "conditions": 6, "returns": 1, "complexity": 25, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 13, "loops": 0, "conditions": 3, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 70, "loops": 5, "conditions": 5, "returns": 5, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "Int10", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 18, "loops": 1, "conditions": 2, "returns": 0, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 2, "conditions": 1, "returns": 0, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 1, "conditions": 1, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7, "loops": 0, "conditions": 1, "returns": 2, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "stringCut", "type": "declaration", "parameters": 2, "statements": 5, "loops": 0, "conditions": 1, "returns": 1, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 216, "loops": 12, "conditions": 29, "returns": 21, "complexity": 134, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "Stream", "type": "declaration", "parameters": 2, "statements": 9, "loops": 0, "conditions": 1, "returns": 0, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 0, "conditions": 2, "returns": 2, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 14, "loops": 1, "conditions": 2, "returns": 1, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 8, "loops": 1, "conditions": 1, "returns": 2, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 6, "loops": 1, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 14, "loops": 1, "conditions": 2, "returns": 1, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 10, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 30, "loops": 0, "conditions": 8, "returns": 2, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 34, "loops": 3, "conditions": 4, "returns": 3, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 16, "loops": 2, "conditions": 1, "returns": 2, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 17, "loops": 1, "conditions": 3, "returns": 2, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 32, "loops": 1, "conditions": 5, "returns": 2, "complexity": 20, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 218, "loops": 5, "conditions": 30, "returns": 62, "complexity": 117, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "ASN1", "type": "declaration", "parameters": 5, "statements": 9, "loops": 0, "conditions": 1, "returns": 0, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 34, "loops": 0, "conditions": 2, "returns": 31, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 43, "loops": 0, "conditions": 9, "returns": 18, "complexity": 34, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 20, "loops": 1, "conditions": 4, "returns": 1, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 17, "loops": 1, "conditions": 3, "returns": 3, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 66, "loops": 3, "conditions": 11, "returns": 2, "complexity": 41, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "getSub", "type": "declaration", "parameters": 0, "statements": 25, "loops": 2, "conditions": 3, "returns": 1, "complexity": 15, "isObfuscated": false, "suspiciousPatterns": ["infinite_loop"]}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 22, "loops": 0, "conditions": 1, "returns": 3, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "ASN1Tag", "type": "declaration", "parameters": 1, "statements": 13, "loops": 0, "conditions": 1, "returns": 0, "complexity": 6, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 1260, "loops": 58, "conditions": 173, "returns": 99, "complexity": 843, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "BigInteger", "type": "declaration", "parameters": 3, "statements": 11, "loops": 0, "conditions": 3, "returns": 0, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 57, "loops": 1, "conditions": 13, "returns": 4, "complexity": 34, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 20, "loops": 1, "conditions": 4, "returns": 5, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 1, "returns": 1, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 8, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 16, "loops": 0, "conditions": 5, "returns": 5, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 9, "loops": 0, "conditions": 2, "returns": 3, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 34, "loops": 1, "conditions": 7, "returns": 1, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 8, "loops": 0, "conditions": 1, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 8, "loops": 0, "conditions": 1, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 10, "loops": 1, "conditions": 2, "returns": 3, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 98, "loops": 5, "conditions": 14, "returns": 2, "complexity": 60, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 70, "loops": 3, "conditions": 14, "returns": 6, "complexity": 81, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 39, "loops": 1, "conditions": 8, "returns": 2, "complexity": 39, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 29, "loops": 4, "conditions": 4, "returns": 5, "complexity": 24, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 1, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 11, "loops": 0, "conditions": 2, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 61, "loops": 1, "conditions": 14, "returns": 1, "complexity": 36, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 1, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 10, "loops": 2, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 6, "loops": 1, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 17, "loops": 2, "conditions": 0, "returns": 0, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 20, "loops": 1, "conditions": 2, "returns": 1, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 35, "loops": 3, "conditions": 3, "returns": 0, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 16, "loops": 2, "conditions": 1, "returns": 0, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 2, "conditions": 2, "returns": 0, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 75, "loops": 3, "conditions": 13, "returns": 3, "complexity": 62, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 18, "loops": 0, "conditions": 3, "returns": 4, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 20, "loops": 1, "conditions": 2, "returns": 2, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 19, "loops": 1, "conditions": 2, "returns": 2, "complexity": 21, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 33, "loops": 1, "conditions": 6, "returns": 0, "complexity": 27, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 31, "loops": 1, "conditions": 6, "returns": 0, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 22, "loops": 3, "conditions": 1, "returns": 0, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 35, "loops": 3, "conditions": 3, "returns": 0, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 15, "loops": 2, "conditions": 2, "returns": 1, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 14, "loops": 3, "conditions": 0, "returns": 0, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 12, "loops": 2, "conditions": 0, "returns": 0, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 16, "loops": 1, "conditions": 3, "returns": 2, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 29, "loops": 2, "conditions": 5, "returns": 4, "complexity": 30, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 47, "loops": 0, "conditions": 9, "returns": 1, "complexity": 43, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "gcda1", "type": "declaration", "parameters": 0, "statements": 24, "loops": 0, "conditions": 5, "returns": 0, "complexity": 24, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 40, "loops": 0, "conditions": 7, "returns": 0, "complexity": 32, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "bnpfn1_1", "type": "declaration", "parameters": 0, "statements": 12, "loops": 0, "conditions": 2, "returns": 0, "complexity": 12, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 16, "loops": 0, "conditions": 0, "returns": 3, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "NullExp", "type": "declaration", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 26, "loops": 0, "conditions": 1, "returns": 4, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "Classic", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 53, "loops": 3, "conditions": 2, "returns": 3, "complexity": 32, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "<PERSON>", "type": "declaration", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 8, "loops": 0, "conditions": 1, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 19, "loops": 3, "conditions": 1, "returns": 0, "complexity": 16, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 49, "loops": 2, "conditions": 3, "returns": 5, "complexity": 34, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "<PERSON>", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 12, "loops": 0, "conditions": 2, "returns": 3, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 15, "loops": 2, "conditions": 1, "returns": 0, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "nbi", "type": "declaration", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "parseBigInt", "type": "declaration", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "am1", "type": "declaration", "parameters": 6, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "am2", "type": "declaration", "parameters": 6, "statements": 12, "loops": 1, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "am3", "type": "declaration", "parameters": 6, "statements": 12, "loops": 1, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "intAt", "type": "declaration", "parameters": 2, "statements": 7, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "nbv", "type": "declaration", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "nbits", "type": "declaration", "parameters": 1, "statements": 24, "loops": 0, "conditions": 5, "returns": 1, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 33, "loops": 2, "conditions": 0, "returns": 2, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "Arcfour", "type": "declaration", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 16, "loops": 2, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "prng_newstate", "type": "declaration", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "onMouseMoveListener_1", "type": "declaration", "parameters": 1, "statements": 17, "loops": 0, "conditions": 3, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "rng_get_byte", "type": "declaration", "parameters": 0, "statements": 14, "loops": 2, "conditions": 1, "returns": 1, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 9, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "SecureRandom", "type": "declaration", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 1, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "pkcs1pad1", "type": "declaration", "parameters": 2, "statements": 12, "loops": 1, "conditions": 1, "returns": 2, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "pkcs1pad2", "type": "declaration", "parameters": 2, "statements": 34, "loops": 3, "conditions": 3, "returns": 2, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 242, "loops": 4, "conditions": 22, "returns": 22, "complexity": 173, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "RSAKey", "type": "declaration", "parameters": 0, "statements": 9, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 1, "conditions": 1, "returns": 2, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 7, "loops": 0, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 15, "loops": 0, "conditions": 3, "returns": 4, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 19, "loops": 0, "conditions": 1, "returns": 3, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 19, "loops": 0, "conditions": 1, "returns": 3, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 8, "loops": 0, "conditions": 1, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 8, "statements": 13, "loops": 0, "conditions": 1, "returns": 0, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 35, "loops": 3, "conditions": 4, "returns": 0, "complexity": 37, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 1, "returns": 2, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 56, "loops": 0, "conditions": 4, "returns": 0, "complexity": 41, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "loop1", "type": "declaration", "parameters": 0, "statements": 48, "loops": 0, "conditions": 4, "returns": 0, "complexity": 39, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "loop4", "type": "declaration", "parameters": 0, "statements": 21, "loops": 0, "conditions": 2, "returns": 0, "complexity": 18, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "loop3", "type": "declaration", "parameters": 0, "statements": 11, "loops": 0, "conditions": 1, "returns": 0, "complexity": 10, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 0, "conditions": 1, "returns": 0, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "loop2", "type": "declaration", "parameters": 0, "statements": 11, "loops": 0, "conditions": 1, "returns": 0, "complexity": 10, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 0, "conditions": 1, "returns": 0, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 17, "loops": 0, "conditions": 3, "returns": 4, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 9, "loops": 0, "conditions": 1, "returns": 2, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "pkcs1unpad2", "type": "declaration", "parameters": 2, "statements": 30, "loops": 3, "conditions": 4, "returns": 3, "complexity": 21, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "getDigestHeader", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "removeDigest<PERSON>eader", "type": "declaration", "parameters": 1, "statements": 11, "loops": 0, "conditions": 2, "returns": 2, "complexity": 7, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 37, "loops": 1, "conditions": 5, "returns": 0, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "F", "type": "declaration", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_IEEnumFix", "type": "declaration", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 8, "loops": 1, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 153, "loops": 3, "conditions": 29, "returns": 22, "complexity": 89, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 27, "loops": 1, "conditions": 5, "returns": 1, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 109, "loops": 2, "conditions": 23, "returns": 18, "complexity": 61, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 1, "conditions": 1, "returns": 1, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 42, "loops": 3, "conditions": 4, "returns": 3, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "itox", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "roidtox", "type": "declaration", "parameters": 1, "statements": 21, "loops": 2, "conditions": 2, "returns": 1, "complexity": 14, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 41, "loops": 0, "conditions": 6, "returns": 5, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 22, "loops": 0, "conditions": 5, "returns": 2, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 31, "loops": 0, "conditions": 4, "returns": 2, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 52, "loops": 0, "conditions": 4, "returns": 6, "complexity": 38, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 22, "loops": 0, "conditions": 3, "returns": 1, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 5, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 6, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 0, "conditions": 2, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 32, "loops": 0, "conditions": 5, "returns": 1, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 78, "loops": 4, "conditions": 10, "returns": 2, "complexity": 46, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 8, "loops": 0, "conditions": 1, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 21, "loops": 2, "conditions": 2, "returns": 0, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 1, "conditions": 1, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 1, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 84, "loops": 3, "conditions": 11, "returns": 3, "complexity": 52, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "itox", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "roidtox", "type": "declaration", "parameters": 1, "statements": 21, "loops": 2, "conditions": 2, "returns": 1, "complexity": 14, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 16, "loops": 1, "conditions": 1, "returns": 0, "complexity": 12, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 1, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 29, "loops": 0, "conditions": 4, "returns": 1, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 32, "loops": 0, "conditions": 6, "returns": 1, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 36, "loops": 0, "conditions": 7, "returns": 1, "complexity": 24, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 12, "loops": 1, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 21, "loops": 1, "conditions": 3, "returns": 1, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 11, "loops": 1, "conditions": 1, "returns": 1, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 36, "loops": 0, "conditions": 5, "returns": 1, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 14, "loops": 0, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 116, "loops": 0, "conditions": 8, "returns": 15, "complexity": 71, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "JSEncryptRSAKey", "type": "declaration", "parameters": 1, "statements": 11, "loops": 0, "conditions": 3, "returns": 1, "complexity": 11, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 42, "loops": 0, "conditions": 3, "returns": 3, "complexity": 30, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 6, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 7, "loops": 0, "conditions": 1, "returns": 2, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 11, "loops": 0, "conditions": 1, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 99, "loops": 1, "conditions": 4, "returns": 19, "complexity": 49, "isObfuscated": true, "suspiciousPatterns": ["dynamic_execution"]}, {"name": "JSEncrypt", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 1, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 1, "conditions": 1, "returns": 2, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 0, "conditions": 2, "returns": 2, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": ["dynamic_execution"]}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "arrow", "parameters": 1, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "commonjsRequire", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireCore", "type": "declaration", "parameters": 0, "statements": 252, "loops": 8, "conditions": 20, "returns": 30, "complexity": 129, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 245, "loops": 8, "conditions": 19, "returns": 28, "complexity": 126, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 240, "loops": 8, "conditions": 19, "returns": 28, "complexity": 124, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 237, "loops": 8, "conditions": 19, "returns": 27, "complexity": 123, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "cryptoSecureRandomInt", "type": "declaration", "parameters": 0, "statements": 16, "loops": 0, "conditions": 3, "returns": 2, "complexity": 9, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 10, "loops": 0, "conditions": 0, "returns": 2, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "F", "type": "declaration", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 31, "loops": 0, "conditions": 4, "returns": 4, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 13, "loops": 0, "conditions": 2, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 0, "conditions": 2, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 7, "loops": 0, "conditions": 1, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 2, "conditions": 1, "returns": 1, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 1, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 1, "conditions": 0, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 23, "loops": 1, "conditions": 2, "returns": 1, "complexity": 12, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 2, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 4, "loops": 0, "conditions": 0, "returns": 2, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireX64Core", "type": "declaration", "parameters": 0, "statements": 51, "loops": 2, "conditions": 2, "returns": 5, "complexity": 23, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 44, "loops": 2, "conditions": 1, "returns": 3, "complexity": 20, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 39, "loops": 2, "conditions": 1, "returns": 3, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 36, "loops": 2, "conditions": 1, "returns": 2, "complexity": 16, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 7, "loops": 0, "conditions": 1, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 10, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireLibTypedarrays", "type": "declaration", "parameters": 0, "statements": 42, "loops": 1, "conditions": 5, "returns": 4, "complexity": 20, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 35, "loops": 1, "conditions": 4, "returns": 2, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 30, "loops": 1, "conditions": 4, "returns": 2, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 27, "loops": 1, "conditions": 4, "returns": 1, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 17, "loops": 1, "conditions": 3, "returns": 0, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireEncUtf16", "type": "declaration", "parameters": 0, "statements": 57, "loops": 4, "conditions": 1, "returns": 8, "complexity": 31, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 50, "loops": 4, "conditions": 0, "returns": 6, "complexity": 28, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 45, "loops": 4, "conditions": 0, "returns": 6, "complexity": 25, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 42, "loops": 4, "conditions": 0, "returns": 5, "complexity": 24, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 1, "conditions": 0, "returns": 1, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "swapEndian", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireEncBase64", "type": "declaration", "parameters": 0, "statements": 75, "loops": 5, "conditions": 6, "returns": 6, "complexity": 45, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 68, "loops": 5, "conditions": 5, "returns": 4, "complexity": 42, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 63, "loops": 5, "conditions": 5, "returns": 4, "complexity": 39, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 60, "loops": 5, "conditions": 5, "returns": 3, "complexity": 38, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 22, "loops": 3, "conditions": 1, "returns": 1, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 1, "conditions": 3, "returns": 1, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "parseLoop", "type": "declaration", "parameters": 3, "statements": 13, "loops": 1, "conditions": 1, "returns": 1, "complexity": 8, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireEncBase64url", "type": "declaration", "parameters": 0, "statements": 75, "loops": 5, "conditions": 6, "returns": 6, "complexity": 45, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 68, "loops": 5, "conditions": 5, "returns": 4, "complexity": 42, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 63, "loops": 5, "conditions": 5, "returns": 4, "complexity": 39, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 60, "loops": 5, "conditions": 5, "returns": 3, "complexity": 38, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 22, "loops": 3, "conditions": 1, "returns": 1, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 18, "loops": 1, "conditions": 3, "returns": 1, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "parseLoop", "type": "declaration", "parameters": 3, "statements": 13, "loops": 1, "conditions": 1, "returns": 1, "complexity": 8, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireMd5", "type": "declaration", "parameters": 0, "statements": 166, "loops": 3, "conditions": 1, "returns": 9, "complexity": 90, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 159, "loops": 3, "conditions": 0, "returns": 7, "complexity": 87, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 154, "loops": 3, "conditions": 0, "returns": 7, "complexity": 84, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 151, "loops": 3, "conditions": 0, "returns": 6, "complexity": 83, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 1, "conditions": 0, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 95, "loops": 1, "conditions": 0, "returns": 0, "complexity": 67, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 19, "loops": 1, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "FF", "type": "declaration", "parameters": 7, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "GG", "type": "declaration", "parameters": 7, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "HH", "type": "declaration", "parameters": 7, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "II", "type": "declaration", "parameters": 7, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireSha1", "type": "declaration", "parameters": 0, "statements": 79, "loops": 1, "conditions": 5, "returns": 5, "complexity": 25, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 72, "loops": 1, "conditions": 4, "returns": 3, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 67, "loops": 1, "conditions": 4, "returns": 3, "complexity": 19, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 64, "loops": 1, "conditions": 4, "returns": 2, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 37, "loops": 1, "conditions": 4, "returns": 0, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 11, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireSha256", "type": "declaration", "parameters": 0, "statements": 113, "loops": 3, "conditions": 5, "returns": 8, "complexity": 39, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 106, "loops": 3, "conditions": 4, "returns": 6, "complexity": 36, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 101, "loops": 3, "conditions": 4, "returns": 6, "complexity": 33, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 98, "loops": 3, "conditions": 4, "returns": 5, "complexity": 32, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 25, "loops": 2, "conditions": 3, "returns": 3, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "isPrime", "type": "declaration", "parameters": 1, "statements": 8, "loops": 1, "conditions": 1, "returns": 2, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "getFractionalBits", "type": "declaration", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 43, "loops": 1, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 11, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireSha224", "type": "declaration", "parameters": 0, "statements": 30, "loops": 0, "conditions": 1, "returns": 4, "complexity": 12, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 23, "loops": 0, "conditions": 0, "returns": 2, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 18, "loops": 0, "conditions": 0, "returns": 2, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 15, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireSha512", "type": "declaration", "parameters": 0, "statements": 183, "loops": 2, "conditions": 2, "returns": 6, "complexity": 107, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 176, "loops": 2, "conditions": 1, "returns": 4, "complexity": 104, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 171, "loops": 2, "conditions": 1, "returns": 4, "complexity": 100, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 168, "loops": 2, "conditions": 1, "returns": 3, "complexity": 99, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "X64Word_create", "type": "declaration", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 1, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 129, "loops": 1, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 12, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireSha384", "type": "declaration", "parameters": 0, "statements": 31, "loops": 0, "conditions": 1, "returns": 4, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 24, "loops": 0, "conditions": 0, "returns": 2, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 19, "loops": 0, "conditions": 0, "returns": 2, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 16, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireSha3", "type": "declaration", "parameters": 0, "statements": 185, "loops": 18, "conditions": 5, "returns": 5, "complexity": 85, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 178, "loops": 18, "conditions": 4, "returns": 3, "complexity": 82, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 173, "loops": 18, "conditions": 4, "returns": 3, "complexity": 78, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 170, "loops": 18, "conditions": 4, "returns": 2, "complexity": 77, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 36, "loops": 5, "conditions": 3, "returns": 0, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 1, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 6, "loops": 1, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 76, "loops": 9, "conditions": 1, "returns": 0, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 24, "loops": 1, "conditions": 0, "returns": 1, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 7, "loops": 1, "conditions": 0, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireRipemd160", "type": "declaration", "parameters": 0, "statements": 153, "loops": 3, "conditions": 9, "returns": 11, "complexity": 59, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 146, "loops": 3, "conditions": 8, "returns": 9, "complexity": 56, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 141, "loops": 3, "conditions": 8, "returns": 9, "complexity": 53, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 138, "loops": 3, "conditions": 8, "returns": 8, "complexity": 52, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 83, "loops": 2, "conditions": 8, "returns": 0, "complexity": 36, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 16, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "f1", "type": "declaration", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "f2", "type": "declaration", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "f3", "type": "declaration", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "f4", "type": "declaration", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "f5", "type": "declaration", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "rotl", "type": "declaration", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireHmac", "type": "declaration", "parameters": 0, "statements": 56, "loops": 1, "conditions": 3, "returns": 4, "complexity": 29, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 49, "loops": 1, "conditions": 2, "returns": 2, "complexity": 26, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 44, "loops": 1, "conditions": 2, "returns": 2, "complexity": 23, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 42, "loops": 1, "conditions": 2, "returns": 2, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 21, "loops": 1, "conditions": 2, "returns": 0, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requirePbkdf2", "type": "declaration", "parameters": 0, "statements": 57, "loops": 3, "conditions": 1, "returns": 5, "complexity": 32, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 50, "loops": 3, "conditions": 0, "returns": 3, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 45, "loops": 3, "conditions": 0, "returns": 3, "complexity": 24, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 42, "loops": 3, "conditions": 0, "returns": 2, "complexity": 23, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 28, "loops": 3, "conditions": 0, "returns": 1, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireEvpkdf", "type": "declaration", "parameters": 0, "statements": 50, "loops": 2, "conditions": 2, "returns": 5, "complexity": 31, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 43, "loops": 2, "conditions": 1, "returns": 3, "complexity": 28, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 38, "loops": 2, "conditions": 1, "returns": 3, "complexity": 23, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 35, "loops": 2, "conditions": 1, "returns": 2, "complexity": 22, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 22, "loops": 2, "conditions": 1, "returns": 1, "complexity": 17, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireCipherCore", "type": "declaration", "parameters": 0, "statements": 230, "loops": 2, "conditions": 13, "returns": 27, "complexity": 124, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 223, "loops": 2, "conditions": 12, "returns": 25, "complexity": 121, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 218, "loops": 2, "conditions": 12, "returns": 25, "complexity": 117, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 214, "loops": 2, "conditions": 11, "returns": 25, "complexity": 114, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 15, "loops": 0, "conditions": 1, "returns": 6, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "selectCipherStrategy", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 3, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 31, "loops": 1, "conditions": 1, "returns": 1, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 7, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "xorBlock", "type": "declaration", "parameters": 3, "statements": 12, "loops": 1, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 10, "loops": 1, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 18, "loops": 0, "conditions": 2, "returns": 0, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 11, "loops": 0, "conditions": 1, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 0, "conditions": 1, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 0, "conditions": 1, "returns": 1, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 6, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 6, "loops": 0, "conditions": 1, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 5, "statements": 12, "loops": 0, "conditions": 2, "returns": 1, "complexity": 12, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireModeCfb", "type": "declaration", "parameters": 0, "statements": 45, "loops": 1, "conditions": 2, "returns": 4, "complexity": 22, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 38, "loops": 1, "conditions": 1, "returns": 2, "complexity": 19, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 33, "loops": 1, "conditions": 1, "returns": 2, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 30, "loops": 1, "conditions": 1, "returns": 1, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 5, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 6, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "generateKeystreamAndEncrypt", "type": "declaration", "parameters": 4, "statements": 13, "loops": 1, "conditions": 1, "returns": 0, "complexity": 7, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireModeCtr", "type": "declaration", "parameters": 0, "statements": 35, "loops": 1, "conditions": 2, "returns": 4, "complexity": 18, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 28, "loops": 1, "conditions": 1, "returns": 2, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 23, "loops": 1, "conditions": 1, "returns": 2, "complexity": 11, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 20, "loops": 1, "conditions": 1, "returns": 1, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 15, "loops": 1, "conditions": 1, "returns": 0, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "require<PERSON>ode<PERSON>tr<PERSON><PERSON><PERSON>", "type": "declaration", "parameters": 0, "statements": 70, "loops": 1, "conditions": 7, "returns": 6, "complexity": 31, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 63, "loops": 1, "conditions": 6, "returns": 4, "complexity": 28, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 58, "loops": 1, "conditions": 6, "returns": 4, "complexity": 24, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 55, "loops": 1, "conditions": 6, "returns": 3, "complexity": 23, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "incWord", "type": "declaration", "parameters": 1, "statements": 28, "loops": 0, "conditions": 4, "returns": 1, "complexity": 8, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "incCounter", "type": "declaration", "parameters": 1, "statements": 5, "loops": 0, "conditions": 1, "returns": 1, "complexity": 4, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 15, "loops": 1, "conditions": 1, "returns": 0, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireModeOfb", "type": "declaration", "parameters": 0, "statements": 33, "loops": 1, "conditions": 2, "returns": 4, "complexity": 17, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 26, "loops": 1, "conditions": 1, "returns": 2, "complexity": 14, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 21, "loops": 1, "conditions": 1, "returns": 2, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 18, "loops": 1, "conditions": 1, "returns": 1, "complexity": 9, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 13, "loops": 1, "conditions": 1, "returns": 0, "complexity": 7, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireModeEcb", "type": "declaration", "parameters": 0, "statements": 24, "loops": 0, "conditions": 1, "returns": 4, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 17, "loops": 0, "conditions": 0, "returns": 2, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 12, "loops": 0, "conditions": 0, "returns": 2, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 9, "loops": 0, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requirePadAnsix923", "type": "declaration", "parameters": 0, "statements": 26, "loops": 0, "conditions": 1, "returns": 3, "complexity": 8, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 19, "loops": 0, "conditions": 0, "returns": 1, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 14, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 8, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requirePadIso10126", "type": "declaration", "parameters": 0, "statements": 22, "loops": 0, "conditions": 1, "returns": 3, "complexity": 11, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 15, "loops": 0, "conditions": 0, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 10, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requirePadIso97971", "type": "declaration", "parameters": 0, "statements": 21, "loops": 0, "conditions": 1, "returns": 3, "complexity": 11, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 14, "loops": 0, "conditions": 0, "returns": 1, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requirePadZeropadding", "type": "declaration", "parameters": 0, "statements": 28, "loops": 1, "conditions": 2, "returns": 3, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 21, "loops": 1, "conditions": 1, "returns": 1, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 16, "loops": 1, "conditions": 1, "returns": 1, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 9, "loops": 1, "conditions": 1, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requirePadNopadding", "type": "declaration", "parameters": 0, "statements": 17, "loops": 0, "conditions": 1, "returns": 3, "complexity": 7, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 10, "loops": 0, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 5, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireFormatHex", "type": "declaration", "parameters": 0, "statements": 28, "loops": 0, "conditions": 1, "returns": 5, "complexity": 11, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 21, "loops": 0, "conditions": 0, "returns": 3, "complexity": 8, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 16, "loops": 0, "conditions": 0, "returns": 3, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 13, "loops": 0, "conditions": 0, "returns": 2, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireAes", "type": "declaration", "parameters": 0, "statements": 146, "loops": 5, "conditions": 9, "returns": 4, "complexity": 47, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 139, "loops": 5, "conditions": 8, "returns": 2, "complexity": 44, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 134, "loops": 5, "conditions": 8, "returns": 2, "complexity": 37, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 131, "loops": 5, "conditions": 8, "returns": 1, "complexity": 36, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 36, "loops": 2, "conditions": 2, "returns": 0, "complexity": 10, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 41, "loops": 2, "conditions": 6, "returns": 1, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 8, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 8, "statements": 25, "loops": 1, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireTripledes", "type": "declaration", "parameters": 0, "statements": 119, "loops": 7, "conditions": 2, "returns": 3, "complexity": 67, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 112, "loops": 7, "conditions": 1, "returns": 1, "complexity": 64, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 107, "loops": 7, "conditions": 1, "returns": 1, "complexity": 57, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 104, "loops": 7, "conditions": 1, "returns": 0, "complexity": 56, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 26, "loops": 5, "conditions": 0, "returns": 0, "complexity": 15, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 29, "loops": 2, "conditions": 0, "returns": 0, "complexity": 16, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "exchangeLR", "type": "declaration", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "exchangeRL", "type": "declaration", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 12, "loops": 0, "conditions": 1, "returns": 0, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireRc4", "type": "declaration", "parameters": 0, "statements": 65, "loops": 4, "conditions": 1, "returns": 4, "complexity": 31, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 58, "loops": 4, "conditions": 0, "returns": 2, "complexity": 28, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 53, "loops": 4, "conditions": 0, "returns": 2, "complexity": 21, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 50, "loops": 4, "conditions": 0, "returns": 1, "complexity": 20, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 17, "loops": 2, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "generateKeystreamWord", "type": "declaration", "parameters": 0, "statements": 16, "loops": 1, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 5, "loops": 1, "conditions": 0, "returns": 0, "complexity": 5, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "requireRabbit", "type": "declaration", "parameters": 0, "statements": 103, "loops": 7, "conditions": 2, "returns": 3, "complexity": 39, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 96, "loops": 7, "conditions": 1, "returns": 1, "complexity": 36, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 91, "loops": 7, "conditions": 1, "returns": 1, "complexity": 29, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 88, "loops": 7, "conditions": 1, "returns": 0, "complexity": 28, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 35, "loops": 4, "conditions": 1, "returns": 0, "complexity": 16, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 11, "loops": 1, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "nextState", "type": "declaration", "parameters": 0, "statements": 31, "loops": 2, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireRabbitLegacy", "type": "declaration", "parameters": 0, "statements": 100, "loops": 6, "conditions": 2, "returns": 3, "complexity": 36, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 93, "loops": 6, "conditions": 1, "returns": 1, "complexity": 33, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 88, "loops": 6, "conditions": 1, "returns": 1, "complexity": 26, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 85, "loops": 6, "conditions": 1, "returns": 0, "complexity": 25, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 32, "loops": 3, "conditions": 1, "returns": 0, "complexity": 13, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 11, "loops": 1, "conditions": 0, "returns": 0, "complexity": 4, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "nextState", "type": "declaration", "parameters": 0, "statements": 31, "loops": 2, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "requireBlowfish", "type": "declaration", "parameters": 0, "statements": 124, "loops": 8, "conditions": 3, "returns": 8, "complexity": 48, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 117, "loops": 8, "conditions": 2, "returns": 6, "complexity": 45, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 6, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 112, "loops": 8, "conditions": 2, "returns": 6, "complexity": 38, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 109, "loops": 8, "conditions": 2, "returns": 5, "complexity": 37, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "F", "type": "declaration", "parameters": 2, "statements": 9, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "BlowFish_Encrypt", "type": "declaration", "parameters": 3, "statements": 17, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "BlowFish_Decrypt", "type": "declaration", "parameters": 3, "statements": 17, "loops": 1, "conditions": 0, "returns": 1, "complexity": 4, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "BlowFishInit", "type": "declaration", "parameters": 3, "statements": 35, "loops": 6, "conditions": 1, "returns": 1, "complexity": 22, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 8, "loops": 0, "conditions": 1, "returns": 1, "complexity": 3, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 37, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 36, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "encrypt", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "decrypt", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "encrypt", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "decrypt", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "encrypt", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "decrypt", "type": "expression", "parameters": 1, "statements": 7, "loops": 0, "conditions": 0, "returns": 1, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 15, "loops": 1, "conditions": 1, "returns": 0, "complexity": 32, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "_0x2ad4", "type": "declaration", "parameters": 0, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 1, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_0x136b", "type": "declaration", "parameters": 2, "statements": 34, "loops": 2, "conditions": 2, "returns": 3, "complexity": 20, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 30, "loops": 2, "conditions": 2, "returns": 2, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "_0x7210f5", "type": "declaration", "parameters": 1, "statements": 11, "loops": 2, "conditions": 0, "returns": 1, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "iAjax", "type": "declaration", "parameters": 3, "statements": 110, "loops": 1, "conditions": 8, "returns": 17, "complexity": 167, "isObfuscated": false, "suspiciousPatterns": ["base64_string", "infinite_loop"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_0x10d845", "type": "expression", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x3b843c", "type": "expression", "parameters": 0, "statements": 1, "loops": 0, "conditions": 0, "returns": 0, "complexity": 0, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 44, "loops": 1, "conditions": 7, "returns": 1, "complexity": 82, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "iTrack", "type": "declaration", "parameters": 1, "statements": 19, "loops": 0, "conditions": 0, "returns": 6, "complexity": 29, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "Encrypt", "type": "declaration", "parameters": 2, "statements": 15, "loops": 0, "conditions": 2, "returns": 5, "complexity": 18, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "iReport", "type": "declaration", "parameters": 1, "statements": 6, "loops": 0, "conditions": 0, "returns": 1, "complexity": 10, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_0x2fe2", "type": "declaration", "parameters": 2, "statements": 34, "loops": 2, "conditions": 2, "returns": 3, "complexity": 20, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 30, "loops": 2, "conditions": 2, "returns": 2, "complexity": 18, "isObfuscated": true, "suspiciousPatterns": ["base64_string"]}, {"name": "_0x5ac18b", "type": "declaration", "parameters": 1, "statements": 11, "loops": 2, "conditions": 0, "returns": 1, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": ["base64_string"]}, {"name": "_0x258f", "type": "declaration", "parameters": 0, "statements": 6, "loops": 0, "conditions": 0, "returns": 2, "complexity": 1, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 0, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 15, "loops": 1, "conditions": 1, "returns": 0, "complexity": 30, "isObfuscated": true, "suspiciousPatterns": ["infinite_loop"]}, {"name": "anonymous", "type": "expression", "parameters": 1, "statements": 88, "loops": 0, "conditions": 5, "returns": 10, "complexity": 130, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 0, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 3, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 4, "statements": 2, "loops": 0, "conditions": 0, "returns": 1, "complexity": 1, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_0x5d8656", "type": "expression", "parameters": 2, "statements": 4, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x256bc", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x24427e", "type": "expression", "parameters": 2, "statements": 10, "loops": 0, "conditions": 1, "returns": 0, "complexity": 13, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x512aa0", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 3, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x1c3601", "type": "expression", "parameters": 2, "statements": 11, "loops": 0, "conditions": 1, "returns": 1, "complexity": 11, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "anonymous", "type": "expression", "parameters": 2, "statements": 3, "loops": 0, "conditions": 0, "returns": 1, "complexity": 2, "isObfuscated": true, "suspiciousPatterns": []}, {"name": "_0x446500", "type": "expression", "parameters": 3, "statements": 7, "loops": 0, "conditions": 1, "returns": 0, "complexity": 10, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x447eb6", "type": "expression", "parameters": 2, "statements": 13, "loops": 0, "conditions": 1, "returns": 0, "complexity": 14, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "_0x4376ee", "type": "expression", "parameters": 3, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}, {"name": "privateFunSent", "type": "declaration", "parameters": 1, "statements": 3, "loops": 0, "conditions": 0, "returns": 0, "complexity": 2, "isObfuscated": false, "suspiciousPatterns": []}], "variables": [{"name": "t", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "o", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "r", "type": "number", "isObfuscated": true, "value": 1}, {"name": "i", "type": "number", "isObfuscated": true, "value": 0}, {"name": "n", "type": "number", "isObfuscated": true, "value": 0}, {"name": "e", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "commonjsGlobal", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "f", "type": "number", "isObfuscated": true, "value": 0}, {"name": "a", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "d", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "jsencrypt", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "BI_RM", "type": "string", "isObfuscated": false, "value": "0123456789abcdefghijklmnopqrstuvwxyz"}, {"name": "b64map", "type": "string", "isObfuscated": false, "value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"}, {"name": "b64pad", "type": "string", "isObfuscated": false, "value": "="}, {"name": "c", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ret", "type": "string", "isObfuscated": false, "value": ""}, {"name": "k", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "slop", "type": "number", "isObfuscated": false, "value": 0}, {"name": "v", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "p", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "decoder", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Hex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hex", "type": "string", "isObfuscated": false, "value": "0123456789ABCDEF"}, {"name": "ignore", "type": "string", "isObfuscated": false, "value": "= \f\n\r\t   "}, {"name": "out", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "bits", "type": "number", "isObfuscated": false, "value": 0}, {"name": "char_count", "type": "number", "isObfuscated": false, "value": 0}, {"name": "decoder$1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Base64", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "b64", "type": "string", "isObfuscated": false, "value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"}, {"name": "m", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "max", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Int10", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "b", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "l", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "s", "type": "string", "isObfuscated": true, "value": ""}, {"name": "ellipsis", "type": "string", "isObfuscated": false, "value": "…"}, {"name": "reTimeS", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "reTimeL", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Stream", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "str", "type": "string", "isObfuscated": false, "value": ""}, {"name": "hi", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "lo", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "neg", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "pad", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "len", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "unusedBit", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "lenBit", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "intro", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "skip", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "j", "type": "number", "isObfuscated": true, "value": 0}, {"name": "ASN1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "content", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "buf", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hexString", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "offset", "type": "number", "isObfuscated": false, "value": 0}, {"name": "length", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "stream", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "streamStart", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "tag", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "start", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "header", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sub", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "end", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ASN1Tag", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "dbits", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "lowprimes", "type": "array", "isObfuscated": false, "value": "[168 elements]"}, {"name": "lplim", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "BigInteger", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "km", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "z", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "x", "type": "number", "isObfuscated": true, "value": 0}, {"name": "q", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "g", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "k1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "g2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "w", "type": "number", "isObfuscated": true, "value": 0}, {"name": "is1", "type": "boolean", "isObfuscated": false, "value": true}, {"name": "r2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ac", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "u", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "y", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "mi", "type": "boolean", "isObfuscated": true, "value": false}, {"name": "sh", "type": "number", "isObfuscated": true, "value": 0}, {"name": "bs", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "cbs", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bm", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ds", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "pm", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "pt", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ts", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ms", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "nsh", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ys", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "y0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "yt", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "d1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "d2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "qd", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "cs", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "n1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "bnp_1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "NullExp", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Classic", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "u0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "<PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "xl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "xh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "h", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "BI_FP", "type": "number", "isObfuscated": false, "value": 52}, {"name": "BI_RC", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "rr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "vv", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "Arcfour", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rng_psize", "type": "number", "isObfuscated": false, "value": 256}, {"name": "rng_state", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rng_pool", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rng_pptr", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "mouseCoordinates", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "random", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SecureRandom", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "filler", "type": "string", "isObfuscated": false, "value": ""}, {"name": "ba", "type": "array", "isObfuscated": true, "value": "[0 elements]"}, {"name": "rng", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "RSAKey", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "xp", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "xq", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_this", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "max<PERSON><PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ct_1", "type": "string", "isObfuscated": false, "value": ""}, {"name": "lt", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "t1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ct_2", "type": "string", "isObfuscated": false, "value": ""}, {"name": "qs", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ee", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "p1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "q1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "phi", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rsa", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "digest", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "unpadded", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "DIGEST_HEADERS", "type": "object", "isObfuscated": false, "value": "{8 properties}"}, {"name": "name_1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "YAHOO", "type": "object", "isObfuscated": false, "value": "{0 properties}"}, {"name": "ADD", "type": "array", "isObfuscated": false, "value": "[2 elements]"}, {"name": "fname", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "KJUR", "type": "object", "isObfuscated": false, "value": "{0 properties}"}, {"name": "hPos", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "xorLen", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hMask", "type": "string", "isObfuscated": false, "value": ""}, {"name": "biMask", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "biNeg", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_KJUR", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_KJUR_asn1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERBoolean", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERInteger", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERBitString", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DEROctetString", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERNull", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERObjectIdentifier", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DEREnumerated", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERUTF8String", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERNumericString", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERPrintableString", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERTeletexString", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERIA5String", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERUTCTime", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERGeneralizedTime", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERSequence", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERSet", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_DERTaggedObject", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_newObject", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keys", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "key", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "paramList", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "asn1Obj", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "tagParam", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "obj", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "newParam", "type": "object", "isObfuscated": false, "value": "{0 properties}"}, {"name": "i01", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "i0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "i1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "bin<PERSON><PERSON>", "type": "string", "isObfuscated": false, "value": ""}, {"name": "value", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bin", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bi", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "padLen", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bPad", "type": "string", "isObfuscated": false, "value": ""}, {"name": "b8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "hV", "type": "string", "isObfuscated": true, "value": ""}, {"name": "hN", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "h<PERSON>len", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "head", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "utcDate", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "year", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "month", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "day", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hour", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "min", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sec", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "millis", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "dateObject", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hUnusedBits", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "unusedBits", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "oid", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "JSEncryptRSAKey", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "modulus", "type": "number", "isObfuscated": false, "value": 0}, {"name": "public_exponent", "type": "number", "isObfuscated": false, "value": 0}, {"name": "reHex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "der", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "asn1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "private_exponent", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "prime1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "prime2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "exponent1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "exponent2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "coefficient", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bit_string", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sequence", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "options", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "seq", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "first_sequence", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "second_sequence", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "regex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "JSEncrypt", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "encrypted", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "uncrypted", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "count", "type": "number", "isObfuscated": false, "value": 0}, {"name": "reg", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "jsencryptExports", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "url<PERSON>l<PERSON><PERSON>", "type": "string", "isObfuscated": false, "value": "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"}, {"name": "nanoid", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "id", "type": "string", "isObfuscated": true, "value": ""}, {"name": "bytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "cryptoJs", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "core", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "_nodeResolve_empty", "type": "object", "isObfuscated": false, "value": "{0 properties}"}, {"name": "_nodeResolve_empty$1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "require$$0", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hasRequiredCore", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "CryptoJS", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "crypto", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "create", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "subtype", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "C_lib", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Base", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "instance", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "propertyName", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "WordArray", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "thisWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "thatWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "thisSigBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "thatSigBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "thatByte", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "words", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "sigBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "clone", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_enc", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hexChars", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "bite", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hexStr<PERSON>ength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Latin1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "latin1Chars", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "latin1StrLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Utf8", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "BufferedBlockAlgorithm", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "processedWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "data", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "dataWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "dataSigBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockSize", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockSizeBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBlocksReady", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nWordsReady", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBytesReady", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hash", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_algo", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "x64Core", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredX64Core", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "X32WordArray", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_x64", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "x64Words", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "x64WordsLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "x32Words", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "x64Word", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "wordsLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "libTypedarrays", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredLibTypedarrays", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "superInit", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "subInit", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "typedArrayByteLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "encUtf16", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredEncUtf16", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "utf16Chars", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "codePoint", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "utf16StrLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "encBase64", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredEncBase64", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "map", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "base64Chars", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "byte1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "byte2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "byte3", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "triplet", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "paddingChar", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "base64StrLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "reverseMap", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "paddingIndex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBytes", "type": "number", "isObfuscated": false, "value": 0}, {"name": "bits1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bits2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bitsCombined", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "encBase64url", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredEncBase64url", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "md5", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredMd5", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "T", "type": "array", "isObfuscated": true, "value": "[0 elements]"}, {"name": "MD5", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "offset_i", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_i", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "M_offset_0", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_3", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_4", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_5", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_6", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_7", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_8", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_9", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_10", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_11", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_12", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_13", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_14", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M_offset_15", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBitsTotal", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBitsLeft", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBitsTotalH", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBitsTotalL", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H_i", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sha1", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredSha1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "W", "type": "array", "isObfuscated": true, "value": "[0 elements]"}, {"name": "SHA1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sha256", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredSha256", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "K", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "sqrtN", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "factor", "type": "number", "isObfuscated": false, "value": 2}, {"name": "nPrime", "type": "number", "isObfuscated": false, "value": 0}, {"name": "SHA256", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma0x", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma0", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma1x", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ch", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "maj", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sigma0", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sigma1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "t2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "sha224", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredSha224", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SHA224", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sha512", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredSha512", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "X64Word", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "X64WordArray", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SHA512", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H3", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H4", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H5", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H6", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H7", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "H0h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H0l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H1h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H1l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H2h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H2l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H3h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H3l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H4h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H4l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H5h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H5l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H6h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H6l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H7h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "H7l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ah", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "al", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "bh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "bl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "cl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "dh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "dl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "eh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "el", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "fh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "fl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "gh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "gl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "hh", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "hl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "Wil", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "gamma0xh", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma0xl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma0h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma0l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma1xh", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma1xl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma1h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "gamma1l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi7", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi7h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi7l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi16", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi16h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Wi16l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "chh", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "chl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "majh", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "majl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sigma0h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sigma0l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sigma1h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sigma1l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON>", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "<PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "t1l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "t1h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "t2l", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "t2h", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sha384", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredSha384", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SHA384", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "sha3", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredSha3", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "RHO_OFFSETS", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "PI_INDEXES", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "ROUND_CONSTANTS", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "newX", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "newY", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "LFSR", "type": "number", "isObfuscated": false, "value": 1}, {"name": "roundConstantMsw", "type": "number", "isObfuscated": false, "value": 0}, {"name": "roundConstantLsw", "type": "number", "isObfuscated": false, "value": 0}, {"name": "bitPosition", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SHA3", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "state", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nBlockSizeLanes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M2i", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "M2i1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "lane", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "round", "type": "number", "isObfuscated": false, "value": 0}, {"name": "tMsw", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "tLsw", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Tx", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "Tx4", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Tx1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Tx1Msw", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Tx1Lsw", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "laneIndex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "laneMsw", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "laneLsw", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rhoOffset", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "TPiLane", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "T0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "state0", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "TLane", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Tx1Lane", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Tx2Lane", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "roundConstant", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockSizeBits", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "outputLengthBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "outputLengthLanes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hashWords", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "ripemd160", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredRipemd160", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_zl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_zr", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_sl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_sr", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_hl", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_hr", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "RIPEMD160", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "zl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "zr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "sl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "sr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ar", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "br", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "cr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "dr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "er", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "hmac", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hasRequiredHmac", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hasherBlockSize", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hasherBlockSizeBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "o<PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "i<PERSON>ey", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "oKeyWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "iKeyWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "hasher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "innerHash", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "pbkdf2", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredPbkdf2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "HMAC", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "PBKDF2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "cfg", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "<PERSON><PERSON><PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockIndex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "derived<PERSON>eyWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockIndexWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keySize", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "iterations", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "block", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blockWordsLength", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "intermediate", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "intermediateWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "evpkdf", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredEvpkdf", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "EvpKDF", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "cipherCore", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredCipherCore", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Cipher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "finalProcessedData", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "finalProcessedBlocks", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_mode", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "BlockCipherMode", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "CBC", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "cipher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "thisBlock", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "iv", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "C_pad", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Pkcs7", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nPaddingBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "paddingWord", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "paddingWords", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "padding", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "modeCreator", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "mode", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "CipherParams", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_format", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "OpenSSLFormatter", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "wordArray", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ciphertext", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "salt", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ciphertextWords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SerializableCipher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "encryptor", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "cipherCfg", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "plaintext", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_kdf", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "OpenSSLKdf", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "PasswordBasedCipher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "derivedParams", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "modeCfb", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredModeCfb", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "CFB", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keystream", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "modeCtr", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredModeCtr", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "CTR", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Encryptor", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "counter", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "modeCtr<PERSON><PERSON><PERSON>", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredModeCtrGladman", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "CTRGladman", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "b1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "b2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "b3", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "modeOfb", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredModeOfb", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "OFB", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "modeEcb", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredModeEcb", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ECB", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "padAnsix923", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredPadAnsix923", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "lastBytePos", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "padIso10126", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredPadIso10126", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "padIso97971", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredPadIso97971", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "padZeropadding", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredPadZeropadding", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "padNopadding", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredPadNopadding", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "formatHex", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredFormatHex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "aes", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredAes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "BlockCipher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "SBOX", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "INV_SBOX", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "SUB_MIX_0", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "SUB_MIX_1", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "SUB_MIX_2", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "SUB_MIX_3", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "INV_SUB_MIX_0", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "INV_SUB_MIX_1", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "INV_SUB_MIX_2", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "INV_SUB_MIX_3", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "xi", "type": "number", "isObfuscated": true, "value": 0}, {"name": "sx", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "x2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "x4", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "x8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "RCON", "type": "array", "isObfuscated": false, "value": "[11 elements]"}, {"name": "AES", "type": "object", "isObfuscated": false, "value": "{6 properties}"}, {"name": "key<PERSON>ords", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nRounds", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ksRows", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keySchedule", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "ksRow", "type": "number", "isObfuscated": false, "value": 4}, {"name": "invKeySchedule", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "invKsRow", "type": "number", "isObfuscated": false, "value": 0}, {"name": "s0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "s1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "s2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "s3", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "t0", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "t3", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "tripledes", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredTripledes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "PC1", "type": "array", "isObfuscated": false, "value": "[56 elements]"}, {"name": "PC2", "type": "array", "isObfuscated": false, "value": "[48 elements]"}, {"name": "BIT_SHIFTS", "type": "array", "isObfuscated": false, "value": "[16 elements]"}, {"name": "SBOX_P", "type": "array", "isObfuscated": false, "value": "[8 elements]"}, {"name": "SBOX_MASK", "type": "array", "isObfuscated": false, "value": "[8 elements]"}, {"name": "DES", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keyBits", "type": "array", "isObfuscated": false, "value": "[0 elements]"}, {"name": "keyBitPos", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "subKeys", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "nSubKey", "type": "number", "isObfuscated": false, "value": 0}, {"name": "subKey", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "bitShift", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "invSubKeys", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "lBlock", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rB<PERSON>", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "TripleDES", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "key1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "key2", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "key3", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rc4", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredRc4", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "StreamCipher", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "RC4", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keySigBytes", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "S", "type": "array", "isObfuscated": true, "value": "[0 elements]"}, {"name": "keyByteIndex", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keyByte", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "keystreamWord", "type": "number", "isObfuscated": false, "value": 0}, {"name": "RC4Drop", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "rabbit", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredRabbit", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "C_", "type": "array", "isObfuscated": true, "value": "[0 elements]"}, {"name": "G", "type": "array", "isObfuscated": true, "value": "[0 elements]"}, {"name": "Rabbit", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "X", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "IV", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "IV_0", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "IV_1", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "i2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "i3", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "gx", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "ga", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "gb", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "rabbitLegacy", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredRabbitLegacy", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "RabbitLegacy", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "blowfish", "type": "object", "isObfuscated": false, "value": "{1 properties}"}, {"name": "hasRequiredBlowfish", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "N", "type": "number", "isObfuscated": true, "value": 16}, {"name": "ORIG_P", "type": "array", "isObfuscated": false, "value": "[18 elements]"}, {"name": "ORIG_S", "type": "array", "isObfuscated": false, "value": "[4 elements]"}, {"name": "BLOWFISH_CTX", "type": "object", "isObfuscated": false, "value": "{2 properties}"}, {"name": "Xl", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "Xr", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "temp", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Row", "type": "number", "isObfuscated": false, "value": 0}, {"name": "Col", "type": "number", "isObfuscated": false, "value": 0}, {"name": "keyIndex", "type": "number", "isObfuscated": false, "value": 0}, {"name": "index", "type": "number", "isObfuscated": false, "value": 0}, {"name": "Data1", "type": "number", "isObfuscated": false, "value": 0}, {"name": "Data2", "type": "number", "isObfuscated": false, "value": 0}, {"name": "res", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "Blowfish", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "cryptoJsExports", "type": "unknown", "isObfuscated": false, "value": null}, {"name": "_0x5d95f8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x3687f1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2ef923", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x75a5b8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x63ba93", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x13f530", "type": "object", "isObfuscated": true, "value": "{3 properties}"}, {"name": "_0x22066b", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x296520", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4805c8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x12ecb8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x262ff8", "type": "object", "isObfuscated": true, "value": "{3 properties}"}, {"name": "AESEX", "type": "object", "isObfuscated": false, "value": "{6 properties}"}, {"name": "_0x5db0d6", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2cf115", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x490564", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5584b2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5a6106", "type": "object", "isObfuscated": true, "value": "{3 properties}"}, {"name": "_0x36d0b4", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2e6ee2", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5a1591", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5b7e25", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x49d38b", "type": "object", "isObfuscated": true, "value": "{3 properties}"}, {"name": "AES_SIGN", "type": "object", "isObfuscated": false, "value": "{6 properties}"}, {"name": "_0x2e9eed", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1c9186", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x9b2f45", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0xad1b80", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x51d963", "type": "object", "isObfuscated": true, "value": "{3 properties}"}, {"name": "_0xac07cf", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x3058d9", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0xc7a8e3", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x498562", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x242959", "type": "object", "isObfuscated": true, "value": "{3 properties}"}, {"name": "_0x3ad622", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4a189c", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2f477e", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5bb09a", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_excluded", "type": "array", "isObfuscated": false, "value": "[1 elements]"}, {"name": "_0x35cd94", "type": "array", "isObfuscated": true, "value": "[97 elements]"}, {"name": "_0x2ad43b", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4e411c", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1a936d", "type": "string", "isObfuscated": true, "value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/="}, {"name": "_0x315cc1", "type": "string", "isObfuscated": true, "value": ""}, {"name": "_0x4cb634", "type": "string", "isObfuscated": true, "value": ""}, {"name": "_0x48187", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x29262d", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5e0cdd", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5a1f2b", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x3b90e0", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x333365", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4e98de", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x53311e", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x16bb6b", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x337b74", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x20f141", "type": "object", "isObfuscated": true, "value": "{29 properties}"}, {"name": "_0x2ebbf9", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x321806", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x554135", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1a825f", "type": "object", "isObfuscated": true, "value": "{8 properties}"}, {"name": "_0x591845", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x27230f", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x36c367", "type": "string", "isObfuscated": true, "value": ""}, {"name": "_0x18f7d4", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0xa8d53a", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5b4c04", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x595c94", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2d754d", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1cdcf1", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x46b734", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1f900f", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5dc133", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x328c48", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5d3883", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x176110", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4375da", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x19fd37", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2e029f", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1b0900", "type": "object", "isObfuscated": true, "value": "{2 properties}"}, {"name": "_0x2b33aa", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x242c07", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x50fea6", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x3bdc03", "type": "object", "isObfuscated": true, "value": "{11 properties}"}, {"name": "_0xf3e966", "type": "object", "isObfuscated": true, "value": "{4 properties}"}, {"name": "_0x397065", "type": "object", "isObfuscated": true, "value": "{0 properties}"}, {"name": "_0x46b455", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1aa55c", "type": "object", "isObfuscated": true, "value": "{2 properties}"}, {"name": "_0xb1c439", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4616c7", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1ebca8", "type": "object", "isObfuscated": true, "value": "{4 properties}"}, {"name": "_0x258fa1", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x4f512d", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x31f1ad", "type": "string", "isObfuscated": true, "value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/="}, {"name": "_0x38c245", "type": "string", "isObfuscated": true, "value": ""}, {"name": "_0x554e72", "type": "string", "isObfuscated": true, "value": ""}, {"name": "_0x33be71", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x32ce32", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5f0aed", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x67cbc", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x4fb602", "type": "number", "isObfuscated": true, "value": 0}, {"name": "_0x1eb1fe", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x56e2ef", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0xce9473", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x40f0e4", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1ce766", "type": "array", "isObfuscated": true, "value": "[67 elements]"}, {"name": "_0xa309fd", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x2619a9", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x591dc7", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x42c3b8", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x292549", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x5974b0", "type": "object", "isObfuscated": true, "value": "{31 properties}"}, {"name": "_0x590c83", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x54c237", "type": "object", "isObfuscated": true, "value": "{2 properties}"}, {"name": "_0x410eca", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x21fdc7", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x1f42f6", "type": "object", "isObfuscated": true, "value": "{0 properties}"}, {"name": "_0x2b02be", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x293233", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x57deae", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x151fdc", "type": "object", "isObfuscated": true, "value": "{2 properties}"}, {"name": "_0x4c1b64", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0xe0b91d", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x35ce67", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x8fa31e", "type": "object", "isObfuscated": true, "value": "{0 properties}"}, {"name": "_0x2cb79a", "type": "object", "isObfuscated": true, "value": "{0 properties}"}, {"name": "_0x2a100d", "type": "unknown", "isObfuscated": true, "value": null}, {"name": "_0x291f4d", "type": "unknown", "isObfuscated": true, "value": null}], "strings": [{"value": "object", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "object", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "@@toPrimitive must return a primitive value.", "length": 44, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "symbol", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "default", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "function", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "__esModule", "length": 10, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0123456789abcdefghijklmnopqrstuvwxyz", "length": 36, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", "length": 64, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "=", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0123456789ABCDEF", "length": 16, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": " \f\n\r\t   ", "length": 8, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "=", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Illegal character at offset ", "length": 28, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Hex encoding incomplete: 4 bits missing", "length": 39, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", "length": 64, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "= \f\n\r\t   ", "length": 9, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "=", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Illegal character at offset ", "length": 28, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Base64 encoding incomplete: at least 2 bits missing", "length": 51, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "RegExp out of sync", "length": 18, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "only base 10 is supported", "length": 25, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "…", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0123456789ABCDEF", "length": 16, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "Requesting byte offset ", "length": 23, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " on a stream of length ", "length": 23, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "  ", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "\n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " ", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Unrecognized time: ", "length": 19, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " ", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ":", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ":", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " UTC", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Z", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ":", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " bit)\n", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " bit)\n", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " byte)\n", "length": 7, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "2.", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".incomplete", "length": 11, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Invalid tag value.", "length": 18, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EOC", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BOOLEAN", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "INTEGER", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BIT_STRING", "length": 10, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "OCTET_STRING", "length": 12, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "NULL", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "OBJECT_IDENTIFIER", "length": 17, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ObjectDescriptor", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "EXTERNAL", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "REAL", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ENUMERATED", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EMBEDDED_PDV", "length": 12, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "UTF8String", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "SEQUENCE", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "SET", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "NumericString", "length": 13, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "PrintableString", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "TeletexString", "length": 13, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "VideotexString", "length": 14, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "IA5String", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "UTCTime", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "GeneralizedTime", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "GraphicString", "length": 13, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "VisibleString", "length": 13, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "GeneralString", "length": 13, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "UniversalString", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BMPString", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Universal_", "length": 10, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Application_", "length": 12, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "[", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "]", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Private_", "length": 8, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " elem)", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "false", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "true", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " elem)", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " elem)", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " elem)", "length": 6, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(no elem)", "length": 9, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "@", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "[header:", "length": 8, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ",length:", "length": 8, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ",sub:", "length": 5, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "null", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "]", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " @", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "+", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " (constructed)", "length": 14, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": " (encapsulates)", "length": 15, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "\n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "  ", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Length over 48 bits not supported at position ", "length": 46, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Content size is not correct for container starting at offset ", "length": 61, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "Exception while decoding undefined length content: ", "length": 51, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "BIT STRINGs with unused bits cannot encapsulate.", "length": 48, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EOC is not supposed to be actual content.", "length": 41, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "We can't skip over an invalid tag with undefined length at offset ", "length": 66, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "number", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "number", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "number", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Microsoft Internet Explorer", "length": 27, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Netscape", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "a", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "A", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mousemove", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mousemove", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Message too long for RSA", "length": 24, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ff", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0001", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Message too long for RSA", "length": 24, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Invalid RSA public key", "length": 22, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Invalid RSA private key", "length": 23, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Invalid RSA private key", "length": 23, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "3020300c06082a864886f70d020205000410", "length": 36, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "3020300c06082a864886f70d020505000410", "length": 36, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "3021300906052b0e03021a05000414", "length": 30, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "302d300d06096086480165030402040500041c", "length": 38, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "3031300d060960864801650304020105000420", "length": 38, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "3041300d060960864801650304020205000430", "length": 38, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "3051300d060960864801650304020305000440", "length": 38, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "3021300906052b2403020105000414", "length": 30, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "YAHOO.lang.extend failed, please check that all dependencies are included.", "length": 74, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "toString", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "valueOf", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "function", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "f", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "key of param shall be only one.", "length": 31, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:ta...", "length": 102, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": ":", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ":", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined key: ", "length": 15, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "bool", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "int", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "bitstr", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "octstr", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "null", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "oid", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "enum", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "utf8str", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "numstr", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "prnstr", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "telstr", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ia5str", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "utctime", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "gentime", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "seq", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "set", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "tag", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "[object Array]", "length": 14, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "obj shall be specified for 'tag'.", "length": 33, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00000000", "length": 8, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "malformed oid string: ", "length": 22, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "this.hV is null or undefined.", "length": 29, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "value hex must be even length: n=", "length": 33, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ",v=", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ASN.1 length too long to represent by 8x: n = ", "length": 46, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "utc", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Z", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "01", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0101ff", "length": 6, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "02", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "number", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "03", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "unused bits shall be from 0 to 7: u = ", "length": 38, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "04", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "05", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0500", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "06", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "malformed oid string: ", "length": 22, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": ".", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "DERObjectIdentifier oidName undefined: ", "length": 39, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0a", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "number", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0c", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "12", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "13", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "14", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "16", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "17", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "utc", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "utc", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "18", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "gen", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "gen", "length": 3, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "30", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "31", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "a0", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1.2.840.113549.1.1.1", "length": 20, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "(.{1,", "length": 5, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "})( +|$\n?)|(.{1,", "length": 16, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "})", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "g", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "\n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-----BEGIN RSA PRIVATE KEY-----\n", "length": 32, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "\n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-----END RSA PRIVATE KEY-----", "length": 29, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-----BEGIN PUBLIC KEY-----\n", "length": 27, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "\n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "-----END PUBLIC KEY-----", "length": 24, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "e", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "n", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "e", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "d", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "p", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "q", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "dmp1", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "dmq1", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "coeff", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "d", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "010001", "length": 6, "isBase64": true, "isHex": true, "hasUnicode": false, "isSuspicious": false}, {"value": "A key was already set, overriding existing.", "length": 43, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "[object Function]", "length": 17, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "3.1.4", "length": 5, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "__esModule", "length": 10, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict", "length": 64, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Could not dynamically require \"", "length": 31, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "\". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-...", "length": 153, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "function", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "function", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "function", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "Native crypto module could not be used to get secure random number.", "length": 67, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "init", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "toString", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Malformed UTF-8 data", "length": 20, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "function", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "undefined", "length": 9, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", "length": 65, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=", "length": 65, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_", "length": 64, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "string", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.", "length": 77, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "A36pXGz0A34Gjp9Z", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1koPZ0pOJ7rn0rB8", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1koPZ0pOJ7rn0rB8", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "A36pXGz0A34Gjp9Z", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "JkoPpX34GAz0Z36j", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "wpO9ZG0Jrn0rB87p", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "23k4fKaj9Pk5kA6J", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "u4J0gN7Wavx2X25S", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "vJyUmc4Y", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "z3bSs3i", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "qM1mDee", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "B25fCNjVCG", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l2fWAs9YzxbVCNq", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ue9tva", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "yKrZsMO", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "yM5UAvK", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mtqYodu0mNnus1biDa", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Dwzvr2O", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ELfvueC", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Dhj1zq", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ywn0Aw9UvMfSDwu", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "qNDfCMm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "DKrSAM4", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ug5QzMG", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mty5nZeWneHqAxbzvq", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "B01gsfe", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "u0PAAvG", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "uxnwtg8", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "u0Hwrfu", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "q0X4zLe", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "DxjS", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2v0uhjPDMf0zuTLEq", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "B25YzwfKExn0yxrLy2HHBMDL", "length": 24, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C3rHDhvZ", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y2fSBa", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "AhLnCxm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "A0Ljug0", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C3bSAxq", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "tuLjq2rNsujbrefoqMDRCwHRAuC5DZbcqvffrKfbu0nbBuf3z2Dky0fNrufbB0DcquTyouHRofP1BwC0mhfTCeDJCZzwz2ryExnI...", "length": 1131, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "B25tDwnJzxnZ", "length": 12, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y0TMqxC", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EePJA3u", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ntaWma", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C3rYAw5NAwz5", "length": 12, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvZCg9UC2vuzxH0", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mxLft0rlAa", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "nJG0mdKYn1fYr1zvyq", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "v1frEfq", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "n2Loy0zdyq", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2LK", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mZy4mZG1wvfovvDN", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvHzhLtDgf0zq", "length": 14, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMfUzg9T", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C3HJBwG", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "tMfoCMO", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ywn0Aw9UtMfTzq", "length": 14, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zMXVB3i", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2v0uMvXDwvZDeHLywrLCG", "length": 22, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "tuLhzK1bmeDdu3fhu0LIm0rrrujbuvvbqtrhtKfeq0jPuuTcz1fesLmXt21Nu3noqZH0kZzWB09ZuwjsyMLcsvGXyLzht1L4ng9I...", "length": 288, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "CNvZu24", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "q05Wqw0", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "sNLmrK0", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zw5JCNLWDa", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "z2v0vgLTzq", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "suLjy1y", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "nZeYmde5me1hyuXntW", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "AgvHzgvYCW", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y29Kzq", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "svPgvwK", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C0DzD0C", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvMzxjYzxi", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mNWZFdf8mhW0", "length": 12, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CLrms00", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "sdvWCM90ywZMLk/KU5G", "length": 19, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mtKWndaZnfrIz1HfAa", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zgvJCNLWDeXVBMC", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BKLSBK8", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Bwv0Ag9K", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "D2L0AenYzwrLBNrPywXZ", "length": 20, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "AMzREhu", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "uuHNCNK", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "m2nmuK1Uva", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l2fWAs9QC3nKAY9Hy3rPB25uCMfJAW", "length": 30, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mtbZuePxy0S", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zgf0yq", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Ae1rENa", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CgfYC2u", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "uK1sv04", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "uLnKtLm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l3nKA19HCgKVBwv0Ag9K", "length": 20, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "A25dr3y", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C3rHDhvZvgv4Da", "length": 14, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Ehnctw4", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "r0vu", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Ew1Xv2u", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Ahr0Chm6lY9TlMLTDxnPyY5JBI9WyxLJzw50zxjHCgK", "length": 43, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EK1rrNa", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "yxn5BMm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BvrOBKu", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "D0Hsru4", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mJe3odm5mMfzv3rXAG", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y29Uy2f0", "length": 8, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2v0uhvIBgLJs2v5", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y0Pvv3e", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y1j1rLm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=", "length": 65, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "%", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "RSdNS", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKX9Hk8Zumg40qmpGcs6VgdXysbx1b+5l+gDs0DETIu562g1gYD4...", "length": 848, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "application/json; charset=utf-8", "length": 31, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "&", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Access-Control-Allow-Origin", "length": 27, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "*", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Access-Control-Allow-Credentials", "length": 32, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Content-Type", "length": 12, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "=", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "&", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "?", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "3001", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "|", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "1", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "2", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "3", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "4", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "4", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "POST", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "0", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "4", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "getBury", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=", "length": 65, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": true}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "", "length": 0, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "%", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "00", "length": 2, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "sw5lDMm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zMTArei", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXqMfJA2zPBgXwzxjPzNLdB2rLuMvZ", "length": 32, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXsw5PDa", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2vzC3u", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "sKzZqve", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "y1nPs3e", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l2fWAs9QC3nKAY9Zzw5KvMvYAwz5q29Kzq", "length": 34, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "rejOvhq", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "r3DUEfu", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "DenbruG", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mtKYmZbrD3LMuLO", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXu2vUzfzLCMLMEunVzgu", "length": 23, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Cg9ZDe1LC3nHz2u", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "vvLPrMO", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "Ahvvte0", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "nduZodHAA1PPBeG", "length": 15, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EKPfD2y", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "AwDLvKO", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "A1n6r1C", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "s29wC1O", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "D3PQA1q", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "qNrpvuy", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mZy4mdyYwxrVuLHj", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BuTguwC", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "re<PERSON><PERSON><PERSON><PERSON>", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "s2zKu0S", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXt3bLBLbVCNrHBfjLCW", "length": 22, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l2fWAs9QC3nKAY9VCMrLCKXHDw5JAgvK", "length": 32, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "BwvZC2fNzq", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXuMvWB3j0", "length": 12, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zgf0yq", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ue9tva", "length": 6, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zuHuB2e", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2rRx2TLEq", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "nMHotvLruW", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "sgzKz2e", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ze5pquq", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zxjYB3i", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l2fWAs9QC3nKAY9IywnRzMLSBfzLCMLMEunVzgu", "length": 39, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "D2HzDgi", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ywrKrxzLBNrmAxn0zw5LCG", "length": 22, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "u0LMt0W", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "svvOAMG", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "EKz0z3O", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mtG2nta1nMHSDgfwrG", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "r014weC", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "qLDUswu", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "nZzHBhn4yuy", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "DKPqB2C", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "C2LK", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "odu2nZy0z0LsyNvb", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "wxHTCey", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "nJu4odCYwLLuqK5Y", "length": 16, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l3nKA19HCgKVBwv0Ag9K", "length": 20, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "q0HAq2O", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "tNLJs24", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXu2vUzfzLCMLMEunVzgvszxm", "length": 27, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXqMfJA2zPBgXwzxjPzNLdB2rL", "length": 28, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "v3nPtuS", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "sMjvDeO", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "mtH1zK5HCLa", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "zNvUtMfTzq", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "ntqWndC2mhbcyKHOvW", "length": 18, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "q0XSuLm", "length": 7, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "CMvXvhjHy2S", "length": 11, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "l2fWAs9QC3nKAY9JAgvJA1nKA0TLEq", "length": 30, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "请求失败:", "length": 5, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "HIQSU", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "TArzL", "length": 5, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "/sdk_api/method", "length": 15, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "reqInitRes", "length": 10, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "reqOpenPortal", "length": 13, "isBase64": true, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "POST", "length": 4, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}, {"value": "*", "length": 1, "isBase64": false, "isHex": false, "hasUnicode": false, "isSuspicious": false}], "patterns": {"controlFlowFlattening": 9, "stringObfuscation": 32, "deadCodeInjection": 0, "variableRenaming": 4064, "propertyMangling": 0}, "complexity": {"cyclomatic": 922, "maxNesting": 7, "maintainabilityIndex": 133.8919648375057}, "security": {"risks": [], "riskLevel": "MINIMAL"}}, "recommendations": ["检测到控制流扁平化，建议使用控制流去混淆工具", "检测到大量字符串混淆，建议使用字符串解密工具", "检测到大量混淆变量，建议进行变量重命名", "代码复杂度过高，建议进行重构"]}