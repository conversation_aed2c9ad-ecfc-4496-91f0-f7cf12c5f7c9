#!/usr/bin/env node

const fs = require('fs');
const JSDeobfuscator = require('./deobfuscator');
const CodeAnalyzer = require('./analyzer');

// 测试用例
const testCases = [
    {
        name: '简单混淆测试',
        code: `
            var a = "SGVsbG8gV29ybGQ="; // Base64: Hello World
            var b = function(x) { return x + 1; };
            var c = [1, 2, 3];
            if (false) {
                console.log("dead code");
            }
            console.log(atob(a));
        `
    },
    {
        name: '控制流扁平化测试',
        code: `
            function test() {
                var state = 0;
                while (true) {
                    switch (state) {
                        case 0:
                            console.log("step 1");
                            state = 1;
                            break;
                        case 1:
                            console.log("step 2");
                            state = 2;
                            break;
                        case 2:
                            return "done";
                    }
                }
            }
        `
    },
    {
        name: '字符串数组测试',
        code: `
            var strings = ["hello", "world", "test"];
            function getString(index) {
                return strings[index];
            }
            console.log(getString(0) + " " + getString(1));
        `
    }
];

class TestRunner {
    constructor() {
        this.results = [];
    }

    // 运行所有测试
    runAllTests() {
        console.log('🧪 开始运行测试...\n');
        
        for (let i = 0; i < testCases.length; i++) {
            const testCase = testCases[i];
            console.log(`📋 测试 ${i + 1}: ${testCase.name}`);
            
            const result = this.runSingleTest(testCase);
            this.results.push(result);
            
            console.log(`✅ 测试完成\n`);
        }

        this.generateTestReport();
    }

    // 运行单个测试
    runSingleTest(testCase) {
        const startTime = Date.now();
        
        try {
            // 分析原始代码
            console.log('  🔍 分析原始代码...');
            const analyzer = new CodeAnalyzer(testCase.code);
            const originalAnalysis = analyzer.analyze();
            
            // 去混淆
            console.log('  🔧 执行去混淆...');
            const deobfuscator = new JSDeobfuscator(testCase.code);
            const deobfuscatedCode = deobfuscator.deobfuscate();
            
            // 分析去混淆后的代码
            console.log('  📊 分析去混淆后的代码...');
            const deobfuscatedAnalyzer = new CodeAnalyzer(deobfuscatedCode);
            const deobfuscatedAnalysis = deobfuscatedAnalyzer.analyze();
            
            const endTime = Date.now();
            
            return {
                name: testCase.name,
                success: true,
                executionTime: endTime - startTime,
                originalCode: testCase.code,
                deobfuscatedCode,
                originalAnalysis,
                deobfuscatedAnalysis,
                improvements: this.calculateImprovements(originalAnalysis, deobfuscatedAnalysis)
            };
            
        } catch (error) {
            console.log(`  ❌ 测试失败: ${error.message}`);
            
            return {
                name: testCase.name,
                success: false,
                error: error.message,
                executionTime: Date.now() - startTime
            };
        }
    }

    // 计算改进情况
    calculateImprovements(original, deobfuscated) {
        const improvements = {};
        
        // 复杂度改进
        if (original.complexity && deobfuscated.complexity) {
            improvements.complexityReduction = original.complexity.cyclomatic - deobfuscated.complexity.cyclomatic;
            improvements.maintainabilityImprovement = deobfuscated.complexity.maintainabilityIndex - original.complexity.maintainabilityIndex;
        }
        
        // 变量改进
        if (original.variables && deobfuscated.variables) {
            const originalObfuscated = original.variables.filter(v => v.isObfuscated).length;
            const deobfuscatedObfuscated = deobfuscated.variables.filter(v => v.isObfuscated).length;
            improvements.variableImprovement = originalObfuscated - deobfuscatedObfuscated;
        }
        
        // 安全风险改进
        if (original.security && deobfuscated.security) {
            improvements.securityRiskReduction = original.security.risks.length - deobfuscated.security.risks.length;
        }
        
        // 代码大小变化
        improvements.sizeChange = deobfuscated.overview.fileSize - original.overview.fileSize;
        
        return improvements;
    }

    // 生成测试报告
    generateTestReport() {
        console.log('📋 生成测试报告...\n');
        
        const report = {
            timestamp: new Date().toISOString(),
            totalTests: this.results.length,
            successfulTests: this.results.filter(r => r.success).length,
            failedTests: this.results.filter(r => !r.success).length,
            averageExecutionTime: this.results.reduce((sum, r) => sum + r.executionTime, 0) / this.results.length,
            results: this.results
        };

        // 保存详细报告
        fs.writeFileSync('test-report.json', JSON.stringify(report, null, 2));
        
        // 显示摘要
        console.log('📊 测试摘要:');
        console.log(`  - 总测试数: ${report.totalTests}`);
        console.log(`  - 成功: ${report.successfulTests}`);
        console.log(`  - 失败: ${report.failedTests}`);
        console.log(`  - 平均执行时间: ${report.averageExecutionTime.toFixed(2)}ms`);
        
        // 显示改进统计
        this.showImprovementStats();
        
        console.log('\n✅ 测试报告已保存到: test-report.json');
    }

    // 显示改进统计
    showImprovementStats() {
        console.log('\n💡 改进统计:');
        
        const successfulResults = this.results.filter(r => r.success && r.improvements);
        
        if (successfulResults.length === 0) {
            console.log('  - 无改进数据');
            return;
        }

        const totalComplexityReduction = successfulResults.reduce((sum, r) => 
            sum + (r.improvements.complexityReduction || 0), 0);
        
        const totalVariableImprovement = successfulResults.reduce((sum, r) => 
            sum + (r.improvements.variableImprovement || 0), 0);
        
        const totalSecurityImprovement = successfulResults.reduce((sum, r) => 
            sum + (r.improvements.securityRiskReduction || 0), 0);

        console.log(`  - 复杂度降低: ${totalComplexityReduction}`);
        console.log(`  - 变量去混淆: ${totalVariableImprovement}`);
        console.log(`  - 安全风险降低: ${totalSecurityImprovement}`);
    }
}

// 测试真实文件
function testRealFile() {
    console.log('🔍 测试真实文件: proxy.js\n');
    
    if (!fs.existsSync('proxy.js')) {
        console.log('❌ proxy.js 文件不存在，跳过真实文件测试');
        return;
    }

    try {
        const sourceCode = fs.readFileSync('proxy.js', 'utf8');
        
        console.log('📊 分析原始文件...');
        const analyzer = new CodeAnalyzer(sourceCode);
        const analysis = analyzer.analyze();
        const report = analyzer.generateReport();
        
        console.log('\n🔧 执行去混淆...');
        const deobfuscator = new JSDeobfuscator(sourceCode);
        const deobfuscatedCode = deobfuscator.deobfuscate();
        
        if (deobfuscatedCode) {
            fs.writeFileSync('test-deobfuscated.js', deobfuscatedCode);
            console.log('✅ 去混淆结果已保存到: test-deobfuscated.js');
        }
        
        // 保存分析报告
        fs.writeFileSync('test-analysis.json', JSON.stringify(report, null, 2));
        console.log('✅ 分析报告已保存到: test-analysis.json');
        
    } catch (error) {
        console.log(`❌ 真实文件测试失败: ${error.message}`);
    }
}

// 主函数
function main() {
    console.log('🚀 JavaScript去混淆工具测试套件\n');
    
    // 运行基础测试
    const testRunner = new TestRunner();
    testRunner.runAllTests();
    
    // 测试真实文件
    testRealFile();
    
    console.log('\n🎉 所有测试完成！');
}

// 如果直接运行此文件
if (require.main === module) {
    main();
}

module.exports = { TestRunner, testCases };
