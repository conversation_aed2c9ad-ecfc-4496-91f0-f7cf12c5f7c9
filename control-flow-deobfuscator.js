const traverse = require('@babel/traverse').default;
const t = require('@babel/types');

class ControlFlowDeobfuscator {
    constructor(ast) {
        this.ast = ast;
        this.switchCases = new Map();
        this.controlVariables = new Set();
        this.flattenedFunctions = new Set();
    }

    // 检测控制流扁平化模式
    detectControlFlowFlattening() {
        console.log('\n🌀 检测控制流扁平化...');
        
        const patterns = {
            switchBasedFlattening: 0,
            whileLoopFlattening: 0,
            stringArrayFlattening: 0
        };

        traverse(this.ast, {
            WhileStatement: (path) => {
                // 检测while(true) + switch模式
                if (this.isInfiniteLoop(path.node) && this.containsSwitchStatement(path.node.body)) {
                    patterns.whileLoopFlattening++;
                    this.analyzeFlattenedFunction(path);
                    console.log('  🔍 发现while+switch扁平化模式');
                }
            },

            SwitchStatement: (path) => {
                // 检测大型switch语句
                if (path.node.cases.length > 10) {
                    patterns.switchBasedFlattening++;
                    console.log(`  🔍 发现大型switch语句 (${path.node.cases.length} cases)`);
                }
            },

            ArrayExpression: (path) => {
                // 检测字符串数组模式
                if (this.isStringArray(path.node)) {
                    patterns.stringArrayFlattening++;
                    console.log('  🔍 发现字符串数组模式');
                }
            }
        });

        console.log(`📊 扁平化检测结果:`);
        console.log(`  - While循环扁平化: ${patterns.whileLoopFlattening}`);
        console.log(`  - Switch扁平化: ${patterns.switchBasedFlattening}`);
        console.log(`  - 字符串数组: ${patterns.stringArrayFlattening}`);

        return patterns;
    }

    // 检测无限循环
    isInfiniteLoop(whileNode) {
        const test = whileNode.test;
        return (
            t.isBooleanLiteral(test, { value: true }) ||
            t.isNumericLiteral(test, { value: 1 }) ||
            (t.isUnaryExpression(test) && test.operator === '!' && 
             t.isNumericLiteral(test.argument, { value: 0 }))
        );
    }

    // 检测是否包含switch语句
    containsSwitchStatement(blockStatement) {
        if (!t.isBlockStatement(blockStatement)) return false;
        
        return blockStatement.body.some(stmt => 
            t.isSwitchStatement(stmt) || 
            (t.isBlockStatement(stmt) && this.containsSwitchStatement(stmt))
        );
    }

    // 检测字符串数组
    isStringArray(arrayNode) {
        if (arrayNode.elements.length < 5) return false;
        
        const stringElements = arrayNode.elements.filter(el => 
            t.isStringLiteral(el) && el.value.length > 3
        );
        
        return stringElements.length / arrayNode.elements.length > 0.8;
    }

    // 分析扁平化函数
    analyzeFlattenedFunction(whilePath) {
        const functionPath = whilePath.getFunctionParent();
        if (!functionPath) return;

        console.log('  📋 分析扁平化函数结构...');
        
        // 查找控制变量
        this.findControlVariables(whilePath);
        
        // 分析switch cases
        this.analyzeSwitchCases(whilePath);
        
        this.flattenedFunctions.add(functionPath);
    }

    // 查找控制变量
    findControlVariables(whilePath) {
        whilePath.traverse({
            AssignmentExpression: (path) => {
                if (t.isIdentifier(path.node.left)) {
                    this.controlVariables.add(path.node.left.name);
                }
            },
            UpdateExpression: (path) => {
                if (t.isIdentifier(path.node.argument)) {
                    this.controlVariables.add(path.node.argument.name);
                }
            }
        });
    }

    // 分析switch cases
    analyzeSwitchCases(whilePath) {
        whilePath.traverse({
            SwitchStatement: (path) => {
                const discriminant = path.node.discriminant;
                let controlVar = null;

                if (t.isIdentifier(discriminant)) {
                    controlVar = discriminant.name;
                } else if (t.isMemberExpression(discriminant)) {
                    // 处理 array[index] 形式
                    controlVar = this.extractControlVariable(discriminant);
                }

                if (controlVar) {
                    this.switchCases.set(controlVar, path.node.cases);
                    console.log(`    🎯 控制变量: ${controlVar}, Cases: ${path.node.cases.length}`);
                }
            }
        });
    }

    // 提取控制变量
    extractControlVariable(memberExpr) {
        if (t.isIdentifier(memberExpr.object) && t.isIdentifier(memberExpr.property)) {
            return `${memberExpr.object.name}[${memberExpr.property.name}]`;
        }
        return null;
    }

    // 去扁平化处理
    deobfuscateControlFlow() {
        console.log('\n🔧 开始控制流去扁平化...');
        
        let deobfuscatedCount = 0;

        // 处理每个扁平化函数
        for (const functionPath of this.flattenedFunctions) {
            if (this.reconstructControlFlow(functionPath)) {
                deobfuscatedCount++;
            }
        }

        console.log(`✅ 控制流去扁平化完成，处理了 ${deobfuscatedCount} 个函数`);
        return deobfuscatedCount;
    }

    // 重构控制流
    reconstructControlFlow(functionPath) {
        console.log('  🔨 重构函数控制流...');
        
        try {
            // 查找while循环
            let whileLoop = null;
            functionPath.traverse({
                WhileStatement: (path) => {
                    if (this.isInfiniteLoop(path.node)) {
                        whileLoop = path;
                        path.stop();
                    }
                }
            });

            if (!whileLoop) return false;

            // 提取switch语句
            const switchStatement = this.extractSwitchFromWhile(whileLoop);
            if (!switchStatement) return false;

            // 分析执行流程
            const executionFlow = this.analyzeExecutionFlow(switchStatement);
            if (!executionFlow) return false;

            // 重建线性代码
            const linearCode = this.buildLinearCode(executionFlow);
            if (!linearCode) return false;

            // 替换原始代码
            whileLoop.replaceWithMultiple(linearCode);
            
            console.log('    ✅ 函数控制流重构成功');
            return true;

        } catch (error) {
            console.log(`    ❌ 函数控制流重构失败: ${error.message}`);
            return false;
        }
    }

    // 从while循环中提取switch语句
    extractSwitchFromWhile(whilePath) {
        let switchStmt = null;

        whilePath.traverse({
            SwitchStatement: (path) => {
                switchStmt = path.node;
                path.stop();
            }
        });

        return switchStmt;
    }

    // 分析执行流程
    analyzeExecutionFlow(switchStatement) {
        const cases = switchStatement.cases;
        const flow = new Map();
        
        for (const caseNode of cases) {
            if (!caseNode.test) continue; // default case
            
            const caseValue = this.getCaseValue(caseNode.test);
            if (caseValue === null) continue;
            
            const nextCase = this.findNextCase(caseNode);
            flow.set(caseValue, {
                statements: caseNode.consequent,
                next: nextCase
            });
        }

        return flow;
    }

    // 获取case值
    getCaseValue(testNode) {
        if (t.isStringLiteral(testNode)) {
            return testNode.value;
        } else if (t.isNumericLiteral(testNode)) {
            return testNode.value;
        }
        return null;
    }

    // 查找下一个case
    findNextCase(caseNode) {
        const statements = caseNode.consequent;
        
        for (const stmt of statements) {
            if (t.isAssignmentExpression(stmt.expression)) {
                const right = stmt.expression.right;
                if (t.isStringLiteral(right) || t.isNumericLiteral(right)) {
                    return right.value;
                }
            }
        }
        
        return null;
    }

    // 构建线性代码
    buildLinearCode(executionFlow) {
        const linearStatements = [];
        const visited = new Set();
        
        // 从第一个case开始
        let currentCase = this.findFirstCase(executionFlow);
        
        while (currentCase && !visited.has(currentCase)) {
            visited.add(currentCase);
            
            const caseInfo = executionFlow.get(currentCase);
            if (!caseInfo) break;
            
            // 添加当前case的语句（除了控制流语句）
            const filteredStatements = this.filterControlFlowStatements(caseInfo.statements);
            linearStatements.push(...filteredStatements);
            
            currentCase = caseInfo.next;
        }
        
        return linearStatements;
    }

    // 查找第一个case
    findFirstCase(executionFlow) {
        // 简单启发式：选择第一个数字最小的case
        const cases = Array.from(executionFlow.keys());
        return cases.sort((a, b) => {
            if (typeof a === 'number' && typeof b === 'number') {
                return a - b;
            }
            return String(a).localeCompare(String(b));
        })[0];
    }

    // 过滤控制流语句
    filterControlFlowStatements(statements) {
        return statements.filter(stmt => {
            // 移除break语句
            if (t.isBreakStatement(stmt)) return false;
            
            // 移除continue语句
            if (t.isContinueStatement(stmt)) return false;
            
            // 移除控制变量赋值
            if (t.isExpressionStatement(stmt) && t.isAssignmentExpression(stmt.expression)) {
                const left = stmt.expression.left;
                if (t.isIdentifier(left) && this.controlVariables.has(left.name)) {
                    return false;
                }
            }
            
            return true;
        });
    }

    // 清理控制变量
    cleanupControlVariables() {
        console.log('\n🧹 清理控制变量...');
        
        let cleanedCount = 0;
        
        traverse(this.ast, {
            VariableDeclarator: (path) => {
                if (t.isIdentifier(path.node.id) && 
                    this.controlVariables.has(path.node.id.name)) {
                    
                    // 检查变量是否只用于控制流
                    if (this.isOnlyUsedForControlFlow(path.node.id.name)) {
                        path.remove();
                        cleanedCount++;
                    }
                }
            }
        });

        console.log(`✅ 清理了 ${cleanedCount} 个控制变量`);
        return cleanedCount;
    }

    // 检查变量是否只用于控制流
    isOnlyUsedForControlFlow(varName) {
        let usageCount = 0;
        let controlFlowUsage = 0;
        
        traverse(this.ast, {
            Identifier: (path) => {
                if (path.node.name === varName && path.isReferencedIdentifier()) {
                    usageCount++;
                    
                    // 检查是否在控制流上下文中使用
                    const parent = path.parent;
                    if (t.isAssignmentExpression(parent) || 
                        t.isUpdateExpression(parent) ||
                        t.isSwitchStatement(path.parentPath.parent)) {
                        controlFlowUsage++;
                    }
                }
            }
        });
        
        return usageCount > 0 && controlFlowUsage === usageCount;
    }

    // 执行完整的控制流去混淆
    process() {
        this.detectControlFlowFlattening();
        const deobfuscatedFunctions = this.deobfuscateControlFlow();
        const cleanedVariables = this.cleanupControlVariables();
        
        return {
            deobfuscatedFunctions,
            cleanedVariables,
            totalControlVariables: this.controlVariables.size
        };
    }
}

module.exports = ControlFlowDeobfuscator;
